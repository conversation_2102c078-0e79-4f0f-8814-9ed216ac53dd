# Copyright 2023 99cloud
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from __future__ import annotations

import os
import tempfile
import aiohttp
import json
import asyncio
import socket
import datetime
import logging
import traceback
from typing import Any, List, Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, Query, status, Response
from fastapi.responses import FileResponse
from sqlalchemy import select, and_, insert, update
from sqlalchemy.sql import text
from sqlalchemy.sql import text

from skyline_apiserver import schemas
from skyline_apiserver.api import deps
from skyline_apiserver.db import api as db_api
from skyline_apiserver.db.models import (
    SysSafeGuestCheckInfo,
    SysSafeHealthCheckSettings,
    SysSafeCloudCheckInfo,
    SysSafeHostCheckInfo
)
from skyline_apiserver.config import CONF
from skyline_apiserver.log import LOG
from skyline_apiserver.utils.tools import fast_check_packet_loss
from skyline_apiserver.utils.roles import assert_system_admin
from skyline_apiserver.schemas.security import CategoryCode
from skyline_apiserver.client.openstack import neutron, nova, system, cinder
from skyline_apiserver.client.utils import generate_session, get_system_session
from skyline_apiserver.core.memcached import MemcachedClient
from skyline_apiserver.core.security import generate_profile
from skyline_apiserver.api.wrapper.skyline import Port
from skyline_apiserver.types import constants
from fastapi import APIRouter, Depends, HTTPException, status, Header


# 可选导入（用于Excel导出功能）
try:
    import openpyxl
    from openpyxl.styles import Alignment, Font, PatternFill, Border, Side
    from openpyxl.utils import get_column_letter
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False

router = APIRouter()


# 全局MemcachedClient实例
_memcached_client = None


def get_memcached_client() -> MemcachedClient:
    """获取MemcachedClient实例（单例模式）"""
    global _memcached_client
    if _memcached_client is None:
        _memcached_client = MemcachedClient()
    return _memcached_client


async def get_health_platform_data(cache_expire: int = 300) -> tuple[int, str, dict]:
    """获取健康检查平台数据的公用函数

    通过HTTP请求获取{health_address}/platform接口的数据，并使用memcache缓存结果。

    Args:
        cache_expire (int): 缓存过期时间（秒），默认300秒（5分钟）

    Returns:
        tuple: (状态码, 错误信息, 数据字典)
            - 状态码: 0表示成功，-1表示失败
            - 错误信息: 失败时的错误描述
            - 数据字典: 成功时返回解析后的JSON数据
    """
    try:
        # 获取健康检查地址
        health_address = CONF.default.health_address
        if not health_address:
            LOG.error("健康检查地址未配置")
            return -1, "健康检查地址未配置", {}

        # 生成缓存键
        cache_key = f"health_platform_data:{health_address}"

        # 尝试从缓存获取数据
        memcached_client = get_memcached_client()
        cached_data = memcached_client.get(cache_key)

        if cached_data:
            try:
                # 缓存数据是字节类型，需要解码并解析JSON
                if isinstance(cached_data, bytes):
                    cached_data = cached_data.decode('utf-8')
                data = json.loads(cached_data)
                LOG.debug(f"从缓存获取平台数据成功，缓存键: {cache_key}")
                return 0, "", data
            except (json.JSONDecodeError, UnicodeDecodeError) as e:
                LOG.warning(f"缓存数据解析失败: {str(e)}，将重新获取数据")
                # 删除损坏的缓存数据
                memcached_client.delete(cache_key)

        LOG.debug(f"缓存中无数据，从API获取平台数据: {health_address}/platform")

        # 发送HTTP请求获取平台数据
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=150)) as session:
            async with session.get(f"{health_address}/platform") as response:
                LOG.debug(f"平台数据API响应状态码: {response.status}")

                if response.status != 200:
                    error_msg = f"获取平台数据失败，状态码: {response.status}"
                    LOG.error(error_msg)
                    return -1, error_msg, {}

                result = await response.text()
                LOG.debug(f"获取到平台数据，长度: {len(result) if result else 0}")

                # 检查响应是否包含错误状态
                if "state" in result:
                    LOG.error("平台数据包含错误状态")
                    return -1, "获取平台数据失败", {}

                # 解析JSON数据
                if result and "state" not in result:
                    try:
                        result_json = json.loads(result)

                        # 将数据存储到缓存
                        cache_success = memcached_client.set(cache_key, result, expire=cache_expire)
                        if cache_success:
                            LOG.debug(f"平台数据已缓存，过期时间: {cache_expire}秒")
                        else:
                            LOG.warning("平台数据缓存失败")

                        return 0, "", result_json

                    except json.JSONDecodeError as e:
                        error_msg = f"解析平台数据JSON失败: {str(e)}"
                        LOG.error(error_msg)
                        return -1, error_msg, {}
                else:
                    LOG.error("获取平台数据失败或数据格式错误")
                    return -1, "获取平台数据失败", {}

    except aiohttp.ClientError as e:
        error_msg = f"HTTP请求失败: {str(e)}"
        LOG.error(error_msg)
        return -1, error_msg, {}
    except Exception as e:
        error_msg = f"获取平台数据时出现未知错误: {str(e)}"
        LOG.error(error_msg, exc_info=True)
        return -1, error_msg, {}


async def get_health_os_data(cache_expire: int = 300) -> tuple[int, str, dict]:
    """获取健康检查OS数据的公用函数

    通过HTTP请求获取{health_address}/os接口的数据，并使用memcache缓存结果。

    Args:
        cache_expire (int): 缓存过期时间（秒），默认300秒（5分钟）

    Returns:
        tuple: (状态码, 错误信息, 数据字典)
            - 状态码: 0表示成功，-1表示失败
            - 错误信息: 失败时的错误描述
            - 数据字典: 成功时返回解析后的JSON数据
    """
    try:
        # 获取健康检查地址
        health_address = CONF.default.health_address
        if not health_address:
            LOG.error("健康检查地址未配置")
            return -1, "健康检查地址未配置", {}

        # 生成缓存键
        cache_key = f"health_os_data:{health_address}"

        # 尝试从缓存获取数据
        memcached_client = get_memcached_client()
        cached_data = memcached_client.get(cache_key)

        if cached_data:
            try:
                # 缓存数据是字节类型，需要解码并解析JSON
                if isinstance(cached_data, bytes):
                    cached_data = cached_data.decode('utf-8')
                data = json.loads(cached_data)
                LOG.debug(f"从缓存获取OS数据成功，缓存键: {cache_key}")
                return 0, "", data
            except (json.JSONDecodeError, UnicodeDecodeError) as e:
                LOG.warning(f"缓存数据解析失败: {str(e)}，将重新获取数据")
                # 删除损坏的缓存数据
                memcached_client.delete(cache_key)

        LOG.debug(f"缓存中无数据，从API获取OS数据: {health_address}/os")

        # 发送HTTP请求获取OS数据
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=150)) as session:
            async with session.get(f"{health_address}/os") as response:
                LOG.debug(f"OS数据API响应状态码: {response.status}")

                if response.status != 200:
                    error_msg = f"获取OS数据失败，状态码: {response.status}"
                    LOG.error(error_msg)
                    return -1, error_msg, {}

                result = await response.text()
                LOG.debug(f"获取到OS数据，长度: {len(result) if result else 0}")

                # 解析JSON数据
                if result:
                    try:
                        result_json = json.loads(result)

                        # 将数据存储到缓存
                        cache_success = memcached_client.set(cache_key, result, expire=cache_expire)
                        if cache_success:
                            LOG.debug(f"OS数据已缓存，过期时间: {cache_expire}秒")
                        else:
                            LOG.warning("OS数据缓存失败")

                        return 0, "", result_json

                    except json.JSONDecodeError as e:
                        error_msg = f"解析OS数据JSON失败: {str(e)}"
                        LOG.error(error_msg)
                        return -1, error_msg, {}
                else:
                    LOG.error("获取OS数据失败或数据为空")
                    return -1, "获取OS数据失败", {}

    except aiohttp.ClientError as e:
        error_msg = f"HTTP请求失败: {str(e)}"
        LOG.error(error_msg)
        return -1, error_msg, {}
    except Exception as e:
        error_msg = f"获取OS数据时出现未知错误: {str(e)}"
        LOG.error(error_msg, exc_info=True)
        return -1, error_msg, {}


async def _save_health_check_result(history_id: int, category_id: int, result: int, info: str):
    """保存健康检查结果到数据库"""
    try:
        # 使用现有的数据库API函数保存结果
        await db_api.save_health_check_result(
            history_id=history_id,
            category_id=category_id,
            result=result,
            detail=info  # 注意：数据库字段名是detail，不是info
        )

        LOG.debug(f"保存健康检查结果成功: history_id={history_id}, category_id={category_id}, result={result}")

    except Exception as e:
        LOG.error(f"保存健康检查结果失败: {str(e)}", exc_info=True)


async def _ceph_auth(base_url: str, username: str, password: str) -> tuple:
    """
    Ceph认证函数，返回认证成功的URL和token

    Args:
        base_url: Ceph基础URL，可能包含多个URL用逗号分隔
        username: 用户名
        password: 密码

    Returns:
        (认证成功的URL, token)，失败返回(None, None)
    """
    try:
        import aiohttp
        import json

        # 构建认证请求体
        auth_body = {
            "username": username,
            "password": password
        }

        # 处理多个URL的情况（用逗号分隔）
        urls = base_url.split(",") if base_url else []

        if not base_url or not username or not password:
            LOG.error("Ceph认证参数不完整")
            return None, None

        async with aiohttp.ClientSession() as session:
            for url in urls:
                url = url.strip()
                auth_url = f"{url}/auth"

                headers = {
                    "Content-Type": "application/json",
                    "Accept": "application/vnd.ceph.api.v1.0+json"
                }

                try:
                    LOG.debug(f"尝试Ceph认证: {auth_url}")
                    async with session.post(
                        auth_url,
                        json=auth_body,
                        headers=headers,
                        timeout=aiohttp.ClientTimeout(total=30), 
                        ssl=False,
                    ) as response:
                        result = await response.text()

                        # 检查是否是重定向响应
                        if "This resource can be found at" in result:
                            LOG.debug(f"URL {url} 返回重定向，尝试下一个")
                            continue

                        if response.status == 201:
                            try:
                                result_json = json.loads(result)
                                token = result_json.get("token")
                                if token:
                                    LOG.debug(f"Ceph认证成功: {url}, token: {token[:20]}...")
                                    return url, token
                            except json.JSONDecodeError:
                                LOG.warning(f"无法解析认证响应: {url}")
                                continue
                        else:
                            LOG.warning(f"认证失败，状态码: {response.status}, URL: {url}")

                except aiohttp.ClientError as e:
                    LOG.warning(f"认证请求失败: {url}, 错误: {str(e)}")
                    continue
                except Exception as e:
                    LOG.warning(f"认证过程出错: {url}, 错误: {str(e)}")
                    continue

        LOG.error("所有Ceph URL认证都失败")
        return None, None

    except Exception as e:
        LOG.error(f"Ceph认证函数出错: {str(e)}", exc_info=True)
        return None, None


@router.post(
    "/security/check-health",
    description="执行健康巡检",
    responses={
        200: {"model": schemas.Message},
        401: {"model": schemas.UnauthorizedMessage},
        403: {"model": schemas.ForbiddenMessage},
        404: {"model": schemas.NotFoundMessage},
    },
    status_code=status.HTTP_200_OK,
    response_description="OK",
)
async def check_health(
    history_id: int,
    category_id: int,
    category_code: str,
    profile: schemas.Profile = Depends(deps.get_profile_update_jwt),
    x_openstack_request_id: str = Header(
        "",
        alias=constants.INBOUND_HEADER,
        regex=constants.INBOUND_HEADER_REGEX,
    ),
) -> Any:
    """
    执行健康巡检
    """
    assert_system_admin(profile=profile, exception="Not allowed to perform health check.")
    
    if history_id is None or category_code is None:
        return {"code": 500, "msg": "History ID and Category Code are required."}
    
    # 根据不同的类别执行不同的检查逻辑
    result = 0  # 默认正常
    detail = "Check passed"
    
    if category_code == CategoryCode.SAFE_CVE:
        # CVE漏洞检测 # 暂不实现 跳过
        result, detail = await check_cve(history_id, category_id)
    elif category_code == CategoryCode.NETWORK_PACKAGE_LOSS:
        # 主机丢包检测 每个host节点 #已实现
        result, detail = await check_packet_loss(history_id, category_id)
    elif category_code == CategoryCode.CEPH_POOL_SIZE:
        # 存储池容量检测 # java版未实现，已废弃，这里也不实现
        result, detail = await check_ceph_pool_size(history_id, category_id)
    elif category_code == CategoryCode.CEPH_POOL:
        # 存储池状态检测 #已实现
        result, detail = await check_ceph_pool(history_id, category_id)
    elif category_code == CategoryCode.CEPH_OBJECT:
        # 存储对象数 #已实现
        result, detail = await check_ceph_object(history_id, category_id)
    elif category_code == CategoryCode.CEPH_PG:
        # 存储PG状态 #已实现
        result, detail = await check_ceph_pg(history_id, category_id)
    elif category_code == CategoryCode.CEPH_OSD:
        # 存储OSD状态 #已实现
        result, detail = await check_ceph_osd(history_id, category_id)
    elif category_code == CategoryCode.CEPH_STATUS:
        # 存储集群状态 #已实现
        result, detail = await check_ceph_status(history_id, category_id)
    elif category_code == CategoryCode.HOSTOS_DISK_USED_PERCENT:
        # HostOS分区利用率检测 #已实现
        result, detail = await check_hostos_disk_used_percent(history_id, category_id)
    elif category_code == CategoryCode.HOSTOS_SHARE_CPU_USED_PERCENT:
        # HostOS CPU分配率检测 # java版中功能已废弃，这里也不实现
        result, detail = await check_hostos_share_cpu_used_percent(history_id, category_id)
    elif category_code == CategoryCode.HOSTOS_SHARE_MEMORY_USED_PERCENT:
        # HostOS内存分配率检测 # java版中功能已废弃，这里也不实现
        result, detail = await check_hostos_share_memory_used_percent(history_id, category_id)
    elif category_code == CategoryCode.HOSTOS_CPU_USED_PERCENT:
        # HostOS CPU利用率检测 #已实现
        result, detail = await check_hostos_cpu_used_percent(history_id, category_id)
    elif category_code == CategoryCode.HOSTOS_MEMORY_USED_PERCENT:
        # HostOS内存利用率检测 #已实现
        result, detail = await check_hostos_memory_used_percent(history_id, category_id)
    elif category_code == CategoryCode.HOSTOS_WARN_EVENT:
        # HostOS告警事件检测 #已实现
        result, detail = await check_hostos_warn_event(history_id, category_id)
    elif category_code == CategoryCode.HOSTOS_NETWORK_STATUS:
        # 物理网卡连通状态检测 #已实现
        result, detail = await check_hostos_network_status(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_NTP:
        # 时钟同步检测 # java版中功能已废弃，这里也不实现
        result, detail = await check_cloud_ntp(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_WARN:
        # 平台告警信息检测 # java版中功能已废弃，这里也不实现
        result, detail = await check_cloud_warn(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_STATUS:
        # 平台服务状态检测 # java版中功能已废弃，这里也不实现
        result, detail = await check_cloud_status(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_VIRTUAL_HA_STATUS:
        # 虚拟主机HA检测 # java版中功能已废弃，这里也不实现
        result, detail = await check_cloud_virtual_ha_status(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_DB_STATUS:
        # 平台数据库状态检测 #已实现
        result, detail = await check_cloud_db_status(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_NOVA:
        # Nova服务 #已实现
        result, detail = await check_cloud_nova(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_CINDER:
        # Cinder服务 #已实现
        result, detail = await check_cloud_cinder(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_KEYSTONE:
        # Keystone服务 #已实现
        result, detail = await check_cloud_keystone(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_NEUTRON_SERVER:
        # NeutronServer服务 #已实现
        result, detail = await check_cloud_neutron_server(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_GLANCE:
        # Glance服务 #已实现
        result, detail = await check_cloud_glance(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_NEUTRON_DHCP_AGENT:
        # NeutronDHCP服务 #已实现
        result, detail = await check_cloud_neutron_dhcp(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_KEEPALVED:
        # Keepalived服务 #已实现
        result, detail = await check_cloud_keepalived(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_NEUTRON_OPENVSWITCH_AGENT:
        # NeutronOpenvswitcha服务 #已实现
        result, detail = await check_cloud_neutron_openvswitch(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_NOVA_COMPUTE:
        # NovaCompute服务 #已实现
        result, detail = await check_cloud_nova_compute(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_CINDER_VOLUME:
        # CinderVolume服务 #已实现
        result, detail = await check_cloud_cinder_volume(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_NEUTRON:
        # Neutron服务 #已实现
        result, detail = await check_cloud_neutron(history_id, category_id)
    elif category_code == CategoryCode.CLOUD_HA_STATUS:
        # 平台HA状态检测 # 服务不正常 TODO
        result, detail = await check_cloud_ha_status(history_id, category_id)
    elif category_code == CategoryCode.GUESTOS_VOLUME:
        # 卷状态 #已实现
        result, detail = await check_guestos_volume(history_id, category_id, x_openstack_request_id, profile)
    elif category_code == CategoryCode.GUESTOS_MEMORY_USED_PERCENT:
        # GuestOS内存利用率检测 #已实现
        result, detail = await check_guestos_memory_used_percent(history_id, category_id, x_openstack_request_id, profile)
    elif category_code == CategoryCode.GUESTOS_CPU_USED_PERCENT:
        # GuestOS CPU利用率检测 #已实现
        result, detail = await check_guestos_cpu_used_percent(history_id, category_id, x_openstack_request_id, profile)
    elif category_code == CategoryCode.GUESTOS_DISK_USED_PERCENT:
        # GuestOS分区利用率检测 # java版未实现，已废弃，这里也不实现
        result, detail = await check_guestos_disk_used_percent(history_id, category_id)
    elif category_code == CategoryCode.NETWORK_STATUS:
        # 连通性检测 #已实现
        result, detail = await check_network_status(history_id, category_id)
    elif category_code == CategoryCode.NETWORK_ROUTE:
        # 虚拟路由状态 #已实现
        result, detail = await check_network_route(history_id, category_id)
    elif category_code == CategoryCode.NETWORK_GATEWAY:
        # 虚拟网关状态 #已实现
        result, detail = await check_network_gateway(history_id, category_id)
    elif category_code == CategoryCode.NETWORK_FLOATIP:
        # 浮动IP状态 # java版未实现，已废弃，这里也不实现
        result, detail = await check_network_floatip(history_id, category_id)
    elif category_code == CategoryCode.NETWORK_PORT:
        # 端口状态 #已实现
        result, detail = await check_network_port(history_id, category_id)
    elif category_code == CategoryCode.NETWORK_HTTP:
        # 基于响应内容的健康检查 #已实现
        result, detail = await check_network_http(history_id, category_id)
    
    # 保存检查结果到数据库
    await _save_health_check_result(history_id, category_id, result, detail)
    
    return {"data": result}


# 各种健康检查函数
async def check_cve(history_id: int, category_id: int):
    """CVE漏洞检测"""
    try:
        
        # 获取健康检查地址
        health_address = CONF.default.health_address
        
        # 发送HTTP请求获取CVE漏洞检测数据
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{health_address}/security/cve") as response:
                if response.status != 200:
                    return -1, "获取CVE漏洞检测数据失败"
                
                result = await response.text()
                
                # 如果结果包含"state"，表示请求失败
                if "state" in result:
                    return -1, "获取CVE漏洞检测数据失败"
                
                # 解析结果
                result_json = json.loads(result)
                
                # 检查CVE漏洞状态
                cve_status = result_json.get("status")
                cve_list = result_json.get("cve_list", [])
                
                if cve_status != "OK" or len(cve_list) > 0:
                    # 如果存在CVE漏洞，返回异常状态
                    return -1, f"系统存在{len(cve_list)}个CVE漏洞，详细信息请查看巡检报告。"
                else:
                    # 没有CVE漏洞
                    return 0, "系统未发现CVE漏洞"
    except Exception as e:
        return -1, f"检查CVE漏洞时出错: {str(e)}"

async def check_packet_loss(history_id: int, category_id: int):
    """丢包检测"""
    data = 0
    info = None

    try:
        LOG.info(f"开始检查主机丢包情况，history_id: {history_id}, category_id: {category_id}")

        # 获取数据库连接
        db = db_api.DB.get()

        # 从OpenStack API获取所有compute service，并进行主机IP去重，从而获取主机列表

        # 获取系统级别的session
        system_session = get_system_session()

        # 获取当前region（不遍历所有regions，只使用当前region）
        current_region = "RegionOne"  # 使用默认region，或从配置中获取
        LOG.debug(f"使用当前region: {current_region}")

        # 创建系统级别的profile用于调用OpenStack API
        # 使用系统管理员token
        system_token = system_session.get_token()

        hosts = []
        unique_host_ips = set()  # 用于去重

        try:
            LOG.debug(f"正在获取当前region: {current_region} 的hypervisors")

            # 创建系统级别的profile（只针对当前region）
            system_profile = await generate_profile(
                keystone_token=system_token,
                region=current_region
            )

            # 调用nova API获取hypervisors (使用 /v2.1/os-hypervisors API)
            hypervisors = await nova.list_hypervisors(
                profile=system_profile,
                session=system_session,
                global_request_id=""
            )

            LOG.debug(f"当前region {current_region} 获取到 {len(hypervisors)} 个hypervisors")

            # 提取主机IP并去重
            for hypervisor in hypervisors:
                try:
                    # hypervisor对象包含host_ip字段，这是真正的IP地址
                    host_ip = getattr(hypervisor, 'host_ip', None)
                    hostname = getattr(hypervisor, 'hypervisor_hostname', None)
                    state = getattr(hypervisor, 'state', None)
                    status = getattr(hypervisor, 'status', None)

                    LOG.debug(f"Hypervisor: {hostname} with IP {host_ip}, state: {state}, status: {status}")

                    # 只处理状态为up且enabled的hypervisor，并且主机IP不重复
                    if (host_ip and host_ip not in unique_host_ips):
                        unique_host_ips.add(host_ip)
                        hosts.append({
                            'ip': host_ip,
                            'hostname': hostname or host_ip,  # 优先使用hostname，否则使用IP
                            'region': current_region,
                            'hypervisor_type': 'compute'  # 标记为计算节点
                        })
                        LOG.debug(f"添加主机: {host_ip} (主机名: {hostname})")

                except Exception as e:
                    LOG.warning(f"处理hypervisor信息时出错: {str(e)}")
                    continue

        except Exception as e:
            LOG.error(f"获取当前region {current_region} 的hypervisors失败: {str(e)}", exc_info=True)
            # 不使用continue，因为只有一个region

        LOG.debug(f"获取到主机列表: {len(hosts)}台主机")

        error = 0
        warn = 0

        if hosts and len(hosts) > 0:
            # 存储丢包检查结果的列表
            packet_loss_check_list = []

            for host in hosts:
                # 创建丢包检查记录
                check_packet_loss_entity = {
                    'history_id': history_id,
                    'host_ip': host['ip'],
                }

                # 调用私有方法检查丢包情况
                result = await _check_packet_loss_for_host(host['ip'])

                if result > 0:
                    # 存在丢包 (丢包百分比大于0)
                    warn += 1
                    check_packet_loss_entity['state'] = "1"
                    LOG.warning(f"主机 {host['ip']} 存在丢包情况，丢包率: {result}%")
                elif result == -1:
                    # 主机检查异常
                    error += 1
                    check_packet_loss_entity['state'] = "-1"
                    LOG.error(f"主机 {host['ip']} 网络检查异常")
                else:
                    # result == 0，无丢包
                    check_packet_loss_entity['state'] = "0"
                    LOG.debug(f"主机 {host['ip']} 网络正常，无丢包")

                packet_loss_check_list.append(check_packet_loss_entity)

            # 批量保存检查结果到数据库
            if packet_loss_check_list:
                await _save_packet_loss_check_results(packet_loss_check_list)

        # 根据检查结果设置返回值，完全按照Java代码逻辑
        if error > 0:
            info = f"存在{error}台主机网络有异常情况"
            if warn > 0:
                info += f"，存在{warn}台主机网络有丢包情况"
            info += "，详细信息请查看巡检报告。"
            data = -1
        elif warn > 0:
            info = f"存在{warn}台主机网络有丢包情况，详细信息请查看巡检报告。"
            data = 1
        else:
            info = "所有主机网络连接正常"
            data = 0

        LOG.info(f"丢包检测完成，结果: data={data}, info={info}")
        return data, info

    except Exception as e:
        LOG.error(f"检查主机丢包情况时出错: {str(e)}", exc_info=True)
        return -1, f"丢包检测出错: {str(e)}"


async def _check_packet_loss_for_host(host_ip: str, port: str = "8774") -> int:
    """
    检查网络丢包情况
    使用fast_check_packet_loss函数通过ping命令检查到目标主机的丢包率

    Args:
        host_ip: 主机IP地址
        port: 端口号，默认8774 (保留参数以保持接口兼容性)

    Returns:
        int: 返回丢包百分比：0表示无丢包，大于0表示丢包百分比；-1表示检查失败
    """
    try:
        LOG.debug(f"开始检查主机 {host_ip} 的丢包情况")

        # 使用fast_check_packet_loss函数进行丢包检测
        # 发送10个数据包进行测试，快速且准确
        result = await asyncio.get_event_loop().run_in_executor(
            None, fast_check_packet_loss, host_ip, 5
        )

        # 检查结果
        if "error" in result:
            LOG.warning(f"主机 {host_ip} 丢包检测失败: {result['error']}")
            return -1

        # 获取丢包百分比
        packet_loss_percent = result.get("packet_loss_percent", -1)

        LOG.debug(f"主机 {host_ip} 丢包检测结果: {packet_loss_percent}% 丢包")
        LOG.debug(f"详细信息: 发送 {result.get('packets_sent', 0)} 个包")

        return packet_loss_percent

    except Exception as e:
        LOG.error(f"主机 {host_ip} 检查网络丢包失败: {str(e)}", exc_info=True)
        return -1


async def _save_packet_loss_check_results(packet_loss_check_list: list):
    """
    批量保存丢包检查结果到数据库

    Args:
        packet_loss_check_list: 丢包检查结果列表
    """
    try:
        from skyline_apiserver.db import api as db_api
        import datetime

        # 为每个检查结果添加创建时间
        for check_result in packet_loss_check_list:
            check_result['create_time'] = datetime.datetime.now()

        # 使用新的数据库API批量保存
        result = await db_api.batch_create_packet_loss_check_results(packet_loss_check_list)

        LOG.info(f"成功保存 {len(packet_loss_check_list)} 条丢包检查结果")
        return result

    except Exception as e:
        LOG.error(f"保存丢包检查结果失败: {str(e)}", exc_info=True)
        raise


async def _get_openstack_hosts():
    """
    从OpenStack API获取所有主机列表

    Returns:
        list: 主机列表，每个主机包含 {'hostname': str, 'ip': str, 'region': str, 'hypervisor_type': str}
    """
    try:
        from skyline_apiserver.client.utils import get_system_session
        from skyline_apiserver.client.openstack import nova, system
        from skyline_apiserver.core.security import generate_profile

        LOG.debug("开始从OpenStack API获取主机列表")

        # 获取系统级别的session
        system_session = get_system_session()

        # 获取当前region（不遍历所有regions，只使用当前region）
        current_region = "RegionOne"  # 使用默认region，或从配置中获取
        LOG.debug(f"使用当前region: {current_region}")

        # 创建系统级别的profile用于调用OpenStack API
        system_token = system_session.get_token()

        hosts = []
        unique_hostnames = set()  # 用于去重

        try:
            LOG.debug(f"正在获取当前region: {current_region} 的hypervisors")

            # 创建系统级别的profile（只针对当前region）
            system_profile = await generate_profile(
                keystone_token=system_token,
                region=current_region
            )

            # 调用nova API获取hypervisors
            hypervisors = await nova.list_hypervisors(
                profile=system_profile,
                session=system_session,
                global_request_id=""
            )

            LOG.debug(f"当前region {current_region} 获取到 {len(hypervisors)} 个hypervisors")

            # 提取主机hostname并去重
            for hypervisor in hypervisors:
                try:
                    # hypervisor对象包含hypervisor_hostname字段，这是唯一的主机名
                    host_ip = getattr(hypervisor, 'host_ip', None)
                    hostname = getattr(hypervisor, 'hypervisor_hostname', None)
                    state = getattr(hypervisor, 'state', None)
                    status = getattr(hypervisor, 'status', None)

                    LOG.debug(f"Hypervisor: {hostname} with IP {host_ip}, state: {state}, status: {status}")

                    if hostname and hostname not in unique_hostnames:
                        unique_hostnames.add(hostname)
                        hosts.append({
                            'hostname': hostname,
                            'ip': host_ip or hostname,  # 备用IP，如果没有IP则使用hostname
                            'region': current_region,
                            'hypervisor_type': 'compute'
                        })
                        LOG.debug(f"添加主机: {hostname} (IP: {host_ip})")

                except Exception as e:
                    LOG.warning(f"处理hypervisor信息时出错: {str(e)}")
                    continue

        except Exception as e:
            LOG.error(f"获取当前region {current_region} 的hypervisors失败: {str(e)}", exc_info=True)
            # 不使用continue，因为只有一个region

        LOG.info(f"从OpenStack API获取到 {len(hosts)} 个唯一主机")
        return hosts

    except Exception as e:
        LOG.error(f"获取OpenStack主机列表失败: {str(e)}", exc_info=True)
        raise

# java版未实现，已废弃，这里也不实现
async def check_ceph_pool_size(history_id: int, category_id: int):
    """存储池容量检测"""
    try:
        LOG.info(f"开始检查存储池容量，history_id: {history_id}, category_id: {category_id}")

        # 获取Ceph API配置
        base_url = CONF.default.ceph_address
        username = CONF.default.ceph_username
        password = CONF.default.ceph_password

        LOG.debug(f"Ceph API配置 - base_url: {base_url}, username: {username}")

        # 首先进行Ceph认证，获取可用的URL和token
        auth_url, token = await _ceph_auth(base_url, username, password)
        if not auth_url or not token:
            LOG.error("Ceph认证失败")
            return -1, "存储服务认证失败"

        LOG.debug(f"Ceph认证成功，使用URL: {auth_url}")

        # 构建API URL
        api_url = f"{auth_url}/health/minimal"

        # 发送HTTP请求获取数据
        async with aiohttp.ClientSession() as session:
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/vnd.ceph.api.v1.0+json",
                "Authorization": f"Bearer {token}"
            }

            LOG.debug(f"发送HTTP请求到: {api_url}")
            async with session.get(api_url, headers=headers, ssl=False) as response:
                LOG.debug(f"HTTP响应状态码: {response.status}")
                if response.status != 200:
                    LOG.error(f"存储服务连接失败，状态码: {response.status}")
                    return -1, "存储服务连接失败"

                result = await response.text()
                LOG.debug(f"获取到Ceph数据，长度: {len(result) if result else 0}")

                # 根据原Java代码，这个函数只是获取数据但没有具体的检查逻辑
                # 注释中提到"需要根据巡检设置存储池容量大小警告值比对后才可以做出是否异常判断"
                # 但实际代码中没有实现具体的检查逻辑，只是获取数据然后保存结果

                LOG.info("存储池容量检测完成（暂无具体检查逻辑）")
                return 0, None

    except json.JSONDecodeError as e:
        LOG.error(f"解析Ceph JSON数据失败: {str(e)}")
        return -1, f"解析存储服务数据失败: {str(e)}"
    except aiohttp.ClientError as e:
        LOG.error(f"Ceph HTTP请求失败: {str(e)}")
        return -1, f"存储服务网络请求失败: {str(e)}"
    except Exception as e:
        LOG.error(f"检查存储池容量时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查存储池容量时出错: {str(e)}"

async def check_ceph_pool(history_id: int, category_id: int):
    """存储池状态检测"""
    try:
        LOG.info(f"开始检查存储池状态，history_id: {history_id}, category_id: {category_id}")

        # 获取Ceph API配置
        base_url = CONF.default.ceph_address
        username = CONF.default.ceph_username
        password = CONF.default.ceph_password

        LOG.debug(f"Ceph API配置 - base_url: {base_url}, username: {username}")

        # 首先进行Ceph认证，获取可用的URL和token
        auth_url, token = await _ceph_auth(base_url, username, password)
        if not auth_url or not token:
            LOG.error("Ceph认证失败")
            return -1, "存储服务认证失败"

        LOG.debug(f"Ceph认证成功，使用URL: {auth_url}")

        # 构建API URL
        api_url = f"{auth_url}/health/minimal"

        # 发送HTTP请求获取数据
        async with aiohttp.ClientSession() as session:
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/vnd.ceph.api.v1.0+json",
                "Authorization": f"Bearer {token}"
            }

            LOG.debug(f"发送HTTP请求到: {api_url}")
            async with session.get(api_url, headers=headers, ssl=False) as response:
                LOG.debug(f"HTTP响应状态码: {response.status}")
                if response.status != 200:
                    LOG.error(f"存储服务连接失败，状态码: {response.status}")
                    return -1, "存储服务连接失败"

                result = await response.text()
                LOG.debug(f"获取到Ceph数据，长度: {len(result) if result else 0}")

                if result:
                    # 解析结果
                    result_json = json.loads(result)

                    # 检查是否有status字段，如果有则表示出错
                    if result_json.get("status") is not None:
                        LOG.error("Ceph API返回错误状态")
                        return -1, "存储服务连接失败"

                    # 获取pg_info对象
                    pg_info = result_json.get("pg_info")
                    if pg_info:
                        statuses = pg_info.get("statuses")
                        LOG.debug(f"PG状态信息: {statuses}")

                        if statuses:
                            if len(statuses) > 1:
                                # 如果状态数量大于1，表示存储池存在异常
                                LOG.warning(f"存储池状态异常，状态数量: {len(statuses)}")
                                return -1, "存储服务存储池存在异常，请及时排查"
                            else:
                                LOG.info("存储池状态正常")
                                return 0, None
                        else:
                            # 如果statuses为空或None，表示存储池存在异常
                            LOG.warning("存储池状态信息为空")
                            return -1, "存储服务存储池存在异常，请及时排查"
                    else:
                        LOG.warning("未获取到PG信息")
                        return 0, None
                else:
                    LOG.error("未获取到Ceph数据")
                    return -1, "存储服务连接失败"

    except json.JSONDecodeError as e:
        LOG.error(f"解析Ceph JSON数据失败: {str(e)}")
        return -1, f"解析存储服务数据失败: {str(e)}"
    except aiohttp.ClientError as e:
        LOG.error(f"Ceph HTTP请求失败: {str(e)}")
        return -1, f"存储服务网络请求失败: {str(e)}"
    except Exception as e:
        LOG.error(f"检查存储池状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查存储池状态时出错: {str(e)}"

async def check_ceph_object(history_id: int, category_id: int):
    """存储对象数检测"""
    try:
        LOG.info(f"开始检查存储对象状态，history_id: {history_id}, category_id: {category_id}")

        # 获取Ceph API配置
        base_url = CONF.default.ceph_address
        username = CONF.default.ceph_username
        password = CONF.default.ceph_password

        LOG.debug(f"Ceph API配置 - base_url: {base_url}, username: {username}")

        # 首先进行Ceph认证，获取可用的URL和token
        auth_url, token = await _ceph_auth(base_url, username, password)
        if not auth_url or not token:
            LOG.error("Ceph认证失败")
            return -1, "存储服务认证失败"

        LOG.debug(f"Ceph认证成功，使用URL: {auth_url}")

        # 构建API URL
        api_url = f"{auth_url}/health/minimal"

        # 发送HTTP请求获取数据
        async with aiohttp.ClientSession() as session:
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/vnd.ceph.api.v1.0+json",
                "Authorization": f"Bearer {token}"
            }

            LOG.debug(f"发送HTTP请求到: {api_url}")
            async with session.get(api_url, headers=headers, ssl=False) as response:
                LOG.debug(f"HTTP响应状态码: {response.status}")
                if response.status != 200:
                    LOG.error(f"存储服务连接失败，状态码: {response.status}")
                    return -1, "存储服务连接失败"

                result = await response.text()
                LOG.debug(f"获取到Ceph数据，长度: {len(result) if result else 0}")

                if result:
                    # 解析结果
                    result_json = json.loads(result)

                    # 检查是否有status字段，如果有则表示出错
                    if result_json.get("status") is None:
                        pg_info = result_json.get("pg_info")
                        if pg_info is not None:
                            object_stats = pg_info.get("object_stats")
                            if object_stats is not None:
                                error = False

                                # 检查degraded对象数
                                degraded = object_stats.get("num_objects_degraded")
                                if degraded is not None:
                                    if str(degraded) != "0":
                                        LOG.warning(f"发现degraded对象: {degraded}")
                                        return -1, "存储服务对象状态存在异常，请及时排查"

                                # 检查misplaced对象数
                                if not error:
                                    misplaced = object_stats.get("num_objects_misplaced")
                                    if misplaced is not None:
                                        if str(misplaced) != "0":
                                            LOG.warning(f"发现misplaced对象: {misplaced}")
                                            return -1, "存储服务对象状态存在异常，请及时排查"

                                # 检查unfound对象数
                                if not error:
                                    unfound = object_stats.get("num_objects_unfound")
                                    if unfound is not None:
                                        if str(unfound) != "0":
                                            LOG.warning(f"发现unfound对象: {unfound}")
                                            return -1, "存储服务对象状态存在异常，请及时排查"

                                LOG.info("存储对象状态正常")
                                return 0, None
                            else:
                                LOG.warning("未获取到对象统计信息")
                                return -1, "存储服务对象状态存在异常，请及时排查"
                        else:
                            LOG.debug("未获取到PG信息，认为正常")
                            return 0, None
                    else:
                        LOG.error("Ceph API返回错误状态")
                        return -1, "存储服务连接失败"
                else:
                    LOG.error("未获取到Ceph数据")
                    return -1, "存储服务连接失败"

    except json.JSONDecodeError as e:
        LOG.error(f"解析Ceph JSON数据失败: {str(e)}")
        return -1, f"解析存储服务数据失败: {str(e)}"
    except aiohttp.ClientError as e:
        LOG.error(f"Ceph HTTP请求失败: {str(e)}")
        return -1, f"存储服务网络请求失败: {str(e)}"
    except Exception as e:
        LOG.error(f"检查存储对象状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查存储对象状态时出错: {str(e)}"

async def check_ceph_pg(history_id: int, category_id: int):
    """存储PG状态检测"""
    try:
        LOG.info(f"开始检查存储PG状态，history_id: {history_id}, category_id: {category_id}")

        # 获取Ceph API配置
        base_url = CONF.default.ceph_address
        username = CONF.default.ceph_username
        password = CONF.default.ceph_password

        LOG.debug(f"Ceph API配置 - base_url: {base_url}, username: {username}")

        # 首先进行Ceph认证，获取可用的URL和token
        auth_url, token = await _ceph_auth(base_url, username, password)
        if not auth_url or not token:
            LOG.error("Ceph认证失败")
            return -1, "存储服务认证失败"

        LOG.debug(f"Ceph认证成功，使用URL: {auth_url}")

        # 构建API URL
        api_url = f"{auth_url}/health/minimal"

        # 发送HTTP请求获取数据
        async with aiohttp.ClientSession() as session:
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/vnd.ceph.api.v1.0+json",
                "Authorization": f"Bearer {token}"
            }

            LOG.debug(f"发送HTTP请求到: {api_url}")
            async with session.get(api_url, headers=headers, ssl=False) as response:
                LOG.debug(f"HTTP响应状态码: {response.status}")
                if response.status != 200:
                    LOG.error(f"存储服务连接失败，状态码: {response.status}")
                    return -1, "存储服务连接失败"

                result = await response.text()
                LOG.debug(f"获取到Ceph数据，长度: {len(result) if result else 0}")

                if result:
                    # 解析结果
                    result_json = json.loads(result)

                    # 检查是否有status字段，如果有则表示出错
                    if result_json.get("status") is None:
                        pg_info = result_json.get("pg_info")
                        if pg_info is not None:
                            # 获取statuses字典
                            statuses = pg_info.get("statuses")
                            LOG.debug(f"PG状态信息: {statuses}")

                            if statuses is not None and len(statuses) > 0:
                                if len(statuses) > 1:
                                    # 如果状态数量大于1，表示存储PG存在异常
                                    LOG.warning(f"存储PG状态异常，状态数量: {len(statuses)}")
                                    return -1, "存储服务PG状态存在异常，请及时排查"
                                else:
                                    LOG.info("存储PG状态正常")
                                    return 0, None
                            else:
                                # 如果statuses为空或None，表示存储PG存在异常
                                LOG.warning("存储PG状态信息为空")
                                return -1, "存储服务PG状态存在异常，请及时排查"
                        else:
                            LOG.debug("未获取到PG信息，认为正常")
                            return 0, None
                    else:
                        LOG.error("Ceph API返回错误状态")
                        return -1, "存储服务连接失败"
                else:
                    LOG.error("未获取到Ceph数据")
                    return -1, "存储服务连接失败"

    except json.JSONDecodeError as e:
        LOG.error(f"解析Ceph JSON数据失败: {str(e)}")
        return -1, f"解析存储服务数据失败: {str(e)}"
    except aiohttp.ClientError as e:
        LOG.error(f"Ceph HTTP请求失败: {str(e)}")
        return -1, f"存储服务网络请求失败: {str(e)}"
    except Exception as e:
        LOG.error(f"检查存储PG状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查存储PG状态时出错: {str(e)}"

async def check_ceph_osd(history_id: int, category_id: int):
    """存储OSD状态检测"""
    try:
        LOG.info(f"开始检查存储OSD状态，history_id: {history_id}, category_id: {category_id}")

        # 获取Ceph API配置
        base_url = CONF.default.ceph_address
        username = CONF.default.ceph_username
        password = CONF.default.ceph_password

        LOG.debug(f"Ceph API配置 - base_url: {base_url}, username: {username}")

        # 首先进行Ceph认证，获取可用的URL和token
        auth_url, token = await _ceph_auth(base_url, username, password)
        if not auth_url or not token:
            LOG.error("Ceph认证失败")
            return -1, "存储服务认证失败"

        LOG.debug(f"Ceph认证成功，使用URL: {auth_url}")

        # 构建API URL
        api_url = f"{auth_url}/health/minimal"

        # 发送HTTP请求获取数据
        async with aiohttp.ClientSession() as session:
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/vnd.ceph.api.v1.0+json",
                "Authorization": f"Bearer {token}"
            }

            LOG.debug(f"发送HTTP请求到: {api_url}")
            async with session.get(api_url, headers=headers, ssl=False) as response:
                LOG.debug(f"HTTP响应状态码: {response.status}")
                if response.status != 200:
                    LOG.error(f"存储服务连接失败，状态码: {response.status}")
                    return -1, "存储服务连接失败"

                result = await response.text()
                LOG.debug(f"获取到Ceph数据，长度: {len(result) if result else 0}")

                if result:
                    # 解析结果
                    result_json = json.loads(result)

                    # 检查是否有status字段，如果有则表示出错
                    if result_json.get("status") is None:
                        osd_object = result_json.get("osd_map")
                        if osd_object is not None:
                            osds = osd_object.get("osds")
                            LOG.debug(f"OSD列表信息: {osds}")

                            if osds is not None and len(osds) > 0:
                                for i in range(len(osds)):
                                    osd_item = osds[i]
                                    in_status = osd_item.get("in")
                                    up_status = osd_item.get("up")

                                    if in_status is not None and up_status is not None:
                                        # 检查in和up状态是否都为1
                                        if not (str(in_status) == "1" and str(up_status) == "1"):
                                            LOG.warning(f"发现异常OSD: index={i}, in={in_status}, up={up_status}")
                                            return -1, "存储OSD服务存在异常，请及时排查"
                                    else:
                                        LOG.warning(f"OSD状态信息不完整: index={i}, in={in_status}, up={up_status}")
                                        return -1, "存储OSD服务存在异常，请及时排查"

                                LOG.info("存储OSD状态正常")
                                return 0, None
                            else:
                                LOG.warning("未获取到OSD列表信息")
                                return -1, "存储OSD服务存在异常，请及时排查"
                        else:
                            LOG.debug("未获取到OSD映射信息，认为正常")
                            return 0, None
                    else:
                        LOG.error("Ceph API返回错误状态")
                        return -1, "存储服务连接失败"
                else:
                    LOG.error("未获取到Ceph数据")
                    return -1, "存储服务连接失败"

    except json.JSONDecodeError as e:
        LOG.error(f"解析Ceph JSON数据失败: {str(e)}")
        return -1, f"解析存储服务数据失败: {str(e)}"
    except aiohttp.ClientError as e:
        LOG.error(f"Ceph HTTP请求失败: {str(e)}")
        return -1, f"存储服务网络请求失败: {str(e)}"
    except Exception as e:
        LOG.error(f"检查存储OSD状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查存储OSD状态时出错: {str(e)}"

async def check_ceph_status(history_id: int, category_id: int):
    """存储集群状态检测"""
    try:
        LOG.info(f"开始检查存储集群状态，history_id: {history_id}, category_id: {category_id}")

        # 获取Ceph API配置
        base_url = CONF.default.ceph_address
        username = CONF.default.ceph_username
        password = CONF.default.ceph_password

        LOG.debug(f"Ceph API配置 - base_url: {base_url}, username: {username}")

        # 首先进行Ceph认证，获取可用的URL和token
        auth_url, token = await _ceph_auth(base_url, username, password)
        if not auth_url or not token:
            LOG.error("Ceph认证失败")
            return -1, "存储服务认证失败"

        LOG.debug(f"Ceph认证成功，使用URL: {auth_url}")

        # 构建API URL
        api_url = f"{auth_url}/health/minimal"

        # 发送HTTP请求获取数据
        async with aiohttp.ClientSession() as session:
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/vnd.ceph.api.v1.0+json",
                "Authorization": f"Bearer {token}"
            }

            LOG.debug(f"发送HTTP请求到: {api_url}")
            async with session.get(api_url, headers=headers, ssl=False) as response:
                LOG.debug(f"HTTP响应状态码: {response.status}")
                if response.status != 200:
                    LOG.error(f"存储服务连接失败，状态码: {response.status}")
                    return -1, "存储服务连接失败"

                result = await response.text()
                LOG.debug(f"获取到Ceph数据，长度: {len(result) if result else 0}")

                if result:
                    # 解析结果
                    result_json = json.loads(result)

                    # 检查是否有status字段，如果有则表示出错
                    if result_json.get("status") is not None:
                        LOG.error("Ceph API返回错误状态")
                        return -1, "存储服务连接失败"

                    # 获取health对象
                    health_object = result_json.get("health")
                    if health_object:
                        health_status = health_object.get("status")
                        LOG.debug(f"Ceph集群健康状态: {health_status}")

                        if health_status:
                            data = 0
                            info = None

                            if health_status != "HEALTH_OK":
                                if health_status == "HEALTH_WARN":
                                    data = 1
                                elif health_status == "HEALTH_ERROR":
                                    data = -1

                                # 获取检查详情
                                checks = health_object.get("checks")
                                if checks:
                                    check_messages = []
                                    if isinstance(checks, list):
                                        for check in checks:
                                            if isinstance(check, dict) and check.get("summary"):
                                                message = check.get("summary", {}).get("message")
                                                if message:
                                                    check_messages.append(message)

                                    if check_messages:
                                        info = ";".join(check_messages)
                                        LOG.warning(f"Ceph集群检查发现问题: {info}")
                            else:
                                LOG.info("Ceph集群状态正常")

                            return data, info

                    LOG.warning("未获取到Ceph集群健康信息")
                    return 0, None
                else:
                    LOG.error("未获取到Ceph数据")
                    return -1, "存储服务连接失败"

    except json.JSONDecodeError as e:
        LOG.error(f"解析Ceph JSON数据失败: {str(e)}")
        return -1, f"解析存储服务数据失败: {str(e)}"
    except aiohttp.ClientError as e:
        LOG.error(f"Ceph HTTP请求失败: {str(e)}")
        return -1, f"存储服务网络请求失败: {str(e)}"
    except Exception as e:
        LOG.error(f"检查存储集群状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查存储集群状态时出错: {str(e)}"

async def check_hostos_disk_used_percent(history_id: int, category_id: int):
    """HostOS分区利用率检测 HOSTOS_DISK_USED_PERCENT"""
    data = 0
    info = None
    error = 0
    warn = 0

    try:
        import aiohttp
        import json
        import os
        import aiofiles
        from skyline_apiserver.db import api as db_api
        from skyline_apiserver.config import CONF

        LOG.info(f"开始执行HostOS分区利用率检测，history_id: {history_id}, category_id: {category_id}")

        # 获取监控阈值设置（移到循环外，避免重复查询）
        db = db_api.DB.get()
        settings_query = select(SysSafeHealthCheckSettings).where(
            and_(
                SysSafeHealthCheckSettings.c.category_code == 'HOSTOS_DISK_USED_PERCENT',
                SysSafeHealthCheckSettings.c.deleted == 1
            )
        )

        settings_entity_list = []
        async with db.transaction():
            settings_result = await db.fetch_all(settings_query)
            if settings_result:
                settings_entity_list = [dict(s) for s in settings_result]

        # 设置阈值
        max_threshold = None
        min_threshold = None

        if settings_entity_list and len(settings_entity_list) > 0:
            max_threshold = settings_entity_list[0].get('max')
            min_threshold = settings_entity_list[0].get('min')
        else:
            # 使用默认阈值
            max_threshold = 90.0  # 默认最大阈值90%
            LOG.debug(f"使用默认磁盘使用率阈值: {max_threshold}%")

        # 使用公用函数获取OS数据（带缓存）
        status_code, error_msg, os_data = await get_health_os_data()
        if status_code != 0:
            LOG.error(f"获取OS数据失败: {error_msg}")
            return -1, error_msg

        # 解析OS数据
        result = json.dumps(os_data) if os_data else None

        if result and result.strip() and "state" not in result:
                # 从OpenStack API获取所有compute service，并进行主机IP去重，从而获取主机列表
                from skyline_apiserver.client.utils import get_system_session
                from skyline_apiserver.client.openstack import nova, system
                from skyline_apiserver.core.security import generate_profile

                # 获取系统级别的session
                system_session = get_system_session()

                # 获取当前region（不遍历所有regions，只使用当前region）
                current_region = "RegionOne"  # 使用默认region，或从配置中获取
                LOG.debug(f"使用当前region: {current_region}")

                # 创建系统级别的profile用于调用OpenStack API
                system_token = system_session.get_token()

                hosts = []
                unique_hostnames = set()  # 用于去重

                try:
                    LOG.debug(f"正在获取当前region: {current_region} 的hypervisors")

                    # 创建系统级别的profile（只针对当前region）
                    system_profile = await generate_profile(
                        keystone_token=system_token,
                        region=current_region
                    )

                    # 调用nova API获取hypervisors
                    hypervisors = await nova.list_hypervisors(
                        profile=system_profile,
                        session=system_session,
                        global_request_id=""
                    )

                    LOG.debug(f"当前region {current_region} 获取到 {len(hypervisors)} 个hypervisors")

                    # 提取主机hostname并去重
                    for hypervisor in hypervisors:
                        try:
                            # hypervisor对象包含hypervisor_hostname字段，这是唯一的主机名
                            host_ip = getattr(hypervisor, 'host_ip', None)
                            hostname = getattr(hypervisor, 'hypervisor_hostname', None)
                            state = getattr(hypervisor, 'state', None)
                            status = getattr(hypervisor, 'status', None)

                            LOG.debug(f"Hypervisor: {hostname} with IP {host_ip}, state: {state}, status: {status}")

                            if hostname and hostname not in unique_hostnames:
                                unique_hostnames.add(hostname)
                                hosts.append({
                                    'hostname': hostname,
                                    'ip': host_ip or hostname,  # 备用IP，如果没有IP则使用hostname
                                    'region': current_region,
                                    'hypervisor_type': 'compute'
                                })
                                LOG.debug(f"添加主机: {hostname} (IP: {host_ip})")

                        except Exception as e:
                            LOG.warning(f"处理hypervisor信息时出错: {str(e)}")
                            continue

                except Exception as e:
                    LOG.error(f"获取当前region {current_region} 的hypervisors失败: {str(e)}", exc_info=True)
                    # 不使用continue，因为只有一个region

                LOG.info(f"从OpenStack API获取到 {len(hosts)} 个唯一主机")

                # 将hosts转换为与原代码兼容的格式
                host_entity_list = hosts

                if host_entity_list and len(host_entity_list) > 0:
                    try:
                        os_data = json.loads(result)
                    except json.JSONDecodeError as e:
                        LOG.error(f"解析OS数据JSON失败: {str(e)}")
                        return -1, f"解析OS数据JSON失败: {str(e)}"

                    for host_entity in host_entity_list:
                        hostname = host_entity.get('hostname')
                        host_ip = host_entity.get('ip')  # 保留IP作为备用
                        if not hostname:
                            continue

                        # 首先尝试使用hostname查找OS数据，如果找不到则尝试使用IP
                        host_object = os_data.get(hostname)
                        if not host_object and host_ip:
                            host_object = os_data.get(host_ip)

                        if host_object:
                            # 继续解析
                            disk_object = host_object.get('disk')
                            if disk_object and str(disk_object).strip():
                                try:
                                    if isinstance(disk_object, str):
                                        disk_list = json.loads(disk_object)
                                    else:
                                        disk_list = disk_object

                                    used_percent = "0%"
                                    for disk_info in disk_list:
                                        mount = disk_info.get('mount')
                                        if mount == "/":
                                            size_used_utilization = disk_info.get('size_usedutilization')
                                            if size_used_utilization and str(size_used_utilization).strip():
                                                used_percent = str(size_used_utilization)
                                                break

                                    if used_percent != "0%":
                                        # 保存主机检查信息
                                        host_check_info_entity = {
                                            'history_id': history_id,
                                            'host_ip': hostname  # 使用hostname作为主机标识
                                        }

                                        # 与阈值进行比较
                                        try:
                                            percent = float(used_percent.replace('%', ''))

                                            # 判断状态
                                            disk_state = 0  # 默认正常
                                            if max_threshold is not None:
                                                if percent > max_threshold:
                                                    error += 1
                                                    disk_state = -1  # 异常
                                                    LOG.warning(f"主机 {host_ip} 磁盘使用率 {percent}% 超过异常阈值 {max_threshold}%")
                                                elif min_threshold is not None and percent > min_threshold:
                                                    warn += 1
                                                    disk_state = 1  # 警告
                                                    LOG.warning(f"主机 {host_ip} 磁盘使用率 {percent}% 超过警告阈值 {min_threshold}%")
                                                else:
                                                    LOG.debug(f"主机 {host_ip} 磁盘使用率 {percent}% 正常")

                                            host_check_info_entity['disk_percent'] = percent
                                            host_check_info_entity['disk_state'] = disk_state

                                            # 保存主机检查信息记录（与Java逻辑一致）
                                            from skyline_apiserver import schemas

                                            host_check_info = schemas.SysSafeHostCheckInfoCreate(
                                                history_id=history_id,
                                                host_ip=hostname,  # 使用hostname作为主机标识
                                                disk_percent=percent,
                                                disk_state=disk_state
                                            )

                                            # 保存或更新记录
                                            await db_api.save_or_update_host_check_info(history_id, hostname, host_check_info)
                                            LOG.debug(f"保存主机 {hostname} 磁盘检查记录: 使用率={percent}%, 状态={disk_state}")

                                        except ValueError as e:
                                            LOG.error(f"解析磁盘使用率失败: {used_percent}, 错误: {str(e)}")
                                            continue
                                        except Exception as e:
                                            LOG.error(f"保存主机 {hostname} 检查记录时出现异常: {str(e)}", exc_info=True)
                                            continue

                                except (json.JSONDecodeError, TypeError) as e:
                                    LOG.error(f"解析主机 {host_ip} 磁盘数据失败: {str(e)}")
                                    continue

        # 根据检查结果设置返回值
        if error > 0:
            data = -1
            info = f"主机分区检测时，存在{error}台主机磁盘使用率处于异常状态"
            if warn > 0:
                info += f";存在{warn}台主机磁盘使用率处于警告状态"
        elif warn > 0:
            data = 1
            info = f"主机分区检测时，存在{warn}台主机磁盘使用率处于警告状态"

        if info:
            info += "，详细信息请查看巡检报告。"

        LOG.info(f"HostOS分区利用率检测完成，结果: {data}, 信息: {info}")
        return data, info

    except Exception as e:
        data = -1
        info = str(e)
        LOG.error(f"HostOS分区利用率检测时出现异常: {str(e)}", exc_info=True)
        return data, info

async def check_hostos_share_cpu_used_percent(history_id: int, category_id: int):
    """HostOS CPU分配率检测 HOSTOS_SHARE_CPU_USED_PERCENT"""
    data = 0
    info = None

    try:
        LOG.info(f"开始执行HostOS CPU分配率检测，history_id: {history_id}, category_id: {category_id}")

        # 根据Java代码，这个函数目前是空实现，只保存结果
        # Java代码中只是简单地保存一个默认的正常状态

        LOG.info(f"HostOS CPU分配率检测完成，结果: {data}, 信息: {info}")
        return data, info

    except Exception as e:
        data = -1
        info = str(e)
        LOG.error(f"HostOS CPU分配率检测时出现异常: {str(e)}", exc_info=True)
        return data, info

async def get_host_list_from_openstack():
    """从OpenStack API获取主机列表的公用函数"""
    try:
        from skyline_apiserver.client.openstack import system
        from skyline_apiserver.client.openstack import nova
        from skyline_apiserver.core.security import generate_profile

        # 获取系统级别的session
        system_session = get_system_session()

        # 获取当前region（不遍历所有regions，只使用当前region）
        current_region = "RegionOne"  # 使用默认region，或从配置中获取
        LOG.debug(f"使用当前region: {current_region}")

        # 创建系统级别的profile用于调用OpenStack API
        system_token = system_session.get_token()

        hosts = []
        unique_hostnames = set()  # 用于去重

        try:
            LOG.debug(f"正在获取当前region: {current_region} 的hypervisors")

            # 创建系统级别的profile（只针对当前region）
            system_profile = await generate_profile(
                keystone_token=system_token,
                region=current_region
            )

            # 调用nova API获取hypervisors
            hypervisors = await nova.list_hypervisors(
                profile=system_profile,
                session=system_session,
                global_request_id=""
            )

            LOG.debug(f"当前region {current_region} 获取到 {len(hypervisors)} 个hypervisors")

            # 提取主机hostname并去重
            for hypervisor in hypervisors:
                try:
                    # hypervisor对象包含hypervisor_hostname字段，这是唯一的主机名
                    host_ip = getattr(hypervisor, 'host_ip', None)
                    hostname = getattr(hypervisor, 'hypervisor_hostname', None)
                    state = getattr(hypervisor, 'state', None)
                    status = getattr(hypervisor, 'status', None)

                    LOG.debug(f"Hypervisor: {hostname} with IP {host_ip}, state: {state}, status: {status}")

                    if hostname and hostname not in unique_hostnames:
                        unique_hostnames.add(hostname)
                        hosts.append({
                            'hostname': hostname,
                            'ip': host_ip or hostname,  # 备用IP，如果没有IP则使用hostname
                            'region': current_region,
                            'hypervisor_type': 'compute'
                        })
                        LOG.debug(f"添加主机: {hostname} (IP: {host_ip})")

                except Exception as e:
                    LOG.warning(f"处理hypervisor信息时出错: {str(e)}")
                    continue

        except Exception as e:
            LOG.error(f"获取当前region {current_region} 的hypervisors失败: {str(e)}", exc_info=True)
            # 不使用continue，因为只有一个region

        LOG.info(f"从OpenStack API获取到 {len(hosts)} 个唯一主机")
        return hosts

    except Exception as e:
        LOG.error(f"获取主机列表时出现异常: {str(e)}", exc_info=True)
        return []


async def check_hostos_share_memory_used_percent(history_id: int, category_id: int):
    """HostOS共享内存利用率检测"""
    try:
        # 获取监控阈值设置
        db = db_api.DB.get()
        
        # 构建SQL查询，类似于Java代码中的MPJLambdaWrapper查询
        settings_query = select(SysSafeHealthCheckSettings).where(
            and_(
                SysSafeHealthCheckSettings.c.category_code == 'HOSTOS_SHARE_MEMORY_USED_PERCENT',
                SysSafeHealthCheckSettings.c.deleted == 1
            )
        )
        
        # 执行查询获取设置
        settings = []
        async with db.transaction():
            result = await db.fetch_all(settings_query)
            if result:
                settings = [dict(s) for s in result]
        
        # 如果没有配置监控规则，直接返回正常状态
        if not settings:
            return 0, "未配置主机共享内存利用率监控规则"
        
        # 使用公用函数获取OS数据（带缓存）
        status_code, error_msg, os_data = await get_health_os_data()
        if status_code != 0:
            LOG.error(f"获取OS数据失败: {error_msg}")
            return -1, error_msg

        # 获取主机概览信息
        host_overview = os_data.get("host_overview")
        if not host_overview:
            LOG.warning("未获取到主机概览信息")
            return 0, "未获取到主机概览信息"

        # 获取主机数据并进行检查
        error_count = 0
        warn_count = 0

        LOG.debug(f"开始检查 {len(host_overview)} 个主机的共享内存使用率")

        # 遍历主机数据，检查共享内存使用率
        for host_ip, host_data in host_overview.items():
            try:
                if host_data:
                    # 获取共享内存使用率
                    share_memory_used_percent = host_data.get("share_memory_used_percent")

                    if share_memory_used_percent is not None:
                        try:
                            # 移除百分号并转换为浮点数
                            if isinstance(share_memory_used_percent, str):
                                share_memory_used_percent = float(share_memory_used_percent.replace('%', ''))

                            # 检查是否超过阈值
                            for setting in settings:
                                max_threshold = setting.get('max', 90.0)
                                min_threshold = setting.get('min', 80.0)

                                if share_memory_used_percent > max_threshold:
                                    error_count += 1
                                    LOG.warning(f"主机 {host_ip} 共享内存使用率 {share_memory_used_percent}% 超过错误阈值 {max_threshold}%")
                                    break
                                elif share_memory_used_percent > min_threshold:
                                    warn_count += 1
                                    LOG.warning(f"主机 {host_ip} 共享内存使用率 {share_memory_used_percent}% 超过警告阈值 {min_threshold}%")
                                    break
                                else:
                                    LOG.debug(f"主机 {host_ip} 共享内存使用率 {share_memory_used_percent}% 正常")
                        except ValueError as e:
                            LOG.warning(f"解析主机 {host_ip} 共享内存使用率失败: {share_memory_used_percent}, 错误: {str(e)}")
                            continue
                    else:
                        LOG.debug(f"主机 {host_ip} 没有共享内存使用率数据")
                else:
                    LOG.warning(f"主机 {host_ip} 数据为空")
            except Exception as e:
                LOG.warning(f"处理主机 {host_ip} 数据时出错: {str(e)}")
                continue

        # 根据错误和警告数量判断状态
        if error_count > 0:
            info = f"主机共享内存检测时，存在{error_count}台主机共享内存使用率超过阈值，详细信息请查看巡检报告。"
            LOG.warning(info)
            return -1, info
        elif warn_count > 0:
            info = f"主机共享内存检测时，存在{warn_count}台主机共享内存使用率处于警告状态，详细信息请查看巡检报告。"
            LOG.warning(info)
            return 1, info
        else:
            LOG.info("所有主机的共享内存利用率正常")
            return 0, "主机共享内存利用率正常"
    except Exception as e:
        return -1, f"检查主机共享内存利用率时出错: {str(e)}"

async def check_hostos_cpu_used_percent(history_id: int, category_id: int):
    """HostOS CPU利用率检测 HOSTOS_CPU_USED_PERCENT"""
    data = 0
    info = None
    error_count = 0
    warn_count = 0

    try:
        LOG.info(f"开始执行HostOS CPU利用率检测，history_id: {history_id}, category_id: {category_id}")

        # 获取监控阈值设置
        db = db_api.DB.get()

        # 查询监控阈值，与Java代码逻辑一致
        settings_query = select(SysSafeHealthCheckSettings).where(
            and_(
                SysSafeHealthCheckSettings.c.category_code == 'HOSTOS_CPU_USED_PERCENT',
                SysSafeHealthCheckSettings.c.deleted == 1
            )
        )

        settings = []
        async with db.transaction():
            result = await db.fetch_all(settings_query)
            if result:
                settings = [dict(s) for s in result]

        # 判断是否配置监控规则
        if not settings or len(settings) == 0:
            LOG.warning("未配置HostOS CPU利用率监控规则")
            return 0, "未配置HostOS CPU利用率监控规则"

        # 获取阈值设置
        min_threshold = None
        max_threshold = None
        for setting in settings:
            if setting.get('min') is not None:
                min_threshold = float(setting['min'])
            if setting.get('max') is not None:
                max_threshold = float(setting['max'])

        LOG.debug(f"CPU利用率阈值设置: 警告阈值={min_threshold}, 异常阈值={max_threshold}")

        # 使用公用函数获取OS数据（带缓存）
        status_code, error_msg, os_data = await get_health_os_data()
        if status_code != 0:
            LOG.error(f"获取OS数据失败: {error_msg}")
            return -1, error_msg

        # 使用公用函数获取主机列表
        try:
            hosts = await _get_openstack_hosts()
        except Exception as e:
            LOG.error(f"获取主机列表失败: {str(e)}")
            return -1, f"获取主机列表失败: {str(e)}"

        if not hosts:
            LOG.warning("未发现任何主机")
            return 0, "未发现任何主机"

        # 遍历主机，检查CPU使用率
        for host in hosts:
            hostname = host.get('hostname')

            # 首先尝试使用hostname查找OS数据，如果找不到则尝试使用IP
            host_object = os_data.get(hostname)
            if host_object:
                # 解析CPU数据
                cpu_object = host_object.get('cpu')
                if cpu_object and str(cpu_object).strip():
                    try:
                        # 获取CPU使用率
                        cpu_percent_str = cpu_object.get('cpu_usedutilization', '0%')
                        if cpu_percent_str and str(cpu_percent_str).strip():
                            # 与阈值进行比较
                            try:
                                percent = float(str(cpu_percent_str).replace('%', ''))

                                # 判断状态
                                cpu_state = 0  # 默认正常
                                if max_threshold is not None and percent > max_threshold:
                                    cpu_state = -1  # 异常
                                    error_count += 1
                                elif min_threshold is not None and percent > min_threshold:
                                    cpu_state = 1   # 警告
                                    warn_count += 1

                                # 保存主机检查信息记录（与Java逻辑一致）
                                from skyline_apiserver import schemas

                                host_check_info = schemas.SysSafeHostCheckInfoCreate(
                                    history_id=history_id,
                                    host_ip=hostname,  # 使用hostname作为主机标识
                                    cpu_percent=percent,
                                    cpu_state=cpu_state
                                )

                                # 保存或更新记录
                                await db_api.save_or_update_host_check_info(history_id, hostname, host_check_info)
                                LOG.debug(f"保存主机 {hostname} CPU检查记录: 使用率={percent}%, 状态={cpu_state}")

                            except ValueError as e:
                                LOG.error(f"解析CPU使用率失败: {cpu_percent_str}, 错误: {str(e)}")
                                continue
                            except Exception as e:
                                LOG.error(f"保存主机 {hostname} 检查记录时出现异常: {str(e)}", exc_info=True)
                                continue

                    except json.JSONDecodeError as e:
                        LOG.error(f"解析主机 {hostname} CPU数据失败: {str(e)}")
                        continue

        # 根据检查结果确定整体状态（与Java逻辑一致）
        if error_count > 0:
            data = -1
            info = f"HostOS主机CPU利用情况检测时，共有{error_count}台主机CPU使用率处于异常状态"
            if warn_count > 0:
                info += f"；共有{warn_count}台主机CPU使用率处于警告状态"
            info += "，详细信息请查看巡检报告。"
        elif warn_count > 0:
            data = 1
            info = f"HostOS主机CPU利用情况检测时，共有{warn_count}台主机CPU使用率处于警告状态，详细信息请查看巡检报告。"
        else:
            data = 0
            info = "主机CPU利用率正常"

        LOG.info(f"HostOS CPU利用率检测完成，结果: {data}, 信息: {info}")
        return data, info

    except Exception as e:
        data = -1
        info = str(e)
        LOG.error(f"HostOS CPU利用率检测时出现异常: {str(e)}", exc_info=True)
        return data, info

async def check_hostos_memory_used_percent(history_id: int, category_id: int):
    """HostOS内存利用率检测 HOSTOS_MEMORY_USED_PERCENT"""
    data = 0
    info = None
    error_count = 0
    warn_count = 0

    try:
        LOG.info(f"开始执行HostOS内存利用率检测，history_id: {history_id}, category_id: {category_id}")

        # 获取监控阈值设置
        db = db_api.DB.get()

        # 查询监控阈值，与Java代码逻辑一致
        settings_query = select(SysSafeHealthCheckSettings).where(
            and_(
                SysSafeHealthCheckSettings.c.category_code == 'HOSTOS_MEMORY_USED_PERCENT',
                SysSafeHealthCheckSettings.c.deleted == 1
            )
        )

        settings = []
        async with db.transaction():
            result = await db.fetch_all(settings_query)
            if result:
                settings = [dict(s) for s in result]

        # 判断是否配置监控规则
        if not settings or len(settings) == 0:
            LOG.warning("未配置HostOS内存利用率监控规则")
            return 0, "未配置HostOS内存利用率监控规则"

        # 获取阈值设置
        min_threshold = None
        max_threshold = None
        for setting in settings:
            if setting.get('min') is not None:
                min_threshold = float(setting['min'])
            if setting.get('max') is not None:
                max_threshold = float(setting['max'])

        LOG.debug(f"内存利用率阈值设置: 警告阈值={min_threshold}, 异常阈值={max_threshold}")

        # 使用公用函数获取OS数据（带缓存）
        status_code, error_msg, os_data = await get_health_os_data()
        if status_code != 0:
            LOG.error(f"获取OS数据失败: {error_msg}")
            return -1, error_msg

        # 使用公用函数获取主机列表
        try:
            hosts = await _get_openstack_hosts()
        except Exception as e:
            LOG.error(f"获取主机列表失败: {str(e)}")
            return -1, f"获取主机列表失败: {str(e)}"

        if not hosts:
            LOG.warning("未发现任何主机")
            return 0, "未发现任何主机"

        # 遍历主机，检查内存使用率
        for host in hosts:
            hostname = host.get('hostname')

            # 首先尝试使用hostname查找OS数据，如果找不到则尝试使用IP
            host_object = os_data.get(hostname)
            if host_object:
                # 解析内存数据
                memory_object = host_object.get('mem')
                if memory_object and str(memory_object).strip():
                    try:
                        # 获取内存使用率
                        memory_percent_str = memory_object.get('mem_usedutilization', '0%')
                        if memory_percent_str and str(memory_percent_str).strip():
                            # 与阈值进行比较
                            try:
                                percent = float(str(memory_percent_str).replace('%', ''))

                                # 判断状态
                                memory_state = 0  # 默认正常
                                if max_threshold is not None and percent > max_threshold:
                                    memory_state = -1  # 异常
                                    error_count += 1
                                elif min_threshold is not None and percent > min_threshold:
                                    memory_state = 1   # 警告
                                    warn_count += 1

                                # 保存主机检查信息记录（与Java逻辑一致）
                                from skyline_apiserver import schemas

                                host_check_info = schemas.SysSafeHostCheckInfoCreate(
                                    history_id=history_id,
                                    host_ip=hostname,  # 使用hostname作为主机标识
                                    memory_percent=percent,
                                    memory_state=memory_state
                                )

                                # 保存或更新记录
                                await db_api.save_or_update_host_check_info(history_id, hostname, host_check_info)
                                LOG.debug(f"保存主机 {hostname} 内存检查记录: 使用率={percent}%, 状态={memory_state}")

                            except ValueError as e:
                                LOG.error(f"解析内存使用率失败: {memory_percent_str}, 错误: {str(e)}")
                                continue
                            except Exception as e:
                                LOG.error(f"保存主机 {hostname} 检查记录时出现异常: {str(e)}", exc_info=True)
                                continue

                    except json.JSONDecodeError as e:
                        LOG.error(f"解析主机 {hostname} 内存数据失败: {str(e)}")
                        continue

        # 根据检查结果确定整体状态（与Java逻辑一致）
        if error_count > 0:
            data = -1
            info = f"HostOS主机内存利用情况检测时，共有{error_count}台主机内存使用率处于异常状态"
            if warn_count > 0:
                info += f"；共有{warn_count}台主机内存使用率处于警告状态"
            info += "，详细信息请查看巡检报告。"
        elif warn_count > 0:
            data = 1
            info = f"HostOS主机内存利用情况检测时，共有{warn_count}台主机内存使用率处于警告状态，详细信息请查看巡检报告。"
        else:
            data = 0
            info = "主机内存利用率正常"

        LOG.info(f"HostOS内存利用率检测完成，结果: {data}, 信息: {info}")
        return data, info

    except Exception as e:
        data = -1
        info = str(e)
        LOG.error(f"HostOS内存利用率检测时出现异常: {str(e)}", exc_info=True)
        return data, info

async def check_hostos_warn_event(history_id: int, category_id: int):
    """主机告警事件检测"""
    try:
        LOG.debug(f"开始执行主机告警事件检测，history_id: {history_id}, category_id: {category_id}")

        data = 0
        info = None
        warn = 0

        # 使用公用函数获取OS数据（带缓存）
        status_code, error_msg, os_data = await get_health_os_data()
        if status_code != 0:
            LOG.error(f"获取OS数据失败: {error_msg}")
            return -1, error_msg

        if os_data:
            # 使用_get_openstack_hosts()获取主机列表
            hosts = await _get_openstack_hosts()

            LOG.debug(f"查询到 {len(hosts)} 个主机")

            if hosts:
                for host in hosts:
                    hostname = host.get('hostname')
                    if hostname is None:
                        continue

                    try:
                        # 从OS数据中获取主机对象
                        host_object = os_data.get(hostname)

                        if host_object:
                            # 继续解析系统数据
                            system_object = host_object.get("system")
                            if system_object and str(system_object).strip():
                                # 获取告警数量
                                warn_num = system_object.get("warn_num")

                                # 保存主机检查信息记录（与Java逻辑一致）
                                from skyline_apiserver import schemas

                                warn_num_value = 0
                                print(f"告警数量: {warn_num}")
                                if warn_num is not None:
                                    try:
                                        warn_num_value = int(warn_num)
                                        if warn_num_value > 0:
                                            warn += 1
                                    except (ValueError, TypeError):
                                        warn_num_value = 0

                                host_check_info = schemas.SysSafeHostCheckInfoCreate(
                                    history_id=history_id,
                                    host_ip=hostname,
                                    warn_num=warn_num_value
                                )

                                # 保存或更新记录
                                await db_api.save_or_update_host_check_info(history_id, hostname, host_check_info)
                                LOG.debug(f"保存主机 {hostname} 告警检查记录: 告警数量={warn_num_value}")

                    except json.JSONDecodeError as e:
                        LOG.error(f"解析主机 {hostname} OS数据失败: {str(e)}")
                        continue
                    except Exception as e:
                        LOG.error(f"处理主机 {hostname} 告警事件时出现异常: {str(e)}", exc_info=True)
                        continue

        if warn > 0:
            data = 1
            info = f"主机告警检测时，共有{warn}台主机存在警告信息未处理，详细信息请查看巡检报告。"

        return data, info

    except Exception as e:
        LOG.error(f"主机告警事件检测时出现异常: {str(e)}", exc_info=True)
        return -1, str(e)

async def check_hostos_network_status(history_id: int, category_id: int):
    """主机网络状态检测"""
    try:
        LOG.debug(f"开始执行主机网络状态检测，history_id: {history_id}, category_id: {category_id}")

        data = 0
        info = None
        error = 0

        # 使用公用函数获取OS数据（带缓存）
        status_code, error_msg, os_data = await get_health_os_data()
        if status_code != 0:
            LOG.error(f"获取OS数据失败: {error_msg}")
            return -1, error_msg

        if os_data:
            # 使用_get_openstack_hosts()获取主机列表
            hosts = await _get_openstack_hosts()

            LOG.debug(f"查询到 {len(hosts)} 个主机")

            if hosts:
                for host in hosts:
                    hostname = host.get('hostname')
                    if not hostname:
                        continue

                    try:
                        # 从OS数据中获取主机对象
                        host_object = os_data.get(hostname)

                        if host_object:
                            # 继续解析网络数据
                            network_object = host_object.get("network")
                            if network_object and str(network_object).strip():
                                # 保存主机检查信息记录（与Java逻辑一致）
                                from skyline_apiserver import schemas

                                conn = network_object.get("conn")
                                network_state = 0  # 默认正常  0: 正常; -1: 异常

                                if conn is not None and str(conn).strip():
                                    if str(conn).lower() != "true":
                                        error += 1
                                        network_state = -1
                                    else:
                                        network_state = 0
                                else:
                                    network_state = 0

                                host_check_info = schemas.SysSafeHostCheckInfoCreate(
                                    history_id=history_id,
                                    host_ip=hostname,
                                    network_state=network_state
                                )

                                # 保存或更新记录
                                await db_api.save_or_update_host_check_info(history_id, hostname, host_check_info)
                                LOG.debug(f"保存主机 {hostname} 网络检查记录: 状态={network_state}")

                    except json.JSONDecodeError as e:
                        LOG.error(f"解析主机 {hostname} OS数据失败: {str(e)}")
                        continue
                    except Exception as e:
                        LOG.error(f"处理主机 {hostname} 网络状态时出现异常: {str(e)}", exc_info=True)
                        continue

        if error > 0:
            data = -1
            info = f"主机物理网卡检测时，共有{error}台主机有物理网卡不通问题，详细信息请查看巡检报告。"

        return data, info

    except Exception as e:
        LOG.error(f"主机网络状态检测时出现异常: {str(e)}", exc_info=True)
        return -1, str(e)

async def check_cloud_ntp(history_id: int, category_id: int):
    """云平台NTP检测"""
    try:
        # 获取健康检查地址
        health_address = CONF.default.health_address
        
        # 发送HTTP请求获取NTP状态数据
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{health_address}/cloud/ntp") as response:
                if response.status != 200:
                    return -1, "获取NTP状态数据失败"
                
                result = await response.text()
                
                # 如果结果包含"state"，表示请求失败
                if "state" in result:
                    return -1, "获取NTP状态数据失败"
                
                # 解析结果
                result_json = json.loads(result)
                
                # 检查NTP状态
                ntp_status = result_json.get("status")
                
                if ntp_status != "OK":
                    # 如果NTP状态不正常，返回异常状态
                    return -1, "云平台NTP服务异常，详细信息请查看巡检报告。"
                else:
                    # NTP状态正常
                    return 0, "云平台NTP服务正常"
    except Exception as e:
        return -1, f"检查云平台NTP服务时出错: {str(e)}"

async def check_cloud_warn(history_id: int, category_id: int):
    """云平台告警检测"""
    # 根据Java代码逻辑，这是一个空实现，只返回默认的正常状态
    try:
        LOG.debug(f"执行云平台告警检测，history_id: {history_id}, category_id: {category_id}")

        # 与Java代码保持一致，返回默认的正常状态
        data = 0
        info = None

        return data, info

    except Exception as e:
        LOG.error(f"云平台告警检测时出现异常: {str(e)}", exc_info=True)
        return -1, str(e)

async def check_cloud_status(history_id: int, category_id: int):
    """云平台状态检测"""
    try:
        # 获取健康检查地址
        health_address = CONF.default.health_address
        
        # 发送HTTP请求获取云平台状态数据
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{health_address}/cloud/status") as response:
                if response.status != 200:
                    return -1, "获取云平台状态数据失败"
                
                result = await response.text()
                
                # 如果结果包含"state"，表示请求失败
                if "state" in result:
                    return -1, "获取云平台状态数据失败"
                
                # 解析结果
                result_json = json.loads(result)
                
                # 检查云平台状态
                cloud_status = result_json.get("status")
                
                if cloud_status != "OK":
                    # 如果云平台状态不正常，返回异常状态
                    return -1, "云平台状态异常，详细信息请查看巡检报告。"
                else:
                    # 云平台状态正常
                    return 0, "云平台状态正常"
    except Exception as e:
        return -1, f"检查云平台状态时出错: {str(e)}"

async def check_cloud_virtual_ha_status(history_id: int, category_id: int):
    """云平台虚拟HA状态检测"""
    try:
        # 获取健康检查地址
        health_address = CONF.default.health_address
        
        # 发送HTTP请求获取虚拟HA状态数据
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{health_address}/cloud/virtual-ha") as response:
                if response.status != 200:
                    return -1, "获取虚拟HA状态数据失败"
                
                result = await response.text()
                
                # 如果结果包含"state"，表示请求失败
                if "state" in result:
                    return -1, "获取虚拟HA状态数据失败"
                
                # 解析结果
                result_json = json.loads(result)
                
                # 检查虚拟HA状态
                ha_status = result_json.get("status")
                
                if ha_status != "OK":
                    # 如果虚拟HA状态不正常，返回异常状态
                    return -1, "云平台虚拟HA状态异常，详细信息请查看巡检报告。"
                else:
                    # 虚拟HA状态正常
                    return 0, "云平台虚拟HA状态正常"
    except Exception as e:
        return -1, f"检查云平台虚拟HA状态时出错: {str(e)}"

async def check_cloud_db_status(history_id: int, category_id: int):
    """云平台数据库状态检测

    检查云平台各主机的MySQL数据库服务状态，通过健康检查接口获取控制节点概览信息，
    检查每个主机的control_mysql字段状态，如果包含"down"则认为异常。

    Args:
        history_id (int): 健康检查历史记录ID
        category_id (int): 检查类别ID

    Returns:
        tuple: (状态码, 状态信息)
            - 0: 正常
            - -1: 异常
    """
    try:
        LOG.info(f"开始检查云平台数据库状态，history_id: {history_id}, category_id: {category_id}")

        # 使用公用函数获取平台数据（带缓存）
        status_code, error_msg, platform_data = await get_health_platform_data()
        if status_code != 0:
            LOG.error(f"获取平台数据失败: {error_msg}")
            return -1, error_msg

        # 获取控制节点概览信息
        control_overview = platform_data.get("control_overview")
        if not control_overview:
            LOG.warning("未获取到控制节点概览信息")
            return 0, "未获取到控制节点概览信息"

        error_count = 0
        cloud_check_infos = []

        LOG.debug(f"开始检查 {len(control_overview)} 个主机的数据库服务状态")

        # 遍历主机信息，检查每个主机的MySQL服务状态
        for host_ip, host_data in control_overview.items():
            try:
                if host_data:
                    mysql_status = host_data.get("control_mysql")
                    if mysql_status and "down" in mysql_status.lower():
                        error_count += 1
                        LOG.warning(f"主机 {host_ip} 的数据库服务状态异常: {mysql_status}")
                        # 记录异常主机信息
                        cloud_check_info = {
                            "host_ip": host_ip,
                            "category_id": category_id,
                            "state": -1,
                            "history_id": history_id
                        }
                        cloud_check_infos.append(cloud_check_info)
                    else:
                        LOG.debug(f"主机 {host_ip} 的数据库服务状态正常: {mysql_status}")
                else:
                    LOG.warning(f"主机 {host_ip} 的数据为空")
            except Exception as e:
                LOG.warning(f"处理主机 {host_ip} 数据时出错: {str(e)}")
                continue

        # 保存云平台检查信息到数据库
        if cloud_check_infos:
            LOG.info(f"保存 {len(cloud_check_infos)} 条异常主机信息到数据库")
            try:
                db = db_api.DB.get()
                async with db.transaction():
                    for info in cloud_check_infos:
                        insert_query = insert(SysSafeCloudCheckInfo).values(
                            history_id=info['history_id'],
                            category_id=info['category_id'],
                            host_ip=info['host_ip'],
                            state=info['state']
                        )
                        await db.execute(insert_query)
                LOG.debug("异常主机信息保存成功")
            except Exception as e:
                LOG.error(f"保存异常主机信息到数据库失败: {str(e)}")
                # 不影响主要检查逻辑，继续执行

        # 根据错误数量判断状态
        if error_count > 0:
            info = f"平台数据库服务检测时，共有{error_count}台主机数据库服务状态异常，详细信息请查看巡检报告。"
            LOG.warning(info)
            return -1, info
        else:
            LOG.info("所有主机的数据库服务状态正常")
            return 0, "平台数据库状态正常"

    except Exception as e:
        LOG.error(f"检查云平台数据库状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查云平台数据库状态时出错: {str(e)}"

async def check_cloud_nova(history_id: int, category_id: int):
    """云平台Nova服务检测

    检查云平台各主机的Nova计算服务状态，通过健康检查接口获取控制节点概览信息，
    检查每个主机的control_nova字段状态，如果包含"down"则认为异常。

    Args:
        history_id (int): 健康检查历史记录ID
        category_id (int): 检查类别ID

    Returns:
        tuple: (状态码, 状态信息)
            - 0: 正常
            - -1: 异常
    """
    try:
        LOG.info(f"开始检查云平台Nova服务状态，history_id: {history_id}, category_id: {category_id}")

        # 使用公用函数获取平台数据（带缓存）
        status_code, error_msg, platform_data = await get_health_platform_data()
        if status_code != 0:
            LOG.error(f"获取平台数据失败: {error_msg}")
            return -1, error_msg

        # 获取控制节点概览信息
        control_overview = platform_data.get("control_overview")
        if not control_overview:
            LOG.warning("未获取到控制节点概览信息")
            return 0, "未获取到控制节点概览信息"

        error_count = 0
        cloud_check_infos = []

        LOG.debug(f"开始检查 {len(control_overview)} 个主机的Nova服务状态")

        # 遍历主机信息，检查每个主机的Nova服务状态
        for host_ip, host_data in control_overview.items():
            try:
                if host_data:
                    nova_status = host_data.get("control_nova")
                    if nova_status and "down" in nova_status.lower():
                        error_count += 1
                        LOG.warning(f"主机 {host_ip} 的Nova服务状态异常: {nova_status}")
                        # 记录异常主机信息
                        cloud_check_info = {
                            "host_ip": host_ip,
                            "category_id": category_id,
                            "state": -1,
                            "history_id": history_id
                        }
                        cloud_check_infos.append(cloud_check_info)
                    else:
                        LOG.debug(f"主机 {host_ip} 的Nova服务状态正常: {nova_status}")
                else:
                    LOG.warning(f"主机 {host_ip} 的数据为空")
            except Exception as e:
                LOG.warning(f"处理主机 {host_ip} 数据时出错: {str(e)}")
                continue

        # 保存云平台检查信息到数据库
        if cloud_check_infos:
            LOG.info(f"保存 {len(cloud_check_infos)} 条异常主机信息到数据库")
            try:
                db = db_api.DB.get()
                async with db.transaction():
                    for info in cloud_check_infos:
                        insert_query = insert(SysSafeCloudCheckInfo).values(
                            history_id=info['history_id'],
                            category_id=info['category_id'],
                            host_ip=info['host_ip'],
                            state=info['state']
                        )
                        await db.execute(insert_query)
                LOG.debug("异常主机信息保存成功")
            except Exception as e:
                LOG.error(f"保存异常主机信息到数据库失败: {str(e)}")
                # 不影响主要检查逻辑，继续执行

        # 根据错误数量判断状态
        if error_count > 0:
            info = f"平台Nova服务检测时，共有{error_count}台主机Nova服务状态异常，详细信息请查看巡检报告。"
            LOG.warning(info)
            return -1, info
        else:
            LOG.info("所有主机的Nova服务状态正常")
            return 0, "平台Nova服务状态正常"

    except Exception as e:
        LOG.error(f"检查云平台Nova服务状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查云平台Nova服务状态时出错: {str(e)}"

async def check_cloud_cinder(history_id: int, category_id: int):
    """云平台Cinder服务检测

    检查云平台各主机的Cinder存储服务状态，通过健康检查接口获取控制节点概览信息，
    检查每个主机的control_cinder字段状态，如果包含"down"则认为异常。

    Args:
        history_id (int): 健康检查历史记录ID
        category_id (int): 检查类别ID

    Returns:
        tuple: (状态码, 状态信息)
            - 0: 正常
            - -1: 异常
    """
    try:
        LOG.info(f"开始检查云平台Cinder服务状态，history_id: {history_id}, category_id: {category_id}")

        # 使用公用函数获取平台数据（带缓存）
        status_code, error_msg, platform_data = await get_health_platform_data()
        if status_code != 0:
            LOG.error(f"获取平台数据失败: {error_msg}")
            return -1, error_msg

        # 获取控制节点概览信息
        control_overview = platform_data.get("control_overview")
        if not control_overview:
            LOG.warning("未获取到控制节点概览信息")
            return 0, "未获取到控制节点概览信息"

        error_count = 0
        cloud_check_infos = []

        LOG.debug(f"开始检查 {len(control_overview)} 个主机的Cinder服务状态")

        # 遍历主机信息，检查每个主机的Cinder服务状态
        for host_ip, host_data in control_overview.items():
            try:
                if host_data:
                    cinder_status = host_data.get("control_cinder")
                    if cinder_status and "down" in cinder_status.lower():
                        error_count += 1
                        LOG.warning(f"主机 {host_ip} 的Cinder服务状态异常: {cinder_status}")
                        # 记录异常主机信息
                        cloud_check_info = {
                            "host_ip": host_ip,
                            "category_id": category_id,
                            "state": -1,
                            "history_id": history_id
                        }
                        cloud_check_infos.append(cloud_check_info)
                    else:
                        LOG.debug(f"主机 {host_ip} 的Cinder服务状态正常: {cinder_status}")
                else:
                    LOG.warning(f"主机 {host_ip} 的数据为空")
            except Exception as e:
                LOG.warning(f"处理主机 {host_ip} 数据时出错: {str(e)}")
                continue

        # 保存云平台检查信息到数据库
        if cloud_check_infos:
            LOG.info(f"保存 {len(cloud_check_infos)} 条异常主机信息到数据库")
            try:
                db = db_api.DB.get()
                async with db.transaction():
                    for info in cloud_check_infos:
                        insert_query = insert(SysSafeCloudCheckInfo).values(
                            history_id=info['history_id'],
                            category_id=info['category_id'],
                            host_ip=info['host_ip'],
                            state=info['state']
                        )
                        await db.execute(insert_query)
                LOG.debug("异常主机信息保存成功")
            except Exception as e:
                LOG.error(f"保存异常主机信息到数据库失败: {str(e)}")
                # 不影响主要检查逻辑，继续执行

        # 根据错误数量判断状态
        if error_count > 0:
            info = f"平台Cinder服务检测时，共有{error_count}台主机Cinder服务状态异常，详细信息请查看巡检报告。"
            LOG.warning(info)
            return -1, info
        else:
            LOG.info("所有主机的Cinder服务状态正常")
            return 0, "平台Cinder服务状态正常"

    except Exception as e:
        LOG.error(f"检查云平台Cinder服务状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查云平台Cinder服务状态时出错: {str(e)}"

async def check_cloud_keystone(history_id: int, category_id: int):
    """云平台Keystone服务检测"""
    try:
        LOG.info(f"开始检查云平台Keystone服务状态，history_id: {history_id}, category_id: {category_id}")

        # 获取健康检查地址
        health_address = CONF.default.health_address
        LOG.debug(f"健康检查地址: {health_address}")

        # 使用公用函数获取平台数据（带缓存）
        status_code, error_msg, platform_data = await get_health_platform_data()
        if status_code != 0:
            LOG.error(f"获取平台数据失败: {error_msg}")
            return -1, error_msg
        
        # 将平台数据转换为JSON字符串以保持兼容性
        result = json.dumps(platform_data) if platform_data else None
        LOG.debug(f"获取到平台数据，长度: {len(result) if result else 0}")

        # 如果结果包含"state"，表示请求失败
        if "state" in result:
            LOG.error("平台数据包含错误状态")
            return -1, "获取平台数据失败"

        # 解析结果
        if result and "state" not in result:
            result_json = json.loads(result)
            control_overview = result_json.get("control_overview")

            if control_overview:
                error_count = 0
                cloud_check_infos = []

                LOG.debug(f"开始检查 {len(control_overview)} 个主机的Keystone服务状态")

                # 遍历主机信息
                for host_ip, host_data in control_overview.items():
                    if host_data:
                        keystone_status = host_data.get("control_keystone")
                        if keystone_status and "down" in keystone_status.lower():
                            error_count += 1
                            LOG.warning(f"主机 {host_ip} 的Keystone服务状态异常: {keystone_status}")
                            # 记录异常主机信息
                            cloud_check_info = {
                                "host_ip": host_ip,
                                "category_id": category_id,
                                "state": -1,
                                "history_id": history_id
                            }
                            cloud_check_infos.append(cloud_check_info)
                        else:
                            LOG.debug(f"主机 {host_ip} 的Keystone服务状态正常: {keystone_status}")

                # 保存云平台检查信息到数据库
                if cloud_check_infos:
                    LOG.info(f"保存 {len(cloud_check_infos)} 条异常主机信息到数据库")
                    db = db_api.DB.get()
                    async with db.transaction():
                        for info in cloud_check_infos:
                            insert_query = insert(SysSafeCloudCheckInfo).values(
                                history_id=info['history_id'],
                                category_id=info['category_id'],
                                host_ip=info['host_ip'],
                                state=info['state']
                            )
                            await db.execute(insert_query)

                # 根据错误数量判断状态
                if error_count > 0:
                    info = f"平台Keystone服务检测时，共有{error_count}台主机Keystone服务异常，详细信息请查看巡检报告。"
                    LOG.warning(info)
                    return -1, info
                else:
                    LOG.info("所有主机的Keystone服务状态正常")
                    return 0, "平台Keystone服务状态正常"
            else:
                LOG.warning("未获取到控制节点概览信息")
                return 0, "未获取到控制节点概览信息"
        else:
            LOG.error("获取平台数据失败或数据格式错误")
            return -1, "获取平台数据失败"

    except json.JSONDecodeError as e:
        LOG.error(f"解析JSON数据失败: {str(e)}")
        return -1, f"解析平台数据失败: {str(e)}"
    except aiohttp.ClientError as e:
        LOG.error(f"HTTP请求失败: {str(e)}")
        return -1, f"网络请求失败: {str(e)}"
    except Exception as e:
        LOG.error(f"检查云平台Keystone服务状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查云平台Keystone服务状态时出错: {str(e)}"

async def check_cloud_neutron_server(history_id: int, category_id: int):
    """云平台NeutronServer服务检测"""
    try:
        LOG.info(f"开始检查云平台NeutronServer服务状态，history_id: {history_id}, category_id: {category_id}")

        # 使用公用函数获取平台数据（带缓存）
        status_code, error_msg, platform_data = await get_health_platform_data()
        if status_code != 0:
            LOG.error(f"获取平台数据失败: {error_msg}")
            return -1, error_msg

        # 获取控制节点概览信息
        control_overview = platform_data.get("control_overview")
        if not control_overview:
            LOG.warning("未获取到控制节点概览信息")
            return 0, "未获取到控制节点概览信息"

        error_count = 0
        cloud_check_infos = []

        LOG.debug(f"开始检查 {len(control_overview)} 个主机的NeutronServer服务状态")

        # 遍历主机信息，检查每个主机的NeutronServer服务状态
        for host_ip, host_data in control_overview.items():
            try:
                if host_data:
                    neutron_status = host_data.get("control_neutron")
                    if neutron_status and "down" in neutron_status.lower():
                        error_count += 1
                        LOG.warning(f"主机 {host_ip} 的NeutronServer服务状态异常: {neutron_status}")
                        # 记录异常主机信息
                        cloud_check_info = {
                            "host_ip": host_ip,
                            "category_id": category_id,
                            "state": -1,
                            "history_id": history_id
                        }
                        cloud_check_infos.append(cloud_check_info)
                    else:
                        LOG.debug(f"主机 {host_ip} 的NeutronServer服务状态正常: {neutron_status}")
                else:
                    LOG.warning(f"主机 {host_ip} 的数据为空")
            except Exception as e:
                LOG.warning(f"处理主机 {host_ip} 数据时出错: {str(e)}")
                continue

        # 保存云平台检查信息到数据库
        if cloud_check_infos:
            LOG.info(f"保存 {len(cloud_check_infos)} 条异常主机信息到数据库")
            try:
                db = db_api.DB.get()
                async with db.transaction():
                    for info in cloud_check_infos:
                        insert_query = insert(SysSafeCloudCheckInfo).values(
                            history_id=info['history_id'],
                            category_id=info['category_id'],
                            host_ip=info['host_ip'],
                            state=info['state']
                        )
                        await db.execute(insert_query)
                LOG.debug("异常主机信息保存成功")
            except Exception as e:
                LOG.error(f"保存异常主机信息到数据库失败: {str(e)}")
                # 不影响主要检查逻辑，继续执行

        # 根据错误数量判断状态
        if error_count > 0:
            info = f"平台NeutronServer服务检测时，存在{error_count}台主机NeutronServer服务异常，详细信息请查看巡检报告。"
            LOG.warning(info)
            return -1, info
        else:
            LOG.info("所有主机的NeutronServer服务状态正常")
            return 0, "平台NeutronServer服务状态正常"

    except json.JSONDecodeError as e:
        LOG.error(f"解析JSON数据失败: {str(e)}")
        return -1, f"解析平台数据失败: {str(e)}"
    except aiohttp.ClientError as e:
        LOG.error(f"HTTP请求失败: {str(e)}")
        return -1, f"网络请求失败: {str(e)}"
    except Exception as e:
        LOG.error(f"检查云平台NeutronServer服务状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查云平台NeutronServer服务状态时出错: {str(e)}"

async def check_cloud_glance(history_id: int, category_id: int):
    """云平台Glance服务检测"""
    try:
        from skyline_apiserver.db import api as db_api
        from skyline_apiserver.config import CONF
        import aiohttp
        import json

        LOG.info(f"开始检查云平台Glance服务状态，history_id: {history_id}, category_id: {category_id}")

        # 获取健康检查地址
        health_address = CONF.default.health_address
        LOG.debug(f"健康检查地址: {health_address}")

        # 使用公用函数获取平台数据（带缓存）
        status_code, error_msg, platform_data = await get_health_platform_data()
        if status_code != 0:
            LOG.error(f"获取平台数据失败: {error_msg}")
            return -1, error_msg
        
        # 将平台数据转换为JSON字符串以保持兼容性
        result = json.dumps(platform_data) if platform_data else None
        LOG.debug(f"获取到平台数据，长度: {len(result) if result else 0}")

        # 如果结果包含"state"，表示请求失败
        if "state" in result:
            LOG.error("平台数据包含错误状态")
            return -1, "获取平台数据失败"

        # 解析结果
        if result and "state" not in result:
            result_json = json.loads(result)
            control_overview = result_json.get("control_overview")

            if control_overview:
                error_count = 0
                cloud_check_infos = []

                LOG.debug(f"开始检查 {len(control_overview)} 个主机的Glance服务状态")

                # 遍历主机信息
                for host_ip, host_data in control_overview.items():
                    if host_data:
                        glance_status = host_data.get("control_glance")
                        if glance_status and "down" in glance_status.lower():
                            error_count += 1
                            LOG.warning(f"主机 {host_ip} 的Glance服务状态异常: {glance_status}")
                            # 记录异常主机信息
                            cloud_check_info = {
                                "host_ip": host_ip,
                                "category_id": category_id,
                                "state": -1,
                                "history_id": history_id
                            }
                            cloud_check_infos.append(cloud_check_info)
                        else:
                            LOG.debug(f"主机 {host_ip} 的Glance服务状态正常: {glance_status}")

                # 保存云平台检查信息到数据库
                if cloud_check_infos:
                    LOG.info(f"保存 {len(cloud_check_infos)} 条异常主机信息到数据库")
                    db = db_api.DB.get()
                    async with db.transaction():
                        for info in cloud_check_infos:
                            insert_query = insert(SysSafeCloudCheckInfo).values(
                                history_id=info['history_id'],
                                category_id=info['category_id'],
                                host_ip=info['host_ip'],
                                state=info['state']
                            )
                            await db.execute(insert_query)

                # 根据错误数量判断状态
                if error_count > 0:
                    info = f"平台Glance服务检测时，共有{error_count}台主机Glance服务异常，详细信息请查看巡检报告。"
                    LOG.warning(info)
                    return -1, info
                else:
                    LOG.info("所有主机的Glance服务状态正常")
                    return 0, "平台Glance服务状态正常"
            else:
                LOG.warning("未获取到控制节点概览信息")
                return 0, "未获取到控制节点概览信息"
        else:
            LOG.error("获取平台数据失败或数据格式错误")
            return -1, "获取平台数据失败"

    except json.JSONDecodeError as e:
        LOG.error(f"解析JSON数据失败: {str(e)}")
        return -1, f"解析平台数据失败: {str(e)}"
    except aiohttp.ClientError as e:
        LOG.error(f"HTTP请求失败: {str(e)}")
        return -1, f"网络请求失败: {str(e)}"
    except Exception as e:
        LOG.error(f"检查云平台Glance服务状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查云平台Glance服务状态时出错: {str(e)}"

async def check_cloud_neutron_dhcp(history_id: int, category_id: int):
    """云平台NeutronDHCP服务检测"""
    try:
        LOG.info(f"开始检查云平台NeutronDHCP服务状态，history_id: {history_id}, category_id: {category_id}")

        # 使用公用函数获取平台数据（带缓存）
        status_code, error_msg, platform_data = await get_health_platform_data()
        if status_code != 0:
            LOG.error(f"获取平台数据失败: {error_msg}")
            return -1, error_msg

        # 获取控制节点概览信息
        control_overview = platform_data.get("control_overview")
        if not control_overview:
            LOG.warning("未获取到控制节点概览信息")
            return 0, "未获取到控制节点概览信息"

        error_count = 0
        cloud_check_infos = []

        LOG.debug(f"开始检查 {len(control_overview)} 个主机的NeutronDHCP服务状态")

        # 遍历主机信息，检查每个主机的NeutronDHCP服务状态
        for host_ip, host_data in control_overview.items():
            try:
                if host_data:
                    dhcp_status = host_data.get("control_network_dhcp")
                    if dhcp_status and "down" in dhcp_status.lower():
                        error_count += 1
                        LOG.warning(f"主机 {host_ip} 的NeutronDHCP服务状态异常: {dhcp_status}")
                        # 记录异常主机信息
                        cloud_check_info = {
                            "host_ip": host_ip,
                            "category_id": category_id,
                            "state": -1,
                            "history_id": history_id
                        }
                        cloud_check_infos.append(cloud_check_info)
                    else:
                        LOG.debug(f"主机 {host_ip} 的NeutronDHCP服务状态正常: {dhcp_status}")
                else:
                    LOG.warning(f"主机 {host_ip} 的数据为空")
            except Exception as e:
                LOG.warning(f"处理主机 {host_ip} 数据时出错: {str(e)}")
                continue

        # 保存云平台检查信息到数据库
        if cloud_check_infos:
            LOG.info(f"保存 {len(cloud_check_infos)} 条异常主机信息到数据库")
            try:
                db = db_api.DB.get()
                async with db.transaction():
                    for info in cloud_check_infos:
                        insert_query = insert(SysSafeCloudCheckInfo).values(
                            history_id=info['history_id'],
                            category_id=info['category_id'],
                            host_ip=info['host_ip'],
                            state=info['state']
                        )
                        await db.execute(insert_query)
                LOG.debug("异常主机信息保存成功")
            except Exception as e:
                LOG.error(f"保存异常主机信息到数据库失败: {str(e)}")
                # 不影响主要检查逻辑，继续执行

        # 根据错误数量判断状态
        if error_count > 0:
            info = f"平台NeutronDHCP服务检测时，共有{error_count}台主机NeutronDHCP服务异常，详细信息请查看巡检报告。"
            LOG.warning(info)
            return -1, info
        else:
            LOG.info("所有主机的NeutronDHCP服务状态正常")
            return 0, "平台NeutronDHCP服务状态正常"

    except json.JSONDecodeError as e:
        LOG.error(f"解析JSON数据失败: {str(e)}")
        return -1, f"解析平台数据失败: {str(e)}"
    except aiohttp.ClientError as e:
        LOG.error(f"HTTP请求失败: {str(e)}")
        return -1, f"网络请求失败: {str(e)}"
    except Exception as e:
        LOG.error(f"检查云平台NeutronDHCP服务状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查云平台NeutronDHCP服务状态时出错: {str(e)}"

async def check_cloud_keepalived(history_id: int, category_id: int):
    """云平台Keepalived服务检测"""
    try:
        LOG.info(f"开始检查云平台Keepalived服务状态，history_id: {history_id}, category_id: {category_id}")

        # 使用公用函数获取平台数据（带缓存）
        status_code, error_msg, platform_data = await get_health_platform_data()
        if status_code != 0:
            LOG.error(f"获取平台数据失败: {error_msg}")
            return -1, error_msg

        # 获取控制节点概览信息
        control_overview = platform_data.get("control_overview")
        if not control_overview:
            LOG.warning("未获取到控制节点概览信息")
            return 0, "未获取到控制节点概览信息"

        error_count = 0
        cloud_check_infos = []

        LOG.debug(f"开始检查 {len(control_overview)} 个主机的Keepalived服务状态")

        # 遍历主机信息，检查每个主机的Keepalived服务状态
        for host_ip, host_data in control_overview.items():
            try:
                if host_data:
                    keepalived_status = host_data.get("control_keepalived")
                    if keepalived_status and "down" in keepalived_status.lower():
                        error_count += 1
                        LOG.warning(f"主机 {host_ip} 的Keepalived服务状态异常: {keepalived_status}")
                        # 记录异常主机信息
                        cloud_check_info = {
                            "host_ip": host_ip,
                            "category_id": category_id,
                            "state": -1,
                            "history_id": history_id
                        }
                        cloud_check_infos.append(cloud_check_info)
                    else:
                        LOG.debug(f"主机 {host_ip} 的Keepalived服务状态正常: {keepalived_status}")
                else:
                    LOG.warning(f"主机 {host_ip} 的数据为空")
            except Exception as e:
                LOG.warning(f"处理主机 {host_ip} 数据时出错: {str(e)}")
                continue

        # 保存云平台检查信息到数据库
        if cloud_check_infos:
            LOG.info(f"保存 {len(cloud_check_infos)} 条异常主机信息到数据库")
            try:
                db = db_api.DB.get()
                async with db.transaction():
                    for info in cloud_check_infos:
                        insert_query = insert(SysSafeCloudCheckInfo).values(
                            history_id=info['history_id'],
                            category_id=info['category_id'],
                            host_ip=info['host_ip'],
                            state=info['state']
                        )
                        await db.execute(insert_query)
                LOG.debug("异常主机信息保存成功")
            except Exception as e:
                LOG.error(f"保存异常主机信息到数据库失败: {str(e)}")
                # 不影响主要检查逻辑，继续执行

        # 根据错误数量判断状态
        if error_count > 0:
            info = f"平台Keepalived服务检测时，共有{error_count}台主机Keepalived服务异常，详细信息请查看巡检报告。"
            LOG.warning(info)
            return -1, info
        else:
            LOG.info("所有主机的Keepalived服务状态正常")
            return 0, "平台Keepalived服务状态正常"

    except json.JSONDecodeError as e:
        LOG.error(f"解析JSON数据失败: {str(e)}")
        return -1, f"解析平台数据失败: {str(e)}"
    except aiohttp.ClientError as e:
        LOG.error(f"HTTP请求失败: {str(e)}")
        return -1, f"网络请求失败: {str(e)}"
    except Exception as e:
        LOG.error(f"检查云平台Keepalived服务状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查云平台Keepalived服务状态时出错: {str(e)}"

async def check_cloud_neutron_openvswitch(history_id: int, category_id: int):
    """云平台NeutronOpenvswitch服务检测"""
    try:
        LOG.info(f"开始检查云平台NeutronOpenvswitch服务状态，history_id: {history_id}, category_id: {category_id}")

        # 使用公用函数获取平台数据（带缓存）
        status_code, error_msg, platform_data = await get_health_platform_data()
        if status_code != 0:
            LOG.error(f"获取平台数据失败: {error_msg}")
            return -1, error_msg

        # 获取控制节点概览信息
        control_overview = platform_data.get("control_overview")
        if not control_overview:
            LOG.warning("未获取到控制节点概览信息")
            return 0, "未获取到控制节点概览信息"

        error_count = 0
        cloud_check_infos = []

        # 获取计算节点概览信息（注意：这里使用 compute_overview 而不是 control_overview）
        compute_overview = platform_data.get("compute_overview")
        if not compute_overview:
            LOG.warning("未获取到计算节点概览信息")
            return 0, "未获取到计算节点概览信息"

        LOG.debug(f"开始检查 {len(compute_overview)} 个主机的NeutronOpenvswitch服务状态")

        # 遍历主机信息，检查每个主机的NeutronOpenvswitch服务状态
        for host_ip, host_data in compute_overview.items():
            try:
                if host_data:
                    openvswitch_status = host_data.get("openvswitch_agent")
                    if openvswitch_status and "down" in openvswitch_status.lower():
                        error_count += 1
                        LOG.warning(f"主机 {host_ip} 的NeutronOpenvswitch服务状态异常: {openvswitch_status}")
                        # 记录异常主机信息
                        cloud_check_info = {
                            "host_ip": host_ip,
                            "category_id": category_id,
                            "state": -1,
                            "history_id": history_id
                        }
                        cloud_check_infos.append(cloud_check_info)
                    else:
                        LOG.debug(f"主机 {host_ip} 的NeutronOpenvswitch服务状态正常: {openvswitch_status}")
                else:
                    LOG.warning(f"主机 {host_ip} 的数据为空")
            except Exception as e:
                LOG.warning(f"处理主机 {host_ip} 数据时出错: {str(e)}")
                continue

        # 保存云平台检查信息到数据库
        if cloud_check_infos:
            LOG.info(f"保存 {len(cloud_check_infos)} 条异常主机信息到数据库")
            try:
                db = db_api.DB.get()
                async with db.transaction():
                    for info in cloud_check_infos:
                        insert_query = insert(SysSafeCloudCheckInfo).values(
                            history_id=info['history_id'],
                            category_id=info['category_id'],
                            host_ip=info['host_ip'],
                            state=info['state']
                        )
                        await db.execute(insert_query)
                LOG.debug("异常主机信息保存成功")
            except Exception as e:
                LOG.error(f"保存异常主机信息到数据库失败: {str(e)}")
                # 不影响主要检查逻辑，继续执行

        # 根据错误数量判断状态
        if error_count > 0:
            info = f"平台NeutronOpenvswitch服务检测时，共有{error_count}台主机NeutronOpenvswitch服务异常，详细信息请查看巡检报告。"
            LOG.warning(info)
            return -1, info
        else:
            LOG.info("所有主机的NeutronOpenvswitch服务状态正常")
            return 0, "平台NeutronOpenvswitch服务状态正常"

    except json.JSONDecodeError as e:
        LOG.error(f"解析JSON数据失败: {str(e)}")
        return -1, f"解析平台数据失败: {str(e)}"
    except aiohttp.ClientError as e:
        LOG.error(f"HTTP请求失败: {str(e)}")
        return -1, f"网络请求失败: {str(e)}"
    except Exception as e:
        LOG.error(f"检查云平台NeutronOpenvswitch服务状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查云平台NeutronOpenvswitch服务状态时出错: {str(e)}"

async def check_cloud_nova_compute(history_id: int, category_id: int):
    """云平台NovaCompute服务检测"""
    try:
        LOG.info(f"开始检查云平台NovaCompute服务状态，history_id: {history_id}, category_id: {category_id}")

        # 使用公用函数获取平台数据（带缓存）
        status_code, error_msg, platform_data = await get_health_platform_data()
        if status_code != 0:
            LOG.error(f"获取平台数据失败: {error_msg}")
            return -1, error_msg

        # 获取计算节点概览信息（注意：这里使用 compute_overview 而不是 control_overview）
        compute_overview = platform_data.get("compute_overview")
        if not compute_overview:
            LOG.warning("未获取到计算节点概览信息")
            return 0, "未获取到计算节点概览信息"

        error_count = 0
        cloud_check_infos = []

        LOG.debug(f"开始检查 {len(compute_overview)} 个主机的NovaCompute服务状态")

        # 遍历主机信息，检查每个主机的NovaCompute服务状态
        for host_ip, host_data in compute_overview.items():
            try:
                if host_data:
                    nova_compute_status = host_data.get("nova_compute")
                    if nova_compute_status and "down" in nova_compute_status.lower():
                        error_count += 1
                        LOG.warning(f"主机 {host_ip} 的NovaCompute服务状态异常: {nova_compute_status}")
                        # 记录异常主机信息
                        cloud_check_info = {
                            "host_ip": host_ip,
                            "category_id": category_id,
                            "state": -1,
                            "history_id": history_id
                        }
                        cloud_check_infos.append(cloud_check_info)
                    else:
                        LOG.debug(f"主机 {host_ip} 的NovaCompute服务状态正常: {nova_compute_status}")
                else:
                    LOG.warning(f"主机 {host_ip} 的数据为空")
            except Exception as e:
                LOG.warning(f"处理主机 {host_ip} 数据时出错: {str(e)}")
                continue

        # 保存云平台检查信息到数据库
        if cloud_check_infos:
            LOG.info(f"保存 {len(cloud_check_infos)} 条异常主机信息到数据库")
            try:
                db = db_api.DB.get()
                async with db.transaction():
                    for info in cloud_check_infos:
                        insert_query = insert(SysSafeCloudCheckInfo).values(
                            history_id=info['history_id'],
                            category_id=info['category_id'],
                            host_ip=info['host_ip'],
                            state=info['state']
                        )
                        await db.execute(insert_query)
                LOG.debug("异常主机信息保存成功")
            except Exception as e:
                LOG.error(f"保存异常主机信息到数据库失败: {str(e)}")
                # 不影响主要检查逻辑，继续执行

        # 根据错误数量判断状态
        if error_count > 0:
            info = f"平台NovaCompute服务检测时，共有{error_count}台主机NovaCompute服务异常，详细信息请查看巡检报告。"
            LOG.warning(info)
            return -1, info
        else:
            LOG.info("所有主机的NovaCompute服务状态正常")
            return 0, "平台NovaCompute服务状态正常"

    except json.JSONDecodeError as e:
        LOG.error(f"解析JSON数据失败: {str(e)}")
        return -1, f"解析平台数据失败: {str(e)}"
    except aiohttp.ClientError as e:
        LOG.error(f"HTTP请求失败: {str(e)}")
        return -1, f"网络请求失败: {str(e)}"
    except Exception as e:
        LOG.error(f"检查云平台NovaCompute服务状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查云平台NovaCompute服务状态时出错: {str(e)}"

async def check_cloud_cinder_volume(history_id: int, category_id: int):
    """云平台CinderVolume服务检测"""
    try:
        LOG.info(f"开始检查云平台CinderVolume服务状态，history_id: {history_id}, category_id: {category_id}")

        # 使用公用函数获取平台数据（带缓存）
        status_code, error_msg, platform_data = await get_health_platform_data()
        if status_code != 0:
            LOG.error(f"获取平台数据失败: {error_msg}")
            return -1, error_msg

        # 获取计算节点概览信息（注意：这里使用 compute_overview 而不是 control_overview）
        compute_overview = platform_data.get("compute_overview")
        if not compute_overview:
            LOG.warning("未获取到计算节点概览信息")
            return 0, "未获取到计算节点概览信息"

        error_count = 0
        cloud_check_infos = []

        # 遍历主机信息，检查每个主机的CinderVolume服务状态
        for host_ip, host_data in compute_overview.items():
            try:
                if host_data:
                    cinder_volume_status = host_data.get("cinder_volume")
                    if cinder_volume_status and "down" in cinder_volume_status.lower():
                        error_count += 1
                        LOG.warning(f"主机 {host_ip} 的CinderVolume服务状态异常: {cinder_volume_status}")
                        # 记录异常主机信息
                        cloud_check_info = {
                            "host_ip": host_ip,
                            "category_id": category_id,
                            "state": -1,
                            "history_id": history_id
                        }
                        cloud_check_infos.append(cloud_check_info)
                    else:
                        LOG.debug(f"主机 {host_ip} 的CinderVolume服务状态正常: {cinder_volume_status}")
                else:
                    LOG.warning(f"主机 {host_ip} 的数据为空")
            except Exception as e:
                LOG.warning(f"处理主机 {host_ip} 数据时出错: {str(e)}")
                continue

        # 保存云平台检查信息到数据库
        if cloud_check_infos:
            LOG.info(f"保存 {len(cloud_check_infos)} 条异常主机信息到数据库")
            try:
                db = db_api.DB.get()
                async with db.transaction():
                    for info in cloud_check_infos:
                        insert_query = insert(SysSafeCloudCheckInfo).values(
                            history_id=info['history_id'],
                            category_id=info['category_id'],
                            host_ip=info['host_ip'],
                            state=info['state']
                        )
                        await db.execute(insert_query)
                LOG.debug("异常主机信息保存成功")
            except Exception as e:
                LOG.error(f"保存异常主机信息到数据库失败: {str(e)}")
                # 不影响主要检查逻辑，继续执行

        # 根据错误数量判断状态
        if error_count > 0:
            info = f"平台CinderVolume服务检测时，共有{error_count}台主机CinderVolume服务异常，详细信息请查看巡检报告。"
            LOG.warning(info)
            return -1, info
        else:
            LOG.info("所有主机的CinderVolume服务状态正常")
            return 0, "平台CinderVolume服务状态正常"

    except json.JSONDecodeError as e:
        LOG.error(f"解析JSON数据失败: {str(e)}")
        return -1, f"解析平台数据失败: {str(e)}"
    except aiohttp.ClientError as e:
        LOG.error(f"HTTP请求失败: {str(e)}")
        return -1, f"网络请求失败: {str(e)}"
    except Exception as e:
        LOG.error(f"检查云平台CinderVolume服务状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查云平台CinderVolume服务状态时出错: {str(e)}"

async def check_cloud_neutron(history_id: int, category_id: int):
    """云平台Neutron服务检测"""
    try:
        from skyline_apiserver.db import api as db_api
        from skyline_apiserver.config import CONF
        import aiohttp
        import json

        LOG.info(f"开始检查云平台Neutron服务状态，history_id: {history_id}, category_id: {category_id}")

        # 获取健康检查地址
        health_address = CONF.default.health_address
        LOG.debug(f"健康检查地址: {health_address}")

        # 使用公用函数获取平台数据（带缓存）
        status_code, error_msg, platform_data = await get_health_platform_data()
        if status_code != 0:
            LOG.error(f"获取平台数据失败: {error_msg}")
            return -1, error_msg
        
        # 将平台数据转换为JSON字符串以保持兼容性
        result = json.dumps(platform_data) if platform_data else None
        LOG.debug(f"获取到平台数据，长度: {len(result) if result else 0}")

        # 如果结果包含"state"，表示请求失败
        if "state" in result:
            LOG.error("平台数据包含错误状态")
            return -1, "获取平台数据失败"

        # 解析结果 - 注意：这里使用 platform_overview.iaas_service_overview.ops_neutron
        if result and "state" not in result:
            result_json = json.loads(result)
            platform_overview = result_json.get("platform_overview")

            if platform_overview:
                error_count = 0

                LOG.debug("开始检查平台Neutron服务状态")

                iaas_service_overview = platform_overview.get("iaas_service_overview")
                if iaas_service_overview:
                    ops_neutron = iaas_service_overview.get("ops_neutron")
                    if ops_neutron and "false" in str(ops_neutron).lower():
                        error_count += 1
                        LOG.warning(f"平台Neutron服务状态异常: {ops_neutron}")

                        # 保存云平台检查信息到数据库
                        cloud_check_info = {
                            "category_id": category_id,
                            "state": -1,
                            "history_id": history_id
                        }

                        db = db_api.DB.get()
                        async with db.transaction():
                            insert_query = insert(SysSafeCloudCheckInfo).values(
                                history_id=cloud_check_info['history_id'],
                                category_id=cloud_check_info['category_id'],
                                state=cloud_check_info['state']
                            )
                            await db.execute(insert_query)
                    else:
                        LOG.debug(f"平台Neutron服务状态正常: {ops_neutron}")

                # 根据错误数量判断状态
                if error_count > 0:
                    info = f"平台Neutron服务检测时，共有{error_count}台主机Neutron服务异常，详细信息请查看巡检报告。"
                    LOG.warning(info)
                    return -1, info
                else:
                    LOG.info("平台Neutron服务状态正常")
                    return 0, "平台Neutron服务状态正常"
            else:
                LOG.warning("未获取到平台概览信息")
                return 0, "未获取到平台概览信息"
        else:
            LOG.error("获取平台数据失败或数据格式错误")
            return -1, "获取平台数据失败"

    except json.JSONDecodeError as e:
        LOG.error(f"解析JSON数据失败: {str(e)}")
        return -1, f"解析平台数据失败: {str(e)}"
    except aiohttp.ClientError as e:
        LOG.error(f"HTTP请求失败: {str(e)}")
        return -1, f"网络请求失败: {str(e)}"
    except Exception as e:
        LOG.error(f"检查云平台Neutron服务状态时出现未知错误: {str(e)}", exc_info=True)
        return -1, f"检查云平台Neutron服务状态时出错: {str(e)}"

async def check_cloud_ha_status(history_id: int, category_id: int):
    """云平台HA状态检测 - 与Java代码逻辑完全一致"""
    try:
        data = 0  # 检查结果状态：0-正常，-1-异常
        info = None  # 检查结果信息
        error = 0  # 异常主机计数器

        # 使用公用函数获取平台数据（带缓存）
        status_code, error_msg, platform_data = await get_health_platform_data()
        if status_code != 0:
            LOG.error(f"获取平台数据失败: {error_msg}")
            return -1, error_msg

        # 获取控制节点概览信息
        control_overview = platform_data.get("control_overview")
        if not control_overview:
            LOG.warning("未获取到控制节点概览信息")
            return 0, "未获取到控制节点概览信息"

        cloud_check_infos = []  # 存储异常主机信息的列表

        # 遍历主机信息（与Java代码逻辑一致）
        for host_ip, host_data in control_overview.items():
            try:
                if host_data is not None and str(host_data).strip():
                    # 获取haproxy状态（与Java代码一致）
                    haproxy_status = host_data.get("control_haproxy")
                    if (haproxy_status is not None and
                        str(haproxy_status).strip() and
                        "down" in str(haproxy_status).lower()):

                        error += 1  # 增加异常计数器

                        # 创建云平台检查信息实体（与Java代码一致）
                        cloud_check_info = {
                            "host_ip": str(host_ip),
                            "category_id": category_id,
                            "state": -1,  # 异常状态
                            "history_id": history_id
                        }
                        cloud_check_infos.append(cloud_check_info)
                        LOG.debug(f"发现HA异常主机: {host_ip}, haproxy状态: {haproxy_status}")
                    else:
                        LOG.debug(f"主机 {host_ip} 的HA状态正常: {haproxy_status}")
                else:
                    LOG.warning(f"主机 {host_ip} 的数据为空")
            except Exception as e:
                LOG.warning(f"处理主机 {host_ip} 数据时出错: {str(e)}")
                continue

        # 批量保存异常主机信息到数据库（与Java代码一致）
        if len(cloud_check_infos) > 0:
            try:
                db = db_api.DB.get()
                async with db.transaction():
                    for cloud_info in cloud_check_infos:
                        insert_query = insert(SysSafeCloudCheckInfo).values(
                            history_id=cloud_info['history_id'],
                            category_id=cloud_info['category_id'],
                            host_ip=cloud_info['host_ip'],
                            state=cloud_info['state']
                        )
                        await db.execute(insert_query)
                LOG.info(f"保存了 {len(cloud_check_infos)} 条HA异常主机记录")
            except Exception as e:
                LOG.error(f"保存HA异常主机信息到数据库失败: {str(e)}")
                # 不影响主要检查逻辑，继续执行

        # 根据异常数量设置检查结果（与Java代码逻辑一致）
        if error > 0:
            data = -1
            info = f"平台HA检测时，共有{error}台主机存在HA状态异常，详细信息请查看巡检报告。"

        LOG.info(f"云平台HA状态检测完成: 状态={data}, 异常主机数={error}")

        return data, info if info else "云平台HA状态检测完成"

    except Exception as e:
        # 异常处理（与Java代码一致）
        data = -1
        info = str(e)
        LOG.error(f"检查云平台HA状态时出错: {str(e)}", exc_info=True)

        return data, info



async def _get_volumes_from_openstack_api(x_openstack_request_id: str, profile: schemas.Profile):
    """从OpenStack API获取异常状态的卷列表（状态为error_attaching和error_detaching）"""
    try:
        LOG.debug("开始从OpenStack API获取异常状态的卷列表")

        # 获取系统级别的session（借鉴check_guestos_cpu_used_percent模式）
        current_session = await generate_session(profile=profile)

        volume_list = []

        # 需要筛选的异常状态列表
        error_statuses = ["error_attaching", "error_detaching"]

        try:
            # 分别筛选每种异常状态的卷（因为API可能不支持同时筛选多个状态）
            for status in error_statuses:
                try:
                    search_opts = {
                        "status": status,  # 筛选指定异常状态的卷
                        "all_tenants": True  # 获取所有租户的卷
                    }

                    LOG.debug(f"使用筛选参数获取{status}状态的卷: {search_opts}")

                    # 获取当前状态的异常卷
                    volumes = await cinder.list_volumes(
                        profile=profile,
                        session=current_session,
                        global_request_id=x_openstack_request_id,
                        search_opts=search_opts
                    )

                    LOG.debug(f"从OpenStack API获取到 {len(volumes)} 个{status}状态的卷")

                    # 处理获取到的异常状态卷信息
                    for volume in volumes:
                        try:
                            volume_dict = volume.to_dict() if hasattr(volume, 'to_dict') else dict(volume)

                            # 保存异常状态卷的相关数据（不依赖attachments）
                            volume_record = {
                                'id': volume_dict.get('id'),
                                'name': volume_dict.get('name', ''),
                                'status': volume_dict.get('status'),
                                'size': volume_dict.get('size', 0),
                                'volume_type': volume_dict.get('volume_type', ''),
                                'created_at': volume_dict.get('created_at', ''),
                                'updated_at': volume_dict.get('updated_at', ''),
                                'project_id': volume_dict.get('os-vol-tenant-attr:tenant_id', ''),
                                'host': volume_dict.get('os-vol-host-attr:host', ''),
                                'region': profile.region,  # 使用profile中的region
                                'error_status': status  # 记录具体的错误状态
                            }

                            # 如果有attachments信息，也保存下来
                            attachments = volume_dict.get('attachments', [])
                            if attachments:
                                volume_record['attachments'] = attachments
                                # 如果有server_id，也记录下来
                                for attachment in attachments:
                                    server_id = attachment.get('server_id')
                                    if server_id:
                                        volume_record['server_id'] = server_id
                                        volume_record['attachment_id'] = attachment.get('id')
                                        volume_record['device'] = attachment.get('device')
                                        break  # 只取第一个attachment的server_id

                            volume_list.append(volume_record)
                            LOG.debug(f"添加异常状态卷: volume_id={volume_record['id']}, "
                                    f"status={volume_record['status']}, "
                                    f"server_id={volume_record.get('server_id', 'N/A')}")

                        except Exception as e:
                            LOG.error(f"处理{status}状态卷信息时出错: {str(e)}", exc_info=True)
                            continue

                except Exception as e:
                    LOG.error(f"获取{status}状态卷时出错: {str(e)}", exc_info=True)
                    continue

        except Exception as e:
            LOG.error(f"获取异常状态卷信息时出错: {str(e)}", exc_info=True)
            return []

        LOG.info(f"从OpenStack API总共获取到 {len(volume_list)} 个异常状态的卷")
        return volume_list

    except Exception as e:
        LOG.error(f"从OpenStack API获取异常状态卷列表时出错: {str(e)}", exc_info=True)
        return []


#原java代码有问题，不采用原有逻辑，使用自己定义的新逻辑
async def check_guestos_volume(history_id: int, category_id: int, x_openstack_request_id: str, profile: schemas.Profile):
    """GuestOS卷状态检测 - 检测异常状态的卷（error_attaching和error_detaching）"""
    try:
        data = 0  # 检查结果状态：0-正常，-1-异常
        info = None  # 检查结果信息
        error = 0  # 异常卷计数器

        LOG.info(f"开始GuestOS卷状态检测: history_id={history_id}, category_id={category_id}")

        # 获取数据库连接
        db = db_api.DB.get()

        # 从OpenStack API获取异常状态的卷信息（error_attaching和error_detaching）
        volume_list = await _get_volumes_from_openstack_api(x_openstack_request_id, profile)

        LOG.debug(f"从OpenStack API获取到{len(volume_list)}个异常状态的卷记录")

        # 处理异常状态的卷（新逻辑：直接处理筛选出来的异常卷）
        if volume_list and len(volume_list) > 0:
            for volume in volume_list:
                try:
                    # 获取卷的基本信息
                    volume_id = volume.get("id")
                    volume_status = volume.get("status")
                    error_status = volume.get("error_status")  # 具体的错误状态
                    volume_name = volume.get("name", "")

                    # 由于已经筛选了异常状态，所有获取到的卷都是有问题的
                    error += 1  # 增加异常计数器

                    LOG.warning(f"发现异常状态卷: volume_id={volume_id}, "
                              f"volume_name={volume_name}, "
                              f"status={volume_status}, "
                              f"error_type={error_status}")

                    # 如果卷有关联的虚拟机，记录相关信息
                    server_id = volume.get("server_id")
                    if server_id:
                        LOG.warning(f"异常卷关联的虚拟机: server_id={server_id}, "
                                  f"device={volume.get('device', 'N/A')}")

                    # 如果有关联的虚拟机，保存检查信息记录
                    if server_id:
                        await _save_or_update_guest_check_info(
                            db, history_id, server_id, volume_state=-1
                        )

                except Exception as e:
                    LOG.error(f"处理异常卷记录时出错: {str(e)}", exc_info=True)
                    continue
        else:
            LOG.info("未发现异常状态的卷")

        # 根据异常数量设置检查结果
        if error > 0:
            data = -1
            info = f"GuestOS卷状态检测发现异常：共有{error}个卷处于异常状态（error_attaching或error_detaching），详细信息请查看巡检报告。"
        else:
            data = 0
            info = "GuestOS卷状态检测正常：未发现处于异常状态的卷。"

        LOG.info(f"GuestOS卷状态检测完成: 状态={data}, 异常卷数={error}")

        return data, info

    except Exception as e:
        # 异常处理
        data = -1
        info = f"检查GuestOS卷状态时出错: {str(e)}"
        LOG.error(f"检查GuestOS卷状态时出错: {str(e)}", exc_info=True)

        return data, info


async def _save_or_update_guest_check_info(db, history_id: int, server_id: str,
                                         memory_state: int = None, memory_percent: float = None,
                                         cpu_state: int = None, cpu_percent: float = None,
                                         volume_state: int = None):
    """保存或更新虚机检查信息到数据库（与Java代码逻辑一致）"""
    try:
        LOG.debug(f"开始保存虚机检查信息: history_id={history_id}, server_id={server_id}")
        # 检查是否已存在记录（使用SQLAlchemy）
        check_query = select(SysSafeGuestCheckInfo.c.id).where(
            and_(
                SysSafeGuestCheckInfo.c.history_id == history_id,
                SysSafeGuestCheckInfo.c.server_id == str(server_id)
            )
        )

        existing_record = None
        try:
            async with db.transaction():
                existing_record = await db.fetch_one(check_query)
                LOG.debug(f"查询现有记录结果: {existing_record}")
        except Exception as query_error:
            LOG.error(f"查询现有记录时出错: {str(query_error)}", exc_info=True)
            # 如果查询失败，假设记录不存在，直接插入
            existing_record = None

        if existing_record:
            # 更新现有记录（使用SQLAlchemy）
            update_values = {}

            if memory_state is not None:
                update_values["memory_state"] = memory_state
            if memory_percent is not None:
                update_values["memory_percent"] = memory_percent
            if cpu_state is not None:
                update_values["cpu_state"] = cpu_state
            if cpu_percent is not None:
                update_values["cpu_percent"] = cpu_percent
            if volume_state is not None:
                update_values["volume_state"] = volume_state

            if update_values:
                update_query = update(SysSafeGuestCheckInfo).where(
                    and_(
                        SysSafeGuestCheckInfo.c.history_id == history_id,
                        SysSafeGuestCheckInfo.c.server_id == str(server_id)
                    )
                ).values(**update_values)

                async with db.transaction():
                    await db.execute(update_query)
                LOG.debug(f"更新虚机检查信息: server_id={server_id}, history_id={history_id}")
        else:
            # 插入新记录（使用SQLAlchemy）
            insert_query = insert(SysSafeGuestCheckInfo).values(
                history_id=history_id,
                server_id=str(server_id),
                memory_state=memory_state,
                memory_percent=memory_percent,
                cpu_state=cpu_state,
                cpu_percent=cpu_percent,
                volume_state=volume_state
            )

            async with db.transaction():
                await db.execute(insert_query)
            LOG.debug(f"插入虚机检查信息: server_id={server_id}, history_id={history_id}")

    except Exception as e:
        LOG.error(f"保存虚机检查信息失败: history_id={history_id}, server_id={server_id}, error={str(e)}", exc_info=True)
        # 不重新抛出异常，避免影响整个检查流程
        pass


async def save_guest_check_info(db, history_id: int, server_id: str,
                               memory_state: int = None, memory_percent: float = None,
                               cpu_state: int = None, cpu_percent: float = None,
                               volume_state: str = None):
    """保存虚机检查信息到数据库"""

    # 检查是否已存在记录
    check_query = select(SysSafeGuestCheckInfo.c.id).where(
        and_(
            SysSafeGuestCheckInfo.c.history_id == history_id,
            SysSafeGuestCheckInfo.c.server_id == server_id
        )
    )

    existing_record = await db.fetch_one(check_query)

    if existing_record:
        # 更新现有记录
        update_fields = []
        params = {"history_id": history_id, "server_id": server_id}

        if memory_state is not None:
            update_fields.append("memory_state = :memory_state")
            params["memory_state"] = memory_state
        if memory_percent is not None:
            update_fields.append("memory_percent = :memory_percent")
            params["memory_percent"] = memory_percent
        if cpu_state is not None:
            update_fields.append("cpu_state = :cpu_state")
            params["cpu_state"] = cpu_state
        if cpu_percent is not None:
            update_fields.append("cpu_percent = :cpu_percent")
            params["cpu_percent"] = cpu_percent
        if volume_state is not None:
            update_fields.append("volume_state = :volume_state")
            params["volume_state"] = volume_state

        if update_fields:
            # 构建更新值字典
            update_values = {}
            if memory_state is not None:
                update_values['memory_state'] = memory_state
            if memory_percent is not None:
                update_values['memory_percent'] = memory_percent
            if cpu_state is not None:
                update_values['cpu_state'] = cpu_state
            if cpu_percent is not None:
                update_values['cpu_percent'] = cpu_percent
            if volume_state is not None:
                update_values['volume_state'] = volume_state

            update_query = update(SysSafeGuestCheckInfo).where(
                and_(
                    SysSafeGuestCheckInfo.c.history_id == history_id,
                    SysSafeGuestCheckInfo.c.server_id == server_id
                )
            ).values(**update_values)
            await db.execute(update_query)
    else:
        # 插入新记录
        insert_query = insert(SysSafeGuestCheckInfo).values(
            history_id=history_id,
            server_id=server_id,
            memory_state=memory_state,
            memory_percent=memory_percent,
            cpu_state=cpu_state,
            cpu_percent=cpu_percent,
            volume_state=volume_state
        )

        await db.execute(insert_query)


async def check_guestos_memory_used_percent(history_id: int, category_id: int, x_openstack_request_id: str = "", profile: schemas.Profile = None):
    """GuestOS内存利用率检测 - 使用OpenStack API获取虚机列表，Prometheus获取内存使用率"""
    try:
        LOG.debug(f"开始执行GuestOS内存利用率检测，history_id: {history_id}, category_id: {category_id}")

        data = 0
        info = None
        error = 0
        warn = 0

        # 获取监控阈值设置
        db = db_api.DB.get()

        settings_query = select(SysSafeHealthCheckSettings).where(
            and_(
                SysSafeHealthCheckSettings.c.category_code == 'GUESTOS_MEMORY_USED_PERCENT',
                SysSafeHealthCheckSettings.c.deleted == 1
            )
        )

        settings = []
        async with db.transaction():
            result = await db.fetch_all(settings_query)
            if result:
                settings = [dict(s) for s in result]

        # 如果没有配置监控规则，直接返回正常状态
        if not settings:
            LOG.info("未配置GuestOS内存利用率监控规则")
            return 0, "未配置GuestOS内存利用率监控规则"

        # 获取阈值配置
        min_threshold = settings[0].get('min')
        max_threshold = settings[0].get('max')

        if min_threshold is None or max_threshold is None:
            LOG.warning("内存利用率监控阈值配置不完整")
            return 0, "内存利用率监控阈值配置不完整"

        # 获取Prometheus配置
        prometheus_url = CONF.default.prometheus_endpoint
        if not prometheus_url:
            LOG.error("Prometheus配置未设置")
            return -1, "Prometheus配置未设置"

        LOG.debug(f"使用Prometheus地址: {prometheus_url}")
        LOG.debug(f"内存使用率阈值 - 警告: {min_threshold}%, 异常: {max_threshold}%")

        # 使用已有的公用函数获取虚机列表（不使用数据库）
        try:
            # 使用OpenStack API获取虚机列表
            servers = await _get_servers_from_openstack_api(x_openstack_request_id, profile)
            LOG.debug(f"从OpenStack API获取到 {len(servers)} 个虚机")

        except Exception as e:
            LOG.error(f"获取虚机列表失败: {str(e)}", exc_info=True)
            return -1, f"获取虚机列表失败: {str(e)}"

        if not servers:
            LOG.info("未找到任何虚机")
            return 0, "未找到任何虚机"

        # 遍历所有虚机，检查内存使用率
        for server in servers:
            instance_id = server.get('third_server_id') or server.get('id')

            if not instance_id:
                LOG.warning(f"虚机缺少instance_id: {server}")
                continue

            try:
                # 使用Prometheus获取5分钟内的内存使用率（替代数据库查询）
                memory_percent = await _get_vm_memory_usage_from_prometheus(instance_id, prometheus_url)
                if memory_percent is not None:

                    if (min_threshold is not None and max_threshold is not None):
                        memory_state = None

                        if memory_percent > max_threshold:
                            error += 1
                            memory_state = -1
                        elif memory_percent > min_threshold:
                            warn += 1
                            memory_state = 1

                        # 需要保存检查记录
                        if memory_state is not None:
                            # 直接使用OpenStack server ID作为server_id，不需要查询数据库
                            server_id = instance_id  # instance_id就是OpenStack的server UUID
                            await _save_or_update_guest_check_info(
                                db, history_id, server_id,
                                memory_state=memory_state,
                                memory_percent=memory_percent
                            )
                            LOG.debug(f"保存虚机 {instance_id} 内存检查记录: 内存使用率={memory_percent}%")
                else:
                    LOG.warning(f"虚机 {instance_id} 无法获取内存使用率数据")

            except Exception as e:
                LOG.error(f"处理虚机 {instance_id} 内存监控数据时出现异常: {str(e)}", exc_info=True)
                continue

        # 根据检查结果设置返回状态（与Java代码逻辑一致）
        if error > 0:
            info = f"GuestOS虚机内存利用情况检测时，共有{error}台虚机内存使用率处于异常状态"
            if warn > 0:
                info += f";共有{warn}台虚机内存使用率处于警告状态"
            data = -1
        elif warn > 0:
            info = f"GuestOS虚机内存利用情况检测时，共有{warn}台虚机内存使用率处于警告状态"
            data = 1

        if info:
            info += "，详细信息请查看巡检报告。"

        return data, info

    except Exception as e:
        LOG.error(f"GuestOS内存利用率检测时出现异常: {str(e)}", exc_info=True)
        return -1, str(e)


async def _get_servers_from_openstack_api(x_openstack_request_id: str, profile: schemas.Profile):
    """从OpenStack API获取虚机列表"""
    try:
        LOG.debug("开始从OpenStack API获取虚机列表")

        current_session = await generate_session(profile=profile)

        # 获取当前region（不遍历所有regions，只使用当前region）
        current_region = profile.region if profile and profile.region else "RegionOne"
        LOG.debug(f"使用当前region: {current_region}")

        # 创建系统级别的profile用于调用OpenStack API
        system_token = current_session.get_token()

        servers = []

        try:
            LOG.debug(f"正在获取当前region: {current_region} 的虚机信息")

            # 获取当前region的所有虚机
            region_servers = await nova.list_servers(
                profile=profile,
                session=current_session,
                global_request_id=x_openstack_request_id,
            )
            LOG.debug(f"当前region {current_region} 获取到 {len(region_servers)} 个虚机")

            # 处理虚机信息
            for server in region_servers:
                try:
                    # nova.list_servers返回的是NovaServer对象，直接访问属性
                    server_record = {
                        'id': server.id,  # OpenStack server ID
                        'name': server.name,
                        'status': server.status,
                        'region': current_region,
                        'third_server_id': server.id  # 用于Prometheus查询
                    }
                    servers.append(server_record)

                except Exception as e:
                    LOG.warning(f"处理虚机信息时出错: {str(e)}")
                    continue

        except Exception as e:
            LOG.error(f"获取当前region {current_region} 的虚机信息时出错: {str(e)}", exc_info=True)
            # 不使用continue，因为只有一个region

        LOG.info(f"从OpenStack API总共获取到 {len(servers)} 个虚机")
        return servers

    except Exception as e:
        LOG.error(f"从OpenStack API获取虚机列表时出错: {str(e)}", exc_info=True)
        return []


async def _get_vm_cpu_usage_from_prometheus(vm_uuid: str, prometheus_url: str) -> float:
    """从Prometheus获取虚拟机CPU使用率（5分钟内数据）"""
    try:
        # 使用与get_vm_metrics相同的查询方式，获取5分钟内的CPU使用率
        cpu_query = f"avg(irate(libvirt_domain_info_cpu_time_seconds_total{{instanceId='{vm_uuid}',job=~'libvirt_exporter'}}[300s])/libvirt_domain_info_virtual_cpus)*100"
        cpu_url = f"{prometheus_url}/api/v1/query?query={cpu_query}"

        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

        LOG.debug(f"查询虚机 {vm_uuid} 的CPU使用率: {cpu_url}")

        async with aiohttp.ClientSession() as session:
            async with session.get(cpu_url, headers=headers, timeout=aiohttp.ClientTimeout(total=30)) as response:
                if response.status == 200:
                    result = await response.json()

                    # 处理CPU结果
                    cpu_data = result.get("data", {})
                    if cpu_data.get("result"):
                        try:
                            cpu_value = round(float(cpu_data["result"][0]["value"][1]), 3)
                            LOG.debug(f"虚机 {vm_uuid} CPU使用率: {cpu_value}%")
                            return cpu_value
                        except (IndexError, ValueError, KeyError) as ex:
                            LOG.warning(f"解析虚机 {vm_uuid} CPU数据失败: {ex}")
                            return None
                    else:
                        LOG.warning(f"虚机 {vm_uuid} 无CPU监控数据")
                        return None
                else:
                    LOG.error(f"查询虚机 {vm_uuid} CPU使用率失败，HTTP状态码: {response.status}")
                    return None

    except Exception as e:
        LOG.error(f"获取虚机 {vm_uuid} CPU使用率时出错: {str(e)}", exc_info=True)
        return None


async def _get_vm_memory_usage_from_prometheus(vm_uuid: str, prometheus_url: str) -> float:
    """从Prometheus获取虚拟机内存使用率（5分钟内数据）"""
    try:
        # 使用与get_vm_metrics相同的查询方式，获取5分钟内的内存使用率
        # 内存使用率 = (最大内存 - 未使用内存) / 最大内存 * 100
        memory_query = f"(libvirt_domain_info_maximum_memory_bytes{{instanceId='{vm_uuid}',job=~'libvirt_exporter'}}-libvirt_domain_stat_memory_unused_bytes{{instanceId='{vm_uuid}',job=~'libvirt_exporter'}})/libvirt_domain_info_maximum_memory_bytes{{instanceId='{vm_uuid}',job=~'libvirt_exporter'}}*100"
        memory_url = f"{prometheus_url}/api/v1/query?query={memory_query}"

        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

        LOG.debug(f"查询虚机 {vm_uuid} 的内存使用率: {memory_url}")

        async with aiohttp.ClientSession() as session:
            async with session.get(memory_url, headers=headers, timeout=aiohttp.ClientTimeout(total=30)) as response:
                if response.status == 200:
                    result = await response.json()

                    # 处理内存结果
                    memory_data = result.get("data", {})
                    if memory_data.get("result"):
                        try:
                            memory_value = round(float(memory_data["result"][0]["value"][1]), 3)
                            LOG.debug(f"虚机 {vm_uuid} 内存使用率: {memory_value}%")
                            return memory_value
                        except (IndexError, ValueError, KeyError) as ex:
                            LOG.warning(f"解析虚机 {vm_uuid} 内存数据失败: {ex}")
                            return None
                    else:
                        LOG.warning(f"虚机 {vm_uuid} 无内存监控数据")
                        return None
                else:
                    LOG.error(f"查询虚机 {vm_uuid} 内存使用率失败，HTTP状态码: {response.status}")
                    return None

    except Exception as e:
        LOG.error(f"获取虚机 {vm_uuid} 内存使用率时出错: {str(e)}", exc_info=True)
        return None


async def check_guestos_cpu_used_percent(history_id: int, category_id: int, x_openstack_request_id: str, profile: schemas.Profile):
    """GuestOS CPU利用率检测 - 使用OpenStack API获取虚机列表，Prometheus获取CPU使用率"""
    try:
        LOG.debug(f"开始执行GuestOS CPU利用率检测，history_id: {history_id}, category_id: {category_id}")

        data = 0
        info = None
        error = 0
        warn = 0

        # 获取监控阈值设置
        db = db_api.DB.get()

        settings_query = select(SysSafeHealthCheckSettings).where(
            and_(
                SysSafeHealthCheckSettings.c.category_code == 'GUESTOS_CPU_USED_PERCENT',
                SysSafeHealthCheckSettings.c.deleted == 1
            )
        )

        settings = []
        async with db.transaction():
            result = await db.fetch_all(settings_query)
            if result:
                settings = [dict(s) for s in result]

        if settings:
            min_threshold = settings[0].get('min')
            max_threshold = settings[0].get('max')
            # 使用OpenStack API获取虚机列表（替代数据库查询）
            servers = await _get_servers_from_openstack_api(x_openstack_request_id, profile)

            LOG.debug(f"从OpenStack API获取到 {len(servers)} 个虚机")

            if servers:
                for server in servers:
                    instance_id = server.get('third_server_id')
                    prometheus_url = CONF.default.prometheus_endpoint

                    if not instance_id or not prometheus_url:
                        LOG.warning(f"虚机 {instance_id} 缺少必要信息，跳过检查")
                        continue

                    try:
                        # 使用Prometheus获取5分钟内的CPU使用率（替代数据库查询）
                        cpu_percent = await _get_vm_cpu_usage_from_prometheus(instance_id, prometheus_url)
                        if cpu_percent is not None:

                            if (min_threshold is not None and max_threshold is not None):
                                cpu_state = None

                                if cpu_percent > max_threshold:
                                    error += 1
                                    cpu_state = -1
                                elif cpu_percent > min_threshold:
                                    warn += 1
                                    cpu_state = 1

                                # 需要获取数据库中的server_id来保存记录
                                if cpu_state is not None:
                                    # 直接使用OpenStack server ID作为server_id，不需要查询数据库
                                    server_id = instance_id  # instance_id就是OpenStack的server UUID
                                    await _save_or_update_guest_check_info(
                                        db, history_id, server_id,
                                        cpu_state=cpu_state,
                                        cpu_percent=cpu_percent
                                    )
                                    LOG.debug(f"保存虚机 {instance_id} CPU检查记录: CPU使用率={cpu_percent}%")
                        else:
                            LOG.warning(f"虚机 {instance_id} 无法获取CPU使用率数据")

                    except Exception as e:
                        LOG.error(f"处理虚机 {instance_id} CPU监控数据时出现异常: {str(e)}", exc_info=True)
                        continue

        # 根据检查结果设置返回状态（与Java代码逻辑一致）
        if error > 0:
            info = f"GuestOS虚机CPU利用情况检测时，共有{error}台虚机CPU使用率处于异常状态"
            if warn > 0:
                info += f";共有{warn}台虚机CPU使用率处于警告状态"
            data = -1
        elif warn > 0:
            info = f"GuestOS虚机CPU利用情况检测时，共有{warn}台虚机CPU使用率处于警告状态"
            data = 1

        if info:
            info += "，详细信息请查看巡检报告。"

        return data, info

    except Exception as e:
        LOG.error(f"GuestOS CPU利用率检测时出现异常: {str(e)}", exc_info=True)
        return -1, str(e)


async def check_guestos_disk_used_percent(history_id: int, category_id: int):
    """GuestOS分区利用率检测"""
    try:
        # 根据原Java代码，这个函数目前只是一个占位符实现
        # 实际的磁盘利用率检测需要更复杂的逻辑来获取虚机内部的磁盘使用情况
        return 0, "虚机分区利用率检测功能待实现"
    except Exception as e:
        return -1, f"检查虚机分区利用率时出错: {str(e)}"


async def check_network_status(history_id: int, category_id: int):
    """连通性检测"""
    try:
        # 根据原Java代码，这个函数目前只是一个占位符实现
        # 实际的连通性检测需要实现网络连通性测试逻辑
        return 0, "网络连通性检测功能待实现"
    except Exception as e:
        return -1, f"检查网络连通性时出错: {str(e)}"


async def check_network_route(history_id: int, category_id: int):
    """虚拟路由状态检测"""
    data = 0
    info = None

    try:
        LOG.info(f"开始检查虚拟路由状态，history_id: {history_id}, category_id: {category_id}")

        from skyline_apiserver.client.utils import get_system_session
        from skyline_apiserver.client.openstack import neutron, system

        # 获取系统级别的session
        system_session = get_system_session()

        # 获取当前region（不遍历所有regions，只使用当前region）
        current_region = "RegionOne"  # 使用默认region，或从配置中获取
        LOG.debug(f"使用当前region: {current_region}")

        all_routers = []
        route_check_info_list = []

        try:
            LOG.debug(f"正在检查当前region: {current_region} 的路由状态")

            # 调用neutron API获取路由列表
            routers_result = await neutron.list_routers(
                session=system_session,
                region_name=current_region,
                global_request_id="",
                retrieve_all=True
            )

            # 获取路由数据
            routers_data = routers_result.get("routers", [])
            LOG.debug(f"当前region {current_region} 获取到路由数量: {len(routers_data)}")

            # 处理每个路由
            for router_data in routers_data:
                try:
                    router_status = router_data.get("status")

                    # 按照原Java代码逻辑：检查status是否为null或不等于"ACTIVE"
                    if router_status is None or router_status != "ACTIVE":
                        LOG.warning(f"发现异常路由: id={router_data.get('id')}, status={router_status}, region={current_region}")

                        # 记录异常路由信息
                        route_check_info = {
                            "router_id": router_data.get("id"),
                            "project_id": router_data.get("project_id"),
                            "tenant_id": router_data.get("tenant_id"),
                            "region": current_region,
                            "history_id": history_id,
                            "status": router_status,
                            "name": router_data.get("name"),
                            "admin_state_up": router_data.get("admin_state_up"),
                            "external_gateway_info": router_data.get("external_gateway_info")
                        }
                        route_check_info_list.append(route_check_info)

                    all_routers.append(router_data)

                except Exception as e:
                    LOG.warning(f"处理路由数据时出错: {str(e)}")
                    continue

        except Exception as e:
            LOG.error(f"检查当前region {current_region} 路由状态时出错: {str(e)}", exc_info=True)
            # 不使用continue，因为只有一个region

        LOG.debug(f"总共检查路由数量: {len(all_routers)}")
        LOG.debug(f"发现异常路由数量: {len(route_check_info_list)}")

        # 按照原Java代码逻辑：如果异常路由列表size>0
        if len(route_check_info_list) > 0:
            data = -1
            info = f"路由检测时，共有{len(route_check_info_list)}个路由处于不可用状态，详细信息请查看巡检报告。"
            LOG.warning(info)

            # TODO: 这里应该保存异常路由信息到数据库
            # sysSafeNetworkRouteCheckInfoService.saveBatch(routeCheckInfoEntityList);
        else:
            info = "所有路由状态正常"
            LOG.info(info)

        LOG.info(f"路由状态检查完成，结果: data={data}, info={info}")

        return data, info

    except Exception as e:
        LOG.error(f"检查虚拟路由状态时出现未知错误: {str(e)}", exc_info=True)
        data = -1
        info = str(e)

        return data, info


async def check_network_gateway(history_id: int, category_id: int):
    """虚拟网关状态检测"""
    data = 0
    info = None

    try:
        LOG.info(f"开始检查虚拟网关状态，history_id: {history_id}, category_id: {category_id}")

        from skyline_apiserver.client.utils import get_system_session
        from skyline_apiserver.client.openstack import neutron, system

        # 获取系统级别的session
        system_session = get_system_session()

        # 获取当前region（不遍历所有regions，只使用当前region）
        current_region = "RegionOne"  # 使用默认region，或从配置中获取
        LOG.debug(f"使用当前region: {current_region}")

        all_gateways = []
        gateway_check_info_list = []

        try:
            LOG.debug(f"正在检查当前region: {current_region} 的网关状态")

            # 调用neutron API获取路由列表
            routers_result = await neutron.list_routers(
                session=system_session,
                region_name=current_region,
                global_request_id="",
                retrieve_all=True
            )

            # 获取路由数据
            routers_data = routers_result.get("routers", [])
            LOG.debug(f"当前region {current_region} 获取到路由数量: {len(routers_data)}")

            # 处理每个路由器的外部网关
            for router_data in routers_data:
                try:
                    external_gateway_info = router_data.get("external_gateway_info")

                    # 如果路由器有外部网关配置，则检查网关状态
                    if external_gateway_info:
                        router_status = router_data.get("status")
                        admin_state_up = router_data.get("admin_state_up")

                        # 检查路由器状态和管理状态
                        # 网关异常条件：路由器状态不是ACTIVE或管理状态为False
                        if router_status != "ACTIVE" or not admin_state_up:
                            LOG.warning(f"发现异常网关: router_id={router_data.get('id')}, status={router_status}, admin_state_up={admin_state_up}, region={current_region}")

                            # 记录异常网关信息
                            gateway_check_info = {
                                "router_id": router_data.get("id"),
                                "project_id": router_data.get("project_id"),
                                "tenant_id": router_data.get("tenant_id"),
                                "region": current_region,
                                "history_id": history_id,
                                "status": router_status,
                                "admin_state_up": admin_state_up,
                                "name": router_data.get("name"),
                                "external_gateway_info": external_gateway_info,
                                "network_id": external_gateway_info.get("network_id") if external_gateway_info else None
                            }
                            gateway_check_info_list.append(gateway_check_info)

                        all_gateways.append({
                            "router_id": router_data.get("id"),
                            "name": router_data.get("name"),
                            "status": router_status,
                            "admin_state_up": admin_state_up,
                            "external_gateway_info": external_gateway_info,
                            "region": current_region
                        })

                except Exception as e:
                    LOG.warning(f"处理网关数据时出错: {str(e)}")
                    continue

        except Exception as e:
            LOG.error(f"检查当前region {current_region} 网关状态时出错: {str(e)}", exc_info=True)
            # 不使用continue，因为只有一个region

        LOG.debug(f"总共检查网关数量: {len(all_gateways)}")
        LOG.debug(f"发现异常网关数量: {len(gateway_check_info_list)}")

        # 按照原Java代码逻辑：如果异常网关列表size>0
        if len(gateway_check_info_list) > 0:
            data = -1
            info = f"网关检测时，共有{len(gateway_check_info_list)}个网关处于异常状态，详细信息请查看巡检报告。"
            LOG.warning(info)

            # TODO: 这里应该保存异常网关信息到数据库
            # sysSafeNetworkGetawayCheckInfoService.saveBatch(getawayCheckInfoEntityList);
        else:
            info = "所有网关状态正常"
            LOG.info(info)

        LOG.info(f"网关状态检查完成，结果: data={data}, info={info}")

        return data, info

    except Exception as e:
        LOG.error(f"检查虚拟网关状态时出现未知错误: {str(e)}", exc_info=True)
        data = -1
        info = str(e)

        return data, info


async def check_network_floatip(history_id: int, category_id: int):
    """浮动IP状态检测"""
    try:
        from skyline_apiserver.db import api as db_api
        from sqlalchemy import select, and_
        from sqlalchemy.sql import text

        # 使用数据库查询获取浮动IP状态
        db = db_api.DB.get()

        # 构建SQL查询，类似于Java代码中的MPJLambdaWrapper查询
        query = text("""
            SELECT f.*
            FROM sys_openstack_floating_ip f
            INNER JOIN sys_cloud c ON c.id = f.cloud_id
            INNER JOIN sys_openstack_project p ON p.id = f.project_id
            INNER JOIN sys_openstack_domain d ON d.id = p.domain_id
            WHERE f.deleted = 1
            AND c.deleted = 1
            AND p.deleted = 1
            AND d.deleted = 1
        """)

        # 执行查询
        floating_ips = []
        async with db.transaction():
            result = await db.fetch_all(query)
            if result:
                floating_ips = [dict(f) for f in result]

        # 如果没有浮动IP，直接返回正常状态
        if not floating_ips:
            return 0, "未发现任何浮动IP"

        # 检查浮动IP状态
        abnormal_ips = []
        for floating_ip in floating_ips:
            if floating_ip.get("status") != "ACTIVE":
                # 记录异常浮动IP信息
                abnormal_ip = {
                    "floating_ip_id": floating_ip.get("id"),
                    "floating_ip_address": floating_ip.get("floating_ip_address"),
                    "status": floating_ip.get("status"),
                    "project_id": floating_ip.get("project_id"),
                    "cloud_id": floating_ip.get("cloud_id")
                }
                abnormal_ips.append(abnormal_ip)

        # 如果有异常浮动IP，返回异常状态
        if abnormal_ips:
            return -1, f"浮动IP状态检测时，共有{len(abnormal_ips)}个浮动IP处于异常状态，详细信息请查看巡检报告。"
        else:
            return 0, "所有浮动IP状态正常"

    except Exception as e:
        return -1, f"检查浮动IP状态时出错: {str(e)}"


async def check_network_port(history_id: int, category_id: int):
    """端口状态检测"""
    data = 0
    info = None

    try:
        LOG.info(f"开始检查端口状态，history_id: {history_id}, category_id: {category_id}")

        from skyline_apiserver.client.utils import get_system_session
        from skyline_apiserver.client.openstack import neutron, system
        from skyline_apiserver.api.wrapper.skyline import Port

        # 获取系统级别的session
        system_session = get_system_session()

        # 获取当前region（不遍历所有regions，只使用当前region）
        current_region = "RegionOne"  # 使用默认region，或从配置中获取
        LOG.debug(f"使用当前region: {current_region}")

        all_ports = []
        port_check_info_list = []

        try:
            LOG.debug(f"正在检查当前region: {current_region} 的端口状态")

            # 调用neutron API获取端口列表
            ports_result = await neutron.list_ports(
                session=system_session,
                region_name=current_region,
                global_request_id="",
                retrieve_all=True
            )

            # 获取端口数据
            ports_data = ports_result.get("ports", [])
            LOG.debug(f"当前region {current_region} 获取到端口数量: {len(ports_data)}")

            # 处理每个端口
            for port_data in ports_data:
                try:
                    # 使用Port wrapper处理端口数据
                    port = Port(port_data).to_dict()
                    port_status = port.get("status")  # 注意：API返回的是status字段

                    # 按照原Java代码逻辑：检查status是否为null或不等于"ACTIVE"
                    # 注意：虽然原Java代码检查的是state字段，但API返回的是status字段
                    # 为了保持业务逻辑一致，我们检查status字段
                    if port_status is None or port_status != "ACTIVE":
                        LOG.warning(f"发现异常端口: id={port.get('id')}, status={port_status}, region={current_region}")

                        # 记录异常端口信息
                        port_check_info = {
                            "port_id": port.get("id"),
                            "project_id": port.get("project_id"),
                            "region": current_region,
                            "history_id": history_id,
                            "status": port_status,
                            "name": port.get("name"),
                            "network_id": port.get("network_id"),
                            "device_owner": port.get("device_owner"),
                            "device_id": port.get("device_id")
                        }
                        port_check_info_list.append(port_check_info)

                    all_ports.append(port)

                except Exception as e:
                    LOG.warning(f"处理端口数据时出错: {str(e)}")
                    continue

        except Exception as e:
            LOG.error(f"检查当前region {current_region} 端口状态时出错: {str(e)}", exc_info=True)
            # 不使用continue，因为只有一个region

        LOG.debug(f"总共检查端口数量: {len(all_ports)}")
        LOG.debug(f"发现异常端口数量: {len(port_check_info_list)}")

        # 按照原Java代码逻辑：如果异常端口列表size>0
        if len(port_check_info_list) > 0:
            data = -1
            info = f"端口检测时，共有{len(port_check_info_list)}个端口处于异常状态，详细信息请查看巡检报告。"
            LOG.warning(info)

            # TODO: 这里应该保存异常端口信息到数据库
            # sysSafeNetworkPortCheckInfoService.saveBatch(portCheckInfoEntityList);
        else:
            info = "所有端口状态正常"
            LOG.info(info)

        LOG.info(f"端口状态检查完成，结果: data={data}, info={info}")

        return data, info

    except Exception as e:
        LOG.error(f"检查端口状态时出现未知错误: {str(e)}", exc_info=True)
        data = -1
        info = str(e)

        return data, info


async def check_network_http(history_id: int, category_id: int):
    """基于响应内容的健康检查 NETWORK_HTTP"""
    data = 0
    info = None

    try:
        from skyline_apiserver.db import api as db_api
        from sqlalchemy.sql import text
        import aiohttp
        import asyncio

        LOG.info(f"开始执行基于响应内容的健康检查，history_id: {history_id}, category_id: {category_id}")

        # 查询需要检查的主机配置
        db = db_api.DB.get()

        # 构建SQL查询，获取服务器巡检设置（不过滤category_code，与Java代码一致）
        query = text("""
            SELECT *
            FROM sys_safe_server_inspection_settings s
            WHERE s.deleted = 1
        """)

        # 执行查询获取设置
        settings_entity_list = []
        async with db.transaction():
            result = await db.fetch_all(query)
            if result:
                settings_entity_list = [dict(s) for s in result]

        if settings_entity_list and len(settings_entity_list) > 0:
            # 存在配置针对每台虚机进行检测
            check_info_entity_list = []

            for settings_entity in settings_entity_list:
                check_info = ""
                server_inspection_check_info_entity = {
                    'history_id': history_id,
                    'server_id': settings_entity.get('server_id')
                }

                # 判断使用的是post请求还是get请求
                result = None
                url = settings_entity.get('url')
                url_method = settings_entity.get('url_method', '').lower()

                if not url:
                    LOG.warning(f"服务器 {settings_entity.get('server_id')} 的URL配置为空")
                    continue

                try:
                    timeout = aiohttp.ClientTimeout(total=5)
                    async with aiohttp.ClientSession(timeout=timeout) as session:
                        if url_method == "post":
                            # 发送post请求
                            async with session.post(url) as response:
                                result = await response.text()
                        else:
                            # 默认使用get请求
                            async with session.get(url) as response:
                                result = await response.text()

                    LOG.debug(f"HTTP请求成功，URL: {url}, 响应长度: {len(result) if result else 0}")

                except Exception as e:
                    LOG.error(f"HTTP请求失败，URL: {url}, 错误: {str(e)}")
                    result = None

                if result and result.strip():
                    # 判断是精确匹配还是模糊匹配
                    mate_method = settings_entity.get('mate_method', '')
                    relate_key = settings_entity.get('relate_key', '')
                    exclude_key = settings_entity.get('exclude_key', '')

                    if mate_method == "fuzzy":
                        # 模糊匹配
                        temp = False
                        if relate_key and relate_key.strip():
                            relates = relate_key.split(",")
                            for relate in relates:
                                if relate.strip() and relate.strip() not in result:
                                    temp = True
                                    check_info += f"关键词验证，返回结果不包含关键词：{relate.strip()};"

                        if not temp and exclude_key and exclude_key.strip():
                            excludes = exclude_key.split(",")
                            for exclude in excludes:
                                if exclude.strip() and exclude.strip() in result:
                                    check_info += f"排除关键词验证，返回结果包含排除关键词：{exclude.strip()};"
                                    temp = True

                        if temp:
                            server_inspection_check_info_entity['state'] = -1
                            server_inspection_check_info_entity['check_info'] = check_info
                    else:
                        # 精确匹配
                        temp = False
                        if relate_key and relate_key.strip():
                            relates = relate_key.split(",")
                            for relate in relates:
                                if relate.strip() and result != relate.strip():
                                    temp = True
                                    check_info += f"关键词验证，返回结果不等于关键词：{relate.strip()};"

                        if not temp and exclude_key and exclude_key.strip():
                            excludes = exclude_key.split(",")
                            for exclude in excludes:
                                if exclude.strip() and result == exclude.strip():
                                    check_info += f"排除关键词验证，返回结果等于排除关键词：{exclude.strip()};"
                                    temp = True

                        if temp:
                            server_inspection_check_info_entity['state'] = -1
                            server_inspection_check_info_entity['check_info'] = check_info
                else:
                    server_inspection_check_info_entity['state'] = -1
                    server_inspection_check_info_entity['check_info'] = "请求验证返回结果为空"

                check_info_entity_list.append(server_inspection_check_info_entity)

            if len(check_info_entity_list) > 0:
                # 批量保存检查信息到数据库
                try:
                    # 这里应该调用批量保存服务器巡检检查信息的方法
                    # 由于没有对应的表和API，暂时记录日志
                    LOG.info(f"需要保存 {len(check_info_entity_list)} 条服务器巡检检查信息")
                    for check_info in check_info_entity_list:
                        if check_info.get('state') == -1:
                            LOG.warning(f"服务器 {check_info.get('server_id')} 检查异常: {check_info.get('check_info')}")
                except Exception as e:
                    LOG.error(f"保存服务器巡检检查信息失败: {str(e)}")

                data = -1
                info = f"基于响应内容巡检，共检测虚机：{len(settings_entity_list)}台，其中存在异常虚机虚机：{len(check_info_entity_list)}台，详细信息请查看巡检报告。"

        LOG.info(f"基于响应内容的健康检查完成，结果: {data}, 信息: {info}")
        return data, info

    except Exception as e:
        data = -1
        info = f"{str(e)}程序异常"
        LOG.error(f"基于响应内容的健康检查时出现异常: {str(e)}", exc_info=True)
        return data, info


@router.post(
    "/security/create-health-check",
    description="创建健康巡检",
    responses={
        200: {"model": schemas.Message},
        401: {"model": schemas.UnauthorizedMessage},
        403: {"model": schemas.ForbiddenMessage},
    },
    status_code=status.HTTP_200_OK,
    response_description="OK",
)
async def create_health_check(
    profile: schemas.Profile = Depends(deps.get_profile_update_jwt),
) -> Any:
    """
    创建健康巡检
    """
    assert_system_admin(profile=profile, exception="Not allowed to create health check.")
    
    # 创建巡检记录
    result = await db_api.create_health_check_history(
        # user_id=profile.id,
        username=profile.user.name
    )
    
    if result is None:
        return {"code": 500, "msg": "Failed to create health check."}
    
    return {"data": result}


@router.post(
    "/security/update-health-check-state",
    description="更新健康巡检状态",
    responses={
        200: {"model": schemas.Message},
        401: {"model": schemas.UnauthorizedMessage},
        403: {"model": schemas.ForbiddenMessage},
        404: {"model": schemas.NotFoundMessage},
    },
    status_code=status.HTTP_200_OK,
    response_description="OK",
)
async def update_health_check_state(
    id: int,
    state: int,
    profile: schemas.Profile = Depends(deps.get_profile_update_jwt),
) -> Any:
    """
    更新健康巡检状态
    """
    assert_system_admin(profile=profile, exception="Not allowed to update health check state.")
    
    result = await db_api.update_health_check_state(id, state)
    
    if result:
        return {"data": True}
    else:
        return {"data": False}


@router.get(
    "/security/category-list",
    description="获取巡检类别列表",
    responses={
        200: {"model": schemas.Message},
        401: {"model": schemas.UnauthorizedMessage},
    },
    status_code=status.HTTP_200_OK,
    response_description="OK",
)
async def get_category_list(
    profile: schemas.Profile = Depends(deps.get_profile_update_jwt),
) -> Any:
    """
    获取巡检类别列表
    """
    categories = await db_api.get_health_check_category_list()
    return {"data": categories}


@router.get(
    "/security/health-check-result",
    description="巡检结果集合查询",
    responses={
        200: {"model": schemas.Message},
        401: {"model": schemas.UnauthorizedMessage},
    },
    status_code=status.HTTP_200_OK,
    response_description="OK",
)
async def get_health_check_result(
    history_id: int,
    profile: schemas.Profile = Depends(deps.get_profile_update_jwt),
) -> Any:
    """
    巡检结果集合查询

    对应 Java 版本的 SysSafeController.selectHealthCheckResult 方法
    返回包含父子类别层级结构和检查结果的数据

    Args:
        history_id: 巡检历史ID

    Returns:
        包含巡检结果的响应，格式与 Java 版本完全一致
    """
    try:
        # 验证系统管理员权限
        assert_system_admin(profile=profile, exception="Not allowed to query health check results.")

        LOG.info(f"开始查询巡检结果集合，history_id: {history_id}")

        # 调用数据库API获取结果列表
        # 这与 Java 版本的 sysSafeHealthCheckCategoryService.selectHealthCheckResult(historyId) 逻辑一致
        result_list = await db_api.get_health_check_result_list(history_id)

        LOG.info(f"巡检结果集合查询完成，返回 {len(result_list)} 个父类别")
        LOG.debug(f"查询结果详情: {result_list}")

        # 返回与 Java 版本相同的数据格式
        return {"data": result_list}

    except Exception as e:
        LOG.error(f"查询巡检结果集合失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询巡检结果集合失败: {str(e)}"
        )


@router.get(
    "/security/health-check-result-by-history-and-category",
    description="根据巡检记录ID和检测项目ID查询检测项目巡检结果",
    responses={
        200: {"model": schemas.Message},
        401: {"model": schemas.UnauthorizedMessage},
        404: {"model": schemas.NotFoundMessage},
    },
    status_code=status.HTTP_200_OK,
    response_description="OK",
)
async def get_health_check_result_by_history_and_category(
    history_id: int,
    category_id: int,
    profile: schemas.Profile = Depends(deps.get_profile_update_jwt),
) -> Any:
    """
    根据巡检记录ID和检测项目ID查询检测项目巡检结果
    """
    if history_id is None or category_id is None:
        return {"code": 400, "msg": "History ID and Category ID are required."}
    
    result = await db_api.get_health_check_result_by_history_id_and_category_id(history_id, category_id)
    
    return {"data": result}


@router.get(
    "/security/health-check-history-list",
    description="巡检记录列表查询",
    responses={
        200: {"model": schemas.Message},
        401: {"model": schemas.UnauthorizedMessage},
    },
    status_code=status.HTTP_200_OK,
    response_description="OK",
)
async def get_health_check_history_list(
    page_index: int = Query(1, alias="pageIndex"),
    page_size: int = Query(10, alias="pageSize"),
    profile: schemas.Profile = Depends(deps.get_profile_update_jwt),
) -> Any:
    """
    巡检记录列表查询
    """
    result = await db_api.get_health_check_history_list(page_index, page_size)
    return {"data": result}


@router.get(
    "/security/download-result",
    description="下载巡检结果",
    responses={
        200: {"content": {"application/vnd.ms-excel": {}}},
        401: {"model": schemas.UnauthorizedMessage},
        404: {"model": schemas.NotFoundMessage},
    },
    status_code=status.HTTP_200_OK,
    response_description="OK",
)
async def download_result(
    history_id: int,
    profile: schemas.Profile = Depends(deps.get_profile_update_jwt),
) -> Any:
    """
    下载巡检结果
    """
    assert_system_admin(profile=profile, exception="Not allowed to download health check result.")
    
    try:
        import openpyxl
        from openpyxl.styles import Alignment, Font, PatternFill, Border, Side
        from openpyxl.utils import get_column_letter
        import datetime
        
        # 创建工作簿和工作表
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "健康巡检结果"
        
        # 设置标题样式
        title_font = Font(name='宋体', size=14, bold=True)
        title_alignment = Alignment(horizontal='center', vertical='center')
        title_fill = PatternFill(fill_type='solid', fgColor='DDEBF7')
        
        # 设置表头样式
        header_font = Font(name='宋体', size=11, bold=True)
        header_alignment = Alignment(horizontal='center', vertical='center')
        header_fill = PatternFill(fill_type='solid', fgColor='DDEBF7')
        
        # 设置单元格边框
        thin_border = Border(
            left=Side(style='thin'), 
            right=Side(style='thin'), 
            top=Side(style='thin'), 
            bottom=Side(style='thin')
        )
        
        # 获取巡检历史记录
        history = await db_api.get_health_check_history_by_id(history_id)
        if not history:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Health check history not found"
            )
        
        # 获取巡检结果统计
        statistics = await db_api.statistics_health_check_result(history_id)
        
        # 获取巡检结果详情
        results = await db_api.get_health_check_result_list(history_id)
        
        # 设置标题
        ws.merge_cells('A1:G1')
        ws['A1'] = f"健康巡检报告 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ws['A1'].font = title_font
        ws['A1'].alignment = title_alignment
        ws['A1'].fill = title_fill
        
        # 设置巡检信息
        ws['A2'] = "巡检ID"
        ws['B2'] = history.get("id", "")
        ws['C2'] = "巡检时间"
        ws['D2'] = history.get("create_time", "")
        ws['E2'] = "巡检状态"
        
        state = history.get("state", 0)
        state_text = "未开始"
        if state == 1:
            state_text = "进行中"
        elif state == 2:
            state_text = "已完成"
        elif state == -1:
            state_text = "异常"
        ws['F2'] = state_text
        
        # 设置统计信息
        ws['A3'] = "检查项总数"
        ws['B3'] = sum(stat.get("count", 0) for stat in statistics)
        ws['C3'] = "正常项数"
        normal_count = next((stat.get("count", 0) for stat in statistics if stat.get("result", 0) == 0), 0)
        ws['D3'] = normal_count
        ws['E3'] = "异常项数"
        error_count = next((stat.get("count", 0) for stat in statistics if stat.get("result", 0) == -1), 0)
        ws['F3'] = error_count
        
        # 设置表头
        headers = ["序号", "检查类别", "检查项", "检查结果", "异常详情", "检查时间"]
        for col, header in enumerate(headers, start=1):
            cell = ws.cell(row=5, column=col)
            cell.value = header
            cell.font = header_font
            cell.alignment = header_alignment
            cell.fill = header_fill
            cell.border = thin_border
        
        # 设置数据
        row = 6
        for i, result in enumerate(results, start=1):
            category = result.get("category", {})
            category_name = category.get("name", "")
            category_code = category.get("code", "")
            
            result_items = result.get("result_items", [])
            if not result_items:
                # 如果没有检查结果项，添加一个空行
                ws.cell(row=row, column=1).value = i
                ws.cell(row=row, column=2).value = category_name
                ws.cell(row=row, column=3).value = category_code
                ws.cell(row=row, column=4).value = "未检查"
                ws.cell(row=row, column=5).value = ""
                ws.cell(row=row, column=6).value = ""
                
                # 设置单元格边框
                for col in range(1, 7):
                    ws.cell(row=row, column=col).border = thin_border
                
                row += 1
                continue
            
            for j, item in enumerate(result_items):
                ws.cell(row=row, column=1).value = i if j == 0 else ""
                ws.cell(row=row, column=2).value = category_name if j == 0 else ""
                ws.cell(row=row, column=3).value = category_code
                
                result_value = item.get("result", 0)
                result_text = "正常"
                if result_value == 1:
                    result_text = "警告"
                elif result_value == -1:
                    result_text = "异常"
                
                ws.cell(row=row, column=4).value = result_text
                ws.cell(row=row, column=5).value = item.get("detail", "")
                ws.cell(row=row, column=6).value = item.get("create_time", "")
                
                # 设置单元格边框
                for col in range(1, 7):
                    ws.cell(row=row, column=col).border = thin_border
                
                row += 1
        
        # 调整列宽
        for col in range(1, 7):
            ws.column_dimensions[get_column_letter(col)].width = 20
        
        # 创建临时文件并保存
        fd, path = tempfile.mkstemp(suffix=".xlsx")
        os.close(fd)
        wb.save(path)
        
        return FileResponse(
            path=path,
            filename=f"health_check_result_{history_id}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx",
            media_type="application/vnd.ms-excel"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate health check report: {str(e)}"
        )


@router.get(
    "/security/health-check-setting-list",
    description="查询巡检配置集合",
    responses={
        200: {"model": schemas.Message},
        401: {"model": schemas.UnauthorizedMessage},
    },
    status_code=status.HTTP_200_OK,
    response_description="OK",
)
async def get_health_check_setting_list(
    profile: schemas.Profile = Depends(deps.get_profile_update_jwt),
) -> Any:
    """
    查询巡检配置集合
    """
    settings_list, task = await db_api.get_health_check_settings()
    
    result = {
        "setingsList": settings_list,
        "taskBean": task
    }
    
    return {"data": result}


@router.post(
    "/security/save-settings",
    description="设置巡检配置",
    responses={
        200: {"model": schemas.Message},
        401: {"model": schemas.UnauthorizedMessage},
        403: {"model": schemas.ForbiddenMessage},
    },
    status_code=status.HTTP_200_OK,
    response_description="OK",
)
async def save_settings(
    settings_list: List[schemas.SysSafeHealthCheckSettingsBase],
    isopen: Optional[int] = Query(None, description="是否开启定时任务"),
    cron: Optional[str] = Query(None, description="cron表达式"),
    profile: schemas.Profile = Depends(deps.get_profile_update_jwt),
) -> Any:
    """
    设置巡检配置
    完全按照Java实现的逻辑：saveSetings(@RequestBody List<SysSafeHealthCheckSettingsEntity> list,Integer isopen,String corn)
    """
    try:
        LOG.info(f"开始保存巡检配置，用户: {profile.user.name}, 设置数量: {len(settings_list)}")

        assert_system_admin(profile=profile, exception="Not allowed to save settings.")

        # 将settings转换为列表字典，完全按照Java实体类字段
        settings_data = []
        for setting in settings_list:
            settings_data.append({
                "category_id": setting.category_id,
                "category_code": setting.category_code,
                "min": setting.min,
                "max": setting.max,
                "deleted": 1  # 新增记录默认为未删除状态
            })

        LOG.info(f"转换后的设置列表: {settings_data}")
        LOG.info(f"isopen参数: {isopen}, cron参数: {cron}")

        # 调用数据库API保存设置，包含完整的业务逻辑
        result = await db_api.save_health_check_settings(
            settings=settings_data,
            is_open=isopen,
            cron=cron
        )

        LOG.info(f"巡检配置保存成功，结果: {result}")

        return {"data": result}

    except Exception as e:
        LOG.error(f"保存巡检配置失败: {str(e)}")
        import traceback
        LOG.error(f"错误详情: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"保存巡检配置失败: {str(e)}"
        )


@router.get(
    "/security/statistics-health-result",
    description="巡检结果统计查询",
    responses={
        200: {"description": "巡检结果统计数据"},
        401: {"model": schemas.UnauthorizedMessage},
        403: {"model": schemas.ForbiddenMessage},
        404: {"model": schemas.NotFoundMessage},
    },
    status_code=status.HTTP_200_OK,
    response_description="OK",
)
async def statistics_health_result(
    history_id: int = Query(..., description="巡检历史ID"),
    profile: schemas.Profile = Depends(deps.get_profile_update_jwt),
    x_openstack_request_id: str = Header(
        "",
        alias=constants.INBOUND_HEADER,
        regex=constants.INBOUND_HEADER_REGEX,
    ),
) -> Any:
    """
    巡检结果统计查询

    对应 Java 版本的 SysSafeController.statisticsHealthResult 方法
    返回按状态分组的巡检结果统计信息

    Args:
        history_id: 巡检历史ID

    Returns:
        包含统计结果的响应，格式：
        {
            "data": [
                {"result": "-1", "sum_num": "异常数量"},
                {"result": "0", "sum_num": "正常数量"},
                {"result": "1", "sum_num": "警告数量"}
            ]
        }
    """
    try:
        # 验证系统管理员权限
        assert_system_admin(profile=profile, exception="Not allowed to query health check statistics.")

        LOG.info(f"开始查询巡检结果统计，history_id: {history_id}")

        # 调用数据库API获取统计结果
        # 这与 Java 版本的 sysSafeHealthCheckResultService.statisticsHealthCheckResult(historyId) 逻辑一致
        statistics_list = await db_api.statistics_health_result_by_status(history_id)

        LOG.info(f"巡检结果统计查询完成，返回 {len(statistics_list)} 条记录")
        LOG.debug(f"统计结果详情: {statistics_list}")

        # 返回与 Java 版本相同的数据格式
        return {"data": statistics_list}

    except Exception as e:
        LOG.error(f"查询巡检结果统计失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询巡检结果统计失败: {str(e)}"
        )