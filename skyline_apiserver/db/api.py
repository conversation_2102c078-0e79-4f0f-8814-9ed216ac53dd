# Copyright 2021 99cloud
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from __future__ import annotations

import datetime
import json
import time
from functools import wraps
from typing import Any, Union
import uuid

from sqlalchemy import Insert, Update, and_, delete, func, insert, select, update

from skyline_apiserver.types import Fn
from skyline_apiserver import schemas

from .base import DB, inject_db
from .models import RevokedToken, Settings, UploadedFile, ApsLock, Captcha, OtherSetting, SysSafeHealthCheckHistory, SysSafeHealthCheckCategory, SysSafeHealthCheckResult, SysSafeHealthCheckSettings, SysSafeHealthScheduledTask, SysSafeHostCheckPacketLoss, SysSafeHostCheckInfo
from skyline_apiserver import schemas


def check_db_connected(fn: Fn) -> Any:
    @wraps(fn)
    async def wrapper(*args: Any, **kwargs: Any) -> Any:
        await inject_db()
        db = DB.get()
        assert db is not None, "Database is not connected."
        return await fn(*args, **kwargs)

    return wrapper


@check_db_connected
async def check_token(token_id: str) -> bool:
    count_label = "revoked_count"
    query = (
        select(func.count(RevokedToken.c.uuid).label(count_label))
        .select_from(RevokedToken)
        .where(RevokedToken.c.uuid == token_id)
    )
    db = DB.get()
    async with db.transaction():
        result = await db.fetch_one(query)

    count = getattr(result, count_label, 0)
    return count > 0


@check_db_connected
async def revoke_token(token_id: str, expire: int) -> Any:
    query = insert(RevokedToken)
    db = DB.get()
    async with db.transaction():
        result = await db.execute(query, {"uuid": token_id, "expire": expire})

    return result


@check_db_connected
async def purge_revoked_token() -> Any:
    now = int(time.time()) - 1
    query = delete(RevokedToken).where(RevokedToken.c.expire < now)
    db = DB.get()
    async with db.transaction():
        result = await db.execute(query)

    return result


@check_db_connected
async def list_settings() -> Any:
    query = select(Settings)
    db = DB.get()
    async with db.transaction():
        result = await db.fetch_all(query)

    return result


@check_db_connected
async def get_setting(key: str) -> Any:
    query = select(Settings).where(Settings.c.key == key)
    db = DB.get()
    async with db.transaction():
        result = await db.fetch_one(query)

    return result


@check_db_connected
async def update_setting(key: str, value: Any) -> Any:
    get_query = (
        select(Settings.c.key, Settings.c.value).where(Settings.c.key == key).with_for_update()
    )
    db = DB.get()
    async with db.transaction():
        is_exist = await db.fetch_one(get_query)
        stmt: Union[Insert, Update]
        if is_exist is None:
            stmt = insert(Settings).values(key=key, value=value)
        else:
            stmt = update(Settings).where(Settings.c.key == key).values(value=value)
        await db.execute(stmt)
        result = await db.fetch_one(get_query)

    return result


@check_db_connected
async def delete_setting(key: str) -> Any:
    query = delete(Settings).where(Settings.c.key == key)
    db = DB.get()
    async with db.transaction():
        result = await db.execute(query)

    return result


@check_db_connected
async def get_uploaded_file_info(user: str) -> Any:
    query = select(UploadedFile).where(UploadedFile.c.user ==
                                       user).where(UploadedFile.c.status == 0).order_by(UploadedFile.c.id.desc())
    db = DB.get()
    async with db.transaction():
        result = await db.fetch_one(query)

    return result


@check_db_connected
async def get_task(user: str, status: int) -> Any:
    query = select(UploadedFile).where(UploadedFile.c.user == user,
                                       UploadedFile.c.status == status).order_by(UploadedFile.c.id.desc())
    db = DB.get()
    async with db.transaction():
        result = await db.fetch_one(query)

    return result


@check_db_connected
async def get_file_info(user: str, key: str, status: int) -> Any:
    query = select(UploadedFile).where(UploadedFile.c.user == user,
                                       UploadedFile.c.key == key, UploadedFile.c.status == status)
    db = DB.get()
    async with db.transaction():
        result = await db.fetch_one(query)

    return result


@check_db_connected
async def create_file(user: str, chunk_info: schemas.ChunkInfo) -> Any:
    query = insert(UploadedFile)
    db = DB.get()
    async with db.transaction():
        result = await db.execute(query, {"key": chunk_info.key,
                                          "size": chunk_info.total_size,
                                          "chunk_size": chunk_info.official_chunk_size,
                                          "image_body": chunk_info.image_body,
                                          "user": user,
                                          "status": 0,
                                          "progress": 0,
                                          })

    return result


@check_db_connected
async def update_file_info(id: int, chunk_data: dict, ceph_upload_id: str) -> Any:
    db = DB.get()
    async with db.transaction():
        stmt = update(UploadedFile).where(UploadedFile.c.id ==
                                          id).values(chunk_list=json.dumps(chunk_data), ceph_upload_id=ceph_upload_id)
        await db.execute(stmt)

    return True


@check_db_connected
async def update_status(id: int, status: int) -> Any:
    db = DB.get()
    async with db.transaction():
        stmt = update(UploadedFile).where(UploadedFile.c.id == id).values(status=status)
        await db.execute(stmt)

    return True


@check_db_connected
async def update_lock(id: int, lock: int):
    db = DB.get()
    async with db.transaction():
        stmt = update(UploadedFile).where(UploadedFile.c.id == id).values(lock=lock)
        await db.execute(stmt)


@check_db_connected
async def update_progress(id: int, progress: float) -> Any:
    db = DB.get()
    async with db.transaction():
        stmt = update(UploadedFile).where(UploadedFile.c.id == id).values(progress=progress)
        await db.execute(stmt)

    return True


@check_db_connected
async def update_image_uuid(id: int, image_uuid: str) -> Any:
    db = DB.get()
    async with db.transaction():
        stmt = update(UploadedFile).where(UploadedFile.c.id == id).values(image_uuid=image_uuid)
        await db.execute(stmt)

    return True


@check_db_connected
async def get_file_info_by_id(id: int):
    db = DB.get()
    async with db.transaction():
        query = select(UploadedFile).where(UploadedFile.c.id == id)
        result = await db.fetch_one(query)

    return result


@check_db_connected
async def delete_file_record(id: int):
    now = int(time.time()) - 1
    query = delete(RevokedToken).where(RevokedToken.c.expire < now)
    db = DB.get()
    async with db.transaction():
        result = await db.execute(query)

    return result

    db = DB.get()
    async with db.transaction():
        query = select(UploadedFile).where(UploadedFile.c.id == id)
        result = await db.fetch_one(query)

    return result


@check_db_connected
async def get_other_settings() -> dict[str, Any]:
    query = select(OtherSetting)
    db = DB.get()
    async with db.transaction():
        results = await db.fetch_all(query)

    settings = {}
    for row in results:
        settings[row["type"]] = row["value"]

    # Apply default values if not found in DB
    if "enable_captcha" not in settings:
        settings["enable_captcha"] = "True"
    if "enable_login_failure_check" not in settings:
        settings["enable_login_failure_check"] = "True"
    if "max_login_failures" not in settings:
        settings["max_login_failures"] = "5"
    if "show_uccps_button" not in settings:
        settings["show_uccps_button"] = "True"
    if "show_large_screen_button" not in settings:
        settings["show_large_screen_button"] = "True"
    if "show_health_inspection_button" not in settings:
        settings["show_health_inspection_button"] = "True"

    return settings


@check_db_connected
async def update_other_settings(settings_update: dict[str, Any]) -> dict[str, Any]:
    db = DB.get()
    async with db.transaction():
        for setting_type, setting_value in settings_update.items():
            # Convert boolean and integer values to string for storage
            value_str = str(setting_value)

            # Check if a record with this type exists
            existing_setting = await db.fetch_one(select(OtherSetting).where(OtherSetting.c.type == setting_type))

            if existing_setting is None:
                # Insert a new record if none exists
                stmt = insert(OtherSetting).values(type=setting_type, value=value_str)
            else:
                # Update the existing record
                stmt = update(OtherSetting).where(OtherSetting.c.type == setting_type).values(value=value_str)

            await db.execute(stmt)

    # Fetch and return the updated settings
    return await get_other_settings()


@check_db_connected
async def insert_job_lock(lock_key: str) -> Any:
    now = datetime.datetime.now(tz=datetime.timezone.utc) - datetime.timedelta(minutes=7) # 10m: 允许系统时间误差10分钟 5m: 允许任务执行最小间隔为5分钟,此后分布式锁可以过期
    db = DB.get()

    query_clear = delete(ApsLock).where(and_(ApsLock.c.lock_name == lock_key, ApsLock.c.created_at < now))
    async with db.transaction():
        try:
            result_clear = await db.execute(query_clear)
        except Exception as e:
            print(f"清除过期分布式锁失败 {lock_key}: {e}")
            result = -1
            
            
    query = insert(ApsLock)
    async with db.transaction():
        try:
            result = await db.execute(query, {"lock_name": lock_key})
            # print(f"获取分布式锁成功: {lock_key}")
        except Exception as e:
            # print(f"分布式锁已经被其他worker获取，本worker无法获取该分布式锁 {lock_key}: {e}")
            result = -1

    return result

@check_db_connected
async def delete_job_lock(lock_key: str) -> Any:
    query = delete(ApsLock).where(ApsLock.c.lock_name == lock_key)
    db = DB.get()
    async with db.transaction():
        try:
            result = await db.execute(query)
            # print(f"释放分布式锁成功: {lock_key}")
        except Exception as e:
            print(f"释放分布式锁失败  {lock_key}: {e}")
            result = -1

    return result


@check_db_connected
async def create_captcha(captcha_id: str, captcha_text: str, expires_at: datetime.datetime) -> Any:
    query = insert(Captcha).values(
        id=captcha_id,
        text=captcha_text,
        expires_at=expires_at
    )
    db = DB.get()
    async with db.transaction():
        result = await db.execute(query)
    return result


@check_db_connected
async def get_captcha(captcha_id: str) -> Any:
    query = select(Captcha).where(Captcha.c.id == captcha_id)
    db = DB.get()
    async with db.transaction():
        result = await db.fetch_one(query)
    return result


@check_db_connected
async def delete_captcha(captcha_id: str) -> Any:
    query = delete(Captcha).where(Captcha.c.id == captcha_id)
    db = DB.get()
    async with db.transaction():
        result = await db.execute(query)
    return result


@check_db_connected
async def create_health_check_history(user_id: str = None, username: str = None) -> Any:
    """创建健康巡检历史记录"""
    query = insert(SysSafeHealthCheckHistory)
    db = DB.get()
    async with db.transaction():
        data = {
            "user_id": user_id,
            "username": username,
            "state": 0,  # 0-进行中
            "check_code": str(uuid.uuid4()),
            "created_at": datetime.datetime.now(),
        }
        id = await db.execute(query, data)
        result = await db.fetch_one(
            select(SysSafeHealthCheckHistory).where(SysSafeHealthCheckHistory.c.id == id)
        )

    return result


@check_db_connected
async def update_health_check_state(id: int, state: int) -> bool:
    """更新健康巡检状态"""
    from datetime import datetime
    
    db = DB.get()
    async with db.transaction():
        stmt = update(SysSafeHealthCheckHistory).where(SysSafeHealthCheckHistory.c.id == id).values(
            state=state,
            end_time=datetime.now()
        )
        await db.execute(stmt)

    return True


@check_db_connected
async def get_health_check_category_list() -> Any:
    """获取健康巡检类别列表"""
    query = select(SysSafeHealthCheckCategory).order_by(SysSafeHealthCheckCategory.c.sort)
    db = DB.get()
    async with db.transaction():
        result = await db.fetch_all(query)

    return result


@check_db_connected
async def save_health_check_result(history_id: int, category_id: int, result: int, detail: str = None) -> Any:
    """保存健康巡检结果"""
    query = insert(SysSafeHealthCheckResult)
    db = DB.get()
    async with db.transaction():
        data = {
            "history_id": history_id,
            "category_id": category_id,
            "result": result,
            "detail": detail,
        }
        id = await db.execute(query, data)
        result = await db.fetch_one(
            select(SysSafeHealthCheckResult).where(SysSafeHealthCheckResult.c.id == id)
        )

    return result


@check_db_connected
async def get_health_check_result_by_history_id_and_category_id(history_id: int, category_id: int) -> Any:
    """根据巡检历史ID和巡检类别ID获取巡检结果"""
    query = select(SysSafeHealthCheckResult).where(
        and_(
            SysSafeHealthCheckResult.c.history_id == history_id,
            SysSafeHealthCheckResult.c.category_id == category_id
        )
    )
    db = DB.get()
    async with db.transaction():
        result = await db.fetch_one(query)

    return result


@check_db_connected
async def statistics_health_check_result(history_id: int) -> Any:
    """统计健康巡检结果"""
    db = DB.get()
    async with db.transaction():
        query = """
            SELECT c.id as category_id, c.name,
                SUM(CASE WHEN r.result = 0 THEN 1 ELSE 0 END) as normal,
                SUM(CASE WHEN r.result = 1 THEN 1 ELSE 0 END) as abnormal
            FROM sys_safe_health_check_category c
            LEFT JOIN sys_safe_health_check_result r ON c.id = r.category_id AND r.history_id = :history_id
            WHERE c.parent_id IS NULL
            GROUP BY c.id, c.name
            ORDER BY c.sort
        """
        result = await db.fetch_all(query, {"history_id": history_id})

    return result


@check_db_connected
async def statistics_health_result_by_status(history_id: int) -> Any:
    """
    按状态统计健康巡检结果

    对应 Java 版本的 statisticsHealthCheckResult 方法
    执行 SQL: select result,count(result) as sumnum from sys_safe_health_check_result
              where history_id = #{historyId} group by result

    Args:
        history_id: 巡检历史ID

    Returns:
        list: 包含 {'result': str, 'sum_num': str} 的列表
              确保包含 -1(异常)、0(正常)、1(警告) 三种状态
    """
    db = DB.get()
    async with db.transaction():
        # 执行与 Java 版本完全相同的 SQL 查询
        query = """
            SELECT result, COUNT(result) as sum_num
            FROM sys_safe_health_check_result
            WHERE history_id = :history_id
            GROUP BY result
        """
        raw_results = await db.fetch_all(query, {"history_id": history_id})

        # 转换为字典格式，便于后续处理
        result_dict = {}
        for row in raw_results:
            result_dict[str(row.result)] = str(row.sum_num)

        # 确保包含所有状态（-1, 0, 1），缺失的状态补0
        # 这与 Java 版本的逻辑完全一致
        final_results = []
        for status in ["-1", "0", "1"]:
            final_results.append({
                "result": status,
                "sum_num": result_dict.get(status, "0")
            })

        return final_results


@check_db_connected
async def get_health_check_result_list(history_id: int) -> Any:
    """
    获取健康巡检结果列表

    对应 Java 版本的 SysSafeHealthCheckCategoryService.selectHealthCheckResult 方法
    执行与 Java 版本完全相同的复杂 JOIN 查询逻辑

    Args:
        history_id: 巡检历史ID

    Returns:
        list: 包含父子类别层级结构和检查结果的列表
    """
    db = DB.get()
    async with db.transaction():
        # 执行与 Java 版本完全相同的复杂 JOIN 查询
        # 对应 SysSafeHealthCheckCategoryDao.xml 中的 selectHealthCheckResult 查询
        query = """
            SELECT
                t.id, t.category_name, t.category_code, t.parent_id, t.info, t.order_num, t.deleted, t.proposal_info,
                t1.id AS joina_id, t1.category_name AS joina_category_name,
                t1.category_code AS joina_category_code, t1.parent_id AS joina_parent_id,
                t1.info AS joina_info, t1.order_num AS joina_order_num, t1.deleted AS joina_deleted,
                t1.result AS joina_result, t1.result_info AS joina_result_info,
                t1.proposal_info AS joina_proposal_info
            FROM sys_safe_health_check_category t
            INNER JOIN (
                SELECT t11.*, t2.result, t2.detail AS result_info
                FROM sys_safe_health_check_category t11
                INNER JOIN sys_safe_health_check_result t2 ON t11.id = t2.category_id
                WHERE t11.deleted = 1 AND t2.history_id = :history_id
            ) t1 ON (t1.parent_id = t.id)
            WHERE t.deleted = 1
        """

        raw_results = await db.fetch_all(query, {"history_id": history_id})

        # 按照 Java 版本的 resultMap 逻辑组装数据结构
        # 使用字典来组织父子关系，模拟 MyBatis 的 collection 映射
        parent_map = {}

        for row in raw_results:
            parent_id = row.id

            # 构建父类别对象
            if parent_id not in parent_map:
                parent_map[parent_id] = {
                    "id": row.id,
                    "category_name": row.category_name,
                    "category_code": row.category_code,
                    "parent_id": row.parent_id,
                    "info": row.info,
                    "order_num": row.order_num,
                    "deleted": row.deleted,
                    "proposal_info": row.proposal_info,
                    "child_list": []  # 对应 Java 版本的 childList 属性
                }

            # 构建子类别对象（对应 JOIN 查询的结果）
            child_item = {
                "id": row.joina_id,
                "category_name": row.joina_category_name,
                "category_code": row.joina_category_code,
                "parent_id": row.joina_parent_id,
                "info": row.joina_info,
                "order_num": row.joina_order_num,
                "deleted": row.joina_deleted,
                "result": row.joina_result,  # 巡检结果状态
                "result_info": row.joina_result_info,  # 巡检结果详情
                "proposal_info": row.joina_proposal_info
            }

            parent_map[parent_id]["child_list"].append(child_item)

        # 转换为列表并返回，与 Java 版本的返回格式一致
        result = list(parent_map.values())

        return result


@check_db_connected
async def get_health_check_history_list(page: int, limit: int) -> Any:
    """获取健康巡检历史列表"""
    db = DB.get()
    async with db.transaction():
        # 获取总记录数
        count_query = select(func.count(SysSafeHealthCheckHistory.c.id))
        total = await db.fetch_val(count_query)
        
        # 获取分页数据
        query = select(SysSafeHealthCheckHistory).order_by(SysSafeHealthCheckHistory.c.id.desc()).limit(limit).offset((page - 1) * limit)
        items = await db.fetch_all(query)
        
        # 计算总页数
        pages = (total + limit - 1) // limit
        
        result = {
            "total": total,
            "list": items,
            "pageNum": page,
            "pageSize": limit,
            "pages": pages
        }

    return result


@check_db_connected
async def get_health_check_settings() -> Any:
    """获取健康巡检设置"""
    db = DB.get()
    async with db.transaction():
        # 先获取所有父类别
        parent_categories = await db.fetch_all(
            select(SysSafeHealthCheckCategory).where(SysSafeHealthCheckCategory.c.parent_id.is_(None)).order_by(SysSafeHealthCheckCategory.c.sort)
        )
        
        result = []
        for parent in parent_categories:
            parent_dict = dict(parent)
            # 获取子类别及其设置
            sub_categories = await db.fetch_all(
                select(SysSafeHealthCheckCategory).where(SysSafeHealthCheckCategory.c.parent_id == parent.id).order_by(SysSafeHealthCheckCategory.c.sort)
            )
            
            sub_settings = []
            for sub in sub_categories:
                sub_dict = dict(sub)
                # 获取该子类别的设置
                setting = await db.fetch_one(
                    select(SysSafeHealthCheckSettings).where(
                        and_(
                            SysSafeHealthCheckSettings.c.category_id == sub.id,
                            SysSafeHealthCheckSettings.c.deleted == 1
                        )
                    )
                )
                if setting:
                    sub_dict["setting"] = dict(setting)
                else:
                    sub_dict["setting"] = {"min": None, "max": None, "category_code": sub.get("code")}
                sub_settings.append(sub_dict)
            
            parent_dict["children"] = sub_settings
            result.append(parent_dict)
        
        # 获取定时任务设置
        task_query = select(SysSafeHealthScheduledTask)
        task = await db.fetch_one(task_query)

    return result, task


@check_db_connected
async def save_health_check_settings(settings: list, is_open: int = None, cron: str = None) -> bool:
    """
    保存健康巡检设置
    完全按照Java实现的逻辑：SysSafeHealthCheckSettingsServiceImpl.save
    """
    import logging
    LOG = logging.getLogger(__name__)

    try:
        db = DB.get()
        async with db.transaction():
            LOG.info("开始保存健康巡检设置")

            # 1. 先将数据库数据删除（软删除）- 对应Java代码第72-76行
            LOG.info("执行软删除现有设置")
            await db.execute(
                update(SysSafeHealthCheckSettings).values(
                    deleted=0,
                    update_time=datetime.datetime.now()
                )
            )

            # 2. 批量保存或更新设置 - 对应Java代码第77行 saveOrUpdateBatch(list)
            LOG.info(f"批量保存设置，数量: {len(settings)}")
            for setting in settings:
                # 设置update_time字段
                setting_data = {
                    "category_id": setting["category_id"],
                    "category_code": setting.get("category_code"),
                    "min": setting.get("min"),
                    "max": setting.get("max"),
                    "deleted": 1,  # 新增记录为未删除状态
                    "update_time": datetime.datetime.now()
                }

                # 插入新记录
                await db.execute(insert(SysSafeHealthCheckSettings).values(**setting_data))

            # 3. 处理定时巡检业务逻辑 - 对应Java代码第78-96行
            # LOG.info("处理定时巡检业务逻辑")

            # # 查询现有的定时任务
            # task_list = await db.fetch_all(select(SysSafeHealthScheduledTask))

            # if task_list and len(task_list) > 0:
            #     # 更新现有任务
            #     task_entity = task_list[0]
            #     task_id = task_entity.id

            #     # 设置is_open值 - 对应Java代码第88-92行
            #     is_open_value = 0 if is_open is None else is_open

            #     await db.execute(
            #         update(SysSafeHealthScheduledTask)
            #         .where(SysSafeHealthScheduledTask.c.id == task_id)
            #         .values(
            #             is_open=bool(is_open_value),
            #             cron=cron,
            #             updated_at=datetime.datetime.now()
            #         )
            #     )
            # else:
            #     # 创建新任务
            #     is_open_value = 0 if is_open is None else is_open

            #     await db.execute(
            #         insert(SysSafeHealthScheduledTask).values(
            #             is_open=bool(is_open_value),
            #             cron=cron,
            #             created_at=datetime.datetime.now(),
            #             updated_at=datetime.datetime.now()
            #         )
            #     )

            # # 注意：Java代码中还有处理ScheduleJobEntity的逻辑（第97-117行）
            # # 但在Python版本中，我们暂时不实现这部分，因为可能涉及不同的任务调度系统
            # # 如果需要，可以在后续添加对应的任务调度逻辑

            LOG.info("健康巡检设置保存完成")
            return True

    except Exception as e:
        LOG.error(f"保存健康巡检设置失败: {str(e)}")
        import traceback
        LOG.error(f"错误详情: {traceback.format_exc()}")
        raise e


@check_db_connected
async def batch_create_packet_loss_check_results(packet_loss_check_list: list) -> Any:
    """批量保存丢包检查结果到数据库"""
    if not packet_loss_check_list:
        return 0

    query = insert(SysSafeHostCheckPacketLoss)
    db = DB.get()
    async with db.transaction():
        result = await db.execute_many(query, packet_loss_check_list)
    return result


@check_db_connected
async def get_packet_loss_check_results_by_history_id(history_id: int) -> Any:
    """根据历史记录ID获取丢包检查结果"""
    query = select(SysSafeHostCheckPacketLoss).where(SysSafeHostCheckPacketLoss.c.history_id == history_id)
    db = DB.get()
    async with db.transaction():
        result = await db.fetch_all(query)
    return result


@check_db_connected
async def delete_packet_loss_check_results_by_history_id(history_id: int) -> Any:
    """根据历史记录ID删除丢包检查结果"""
    query = delete(SysSafeHostCheckPacketLoss).where(SysSafeHostCheckPacketLoss.c.history_id == history_id)
    db = DB.get()
    async with db.transaction():
        result = await db.execute(query)
    return result


@check_db_connected
async def create_host_check_info(host_check_info: schemas.SysSafeHostCheckInfoCreate) -> Any:
    """创建主机检查信息记录"""
    query = insert(SysSafeHostCheckInfo).values(**host_check_info.dict(exclude_unset=True))
    db = DB.get()
    async with db.transaction():
        result = await db.execute(query)
    return result


@check_db_connected
async def update_host_check_info_by_history_and_host(
    history_id: int,
    host_ip: str,
    host_check_info: schemas.SysSafeHostCheckInfoCreate
) -> Any:
    """根据历史记录ID和主机IP更新主机检查信息"""
    query = (
        update(SysSafeHostCheckInfo)
        .where(
            and_(
                SysSafeHostCheckInfo.c.history_id == history_id,
                SysSafeHostCheckInfo.c.host_ip == host_ip
            )
        )
        .values(**host_check_info.dict(exclude_unset=True))
    )
    db = DB.get()
    async with db.transaction():
        result = await db.execute(query)
    return result


@check_db_connected
async def get_host_check_info_by_history_and_host(history_id: int, host_ip: str) -> Any:
    """根据历史记录ID和主机IP获取主机检查信息"""
    query = select(SysSafeHostCheckInfo).where(
        and_(
            SysSafeHostCheckInfo.c.history_id == history_id,
            SysSafeHostCheckInfo.c.host_ip == host_ip
        )
    )
    db = DB.get()
    async with db.transaction():
        result = await db.fetch_one(query)
    return result


@check_db_connected
async def save_or_update_host_check_info(
    history_id: int,
    host_ip: str,
    host_check_info: schemas.SysSafeHostCheckInfoCreate
) -> Any:
    """保存或更新主机检查信息（类似Java的saveOrUpdate）"""
    # 先查询是否存在
    existing = await get_host_check_info_by_history_and_host(history_id, host_ip)

    if existing:
        # 如果存在则更新
        return await update_host_check_info_by_history_and_host(history_id, host_ip, host_check_info)
    else:
        # 如果不存在则创建
        return await create_host_check_info(host_check_info)


@check_db_connected
async def get_host_check_info_by_history_id(history_id: int) -> Any:
    """根据历史记录ID获取所有主机检查信息"""
    query = select(SysSafeHostCheckInfo).where(SysSafeHostCheckInfo.c.history_id == history_id)
    db = DB.get()
    async with db.transaction():
        result = await db.fetch_all(query)
    return result


@check_db_connected
async def delete_host_check_info_by_history_id(history_id: int) -> Any:
    """根据历史记录ID删除主机检查信息"""
    query = delete(SysSafeHostCheckInfo).where(SysSafeHostCheckInfo.c.history_id == history_id)
    db = DB.get()
    async with db.transaction():
        result = await db.execute(query)
    return result
