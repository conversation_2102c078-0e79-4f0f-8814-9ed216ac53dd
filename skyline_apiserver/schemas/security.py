# Copyright 2023 99cloud
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from typing import Any, Dict, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field


class CategoryCode(str):
    SAFE_CVE = "SAFE_CVE"
    NETWORK_PACKAGE_LOSS = "NETWORK_PACKAGE_LOSS"
    CEPH_POOL_SIZE = "CEPH_POOL_SIZE"
    CEPH_POOL = "CEPH_POOL"
    CEPH_OBJECT = "CEPH_OBJECT"
    CEPH_PG = "CEPH_PG"
    CEPH_OSD = "CEPH_OSD"
    CEPH_STATUS = "CEPH_STATUS"
    HOSTOS_DISK_USED_PERCENT = "HOSTOS_DISK_USED_PERCENT"
    HOSTOS_SHARE_CPU_USED_PERCENT = "HOSTOS_SHARE_CPU_USED_PERCENT"
    HOSTOS_SHARE_MEMORY_USED_PERCENT = "HOSTOS_SHARE_MEMORY_USED_PERCENT"
    HOSTOS_CPU_USED_PERCENT = "HOSTOS_CPU_USED_PERCENT"
    HOSTOS_MEMORY_USED_PERCENT = "HOSTOS_MEMORY_USED_PERCENT"
    HOSTOS_WARN_EVENT = "HOSTOS_WARN_EVENT"
    HOSTOS_NETWORK_STATUS = "HOSTOS_NETWORK_STATUS"
    CLOUD_NTP = "CLOUD_NTP"
    CLOUD_WARN = "CLOUD_WARN"
    CLOUD_STATUS = "CLOUD_STATUS"
    CLOUD_VIRTUAL_HA_STATUS = "CLOUD_VIRTUAL_HA_STATUS"
    CLOUD_DB_STATUS = "CLOUD_DB_STATUS"
    CLOUD_NOVA = "CLOUD_NOVA"
    CLOUD_CINDER = "CLOUD_CINDER"
    CLOUD_KEYSTONE = "CLOUD_KEYSTONE"
    CLOUD_NEUTRON_SERVER = "CLOUD_NEUTRON_SERVER"
    CLOUD_GLANCE = "CLOUD_GLANCE"
    CLOUD_NEUTRON_DHCP_AGENT = "CLOUD_NEUTRON_DHCP_AGENT"
    CLOUD_KEEPALVED = "CLOUD_KEEPALVED"
    CLOUD_NEUTRON_OPENVSWITCH_AGENT = "CLOUD_NEUTRON_OPENVSWITCH_AGENT"
    CLOUD_NOVA_COMPUTE = "CLOUD_NOVA_COMPUTE"
    CLOUD_CINDER_VOLUME = "CLOUD_CINDER_VOLUME"
    CLOUD_NEUTRON = "CLOUD_NEUTRON"
    CLOUD_HA_STATUS = "CLOUD_HA_STATUS"
    GUESTOS_VOLUME = "GUESTOS_VOLUME"
    GUESTOS_MEMORY_USED_PERCENT = "GUESTOS_MEMORY_USED_PERCENT"
    GUESTOS_CPU_USED_PERCENT = "GUESTOS_CPU_USED_PERCENT"
    GUESTOS_DISK_USED_PERCENT = "GUESTOS_DISK_USED_PERCENT"
    NETWORK_STATUS = "NETWORK_STATUS"
    NETWORK_ROUTE = "NETWORK_ROUTE"
    NETWORK_GATEWAY = "NETWORK_GATEWAY"
    NETWORK_FLOATIP = "NETWORK_FLOATIP"
    NETWORK_PORT = "NETWORK_PORT"
    NETWORK_HTTP = "NETWORK_HTTP"


class SysSafeHealthCheckHistoryBase(BaseModel):
    user_id: Optional[str] = Field(None, description="用户ID")
    username: Optional[str] = Field(None, description="用户名")
    state: Optional[int] = Field(None, description="状态：0-进行中，1-已完成，2-失败")
    check_code: Optional[str] = Field(None, description="巡检编码")


class SysSafeHealthCheckHistory(SysSafeHealthCheckHistoryBase):
    id: int = Field(..., description="巡检历史ID")
    created_at: datetime = Field(..., description="创建时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        orm_mode = True


class SysSafeHealthCheckHistoryCreate(SysSafeHealthCheckHistoryBase):
    pass


class SysSafeHealthCheckHistoryUpdate(BaseModel):
    state: int = Field(..., description="状态：0-进行中，1-已完成，2-失败")


class SysSafeHealthCheckCategoryBase(BaseModel):
    name: str = Field(..., description="类别名称")
    code: str = Field(..., description="类别编码")
    parent_id: Optional[int] = Field(None, description="父类别ID")
    sort: Optional[int] = Field(None, description="排序")


class SysSafeHealthCheckCategory(SysSafeHealthCheckCategoryBase):
    id: int = Field(..., description="巡检类别ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    results: Optional[List[Any]] = Field(None, description="巡检结果")

    class Config:
        orm_mode = True


class SysSafeHealthCheckResultBase(BaseModel):
    history_id: int = Field(..., description="巡检历史ID")
    category_id: int = Field(..., description="巡检类别ID")
    result: Optional[int] = Field(None, description="巡检结果：0-正常，1-异常")
    detail: Optional[str] = Field(None, description="详情")


class SysSafeHealthCheckResult(SysSafeHealthCheckResultBase):
    id: int = Field(..., description="巡检结果ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        orm_mode = True


class SysSafeHealthCheckResultDto(BaseModel):
    category_id: int = Field(..., description="巡检类别ID")
    name: str = Field(..., description="类别名称")
    normal: int = Field(..., description="正常数量")
    abnormal: int = Field(..., description="异常数量")


class SysSafeHealthCheckResultStatisticsDto(BaseModel):
    """
    巡检结果统计DTO

    对应 Java 版本的 SysSafeHealthCheckResultDto，用于 statisticsHealthResult 接口
    """
    result: str = Field(..., description="巡检结果状态：-1(异常)、0(正常)、1(警告)")
    sum_num: str = Field(..., description="该状态的数量")


class SysSafeHealthCheckSettingsBase(BaseModel):
    categoryId: Optional[int] = Field(None, alias="category_id", description="配置项目ID")
    categoryCode: Optional[str] = Field(None, alias="category_code", description="配置项目CODE")
    min: Optional[int] = Field(None, description="警告最小值")
    max: Optional[int] = Field(None, description="警告最大值")
    deleted: Optional[int] = Field(1, description="是否删除0：删除；1：未删除")

    # 添加属性访问器以兼容两种命名方式
    @property
    def category_id(self) -> Optional[int]:
        return self.categoryId

    @property
    def category_code(self) -> Optional[str]:
        return self.categoryCode

    class Config:
        allow_population_by_field_name = True


class SysSafeHealthCheckSettings(SysSafeHealthCheckSettingsBase):
    id: int = Field(..., description="主键ID")
    update_time: datetime = Field(..., description="修改时间")

    class Config:
        orm_mode = True


class SysSafeHealthScheduledTaskBase(BaseModel):
    is_open: Optional[bool] = Field(False, description="是否开启")
    cron: Optional[str] = Field(None, description="cron表达式")


class SysSafeHealthScheduledTask(SysSafeHealthScheduledTaskBase):
    id: int = Field(..., description="定时任务ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        orm_mode = True


class SaveSettingsRequest(BaseModel):
    settings: List[SysSafeHealthCheckSettingsBase] = Field(..., description="巡检设置列表")
    is_open: Optional[int] = Field(None, description="是否开启定时任务")
    cron: Optional[str] = Field(None, description="cron表达式")


class HealthCheckRequest(BaseModel):
    history_id: int = Field(..., description="巡检历史ID")
    category_id: int = Field(..., description="巡检类别ID")
    category_code: str = Field(..., description="巡检类别编码")


class SysSafeHostCheckPacketLossBase(BaseModel):
    cloud_id: Optional[int] = Field(None, description="云平台ID")
    host_ip: Optional[str] = Field(None, description="主机IP")
    state: Optional[str] = Field(None, description="主机丢包检查结果：0-正常；1-警告存在丢包情况；-1-网络存在异常")
    history_id: Optional[int] = Field(None, description="本次检查记录ID")


class SysSafeHostCheckPacketLoss(SysSafeHostCheckPacketLossBase):
    id: int = Field(..., description="主键ID")
    create_time: datetime = Field(..., description="检查时间")

    class Config:
        orm_mode = True


class SysSafeHostCheckPacketLossCreate(SysSafeHostCheckPacketLossBase):
    pass


class SysSafeHostCheckInfoBase(BaseModel):
    history_id: Optional[int] = Field(None, description="巡检记录ID")
    host_ip: Optional[str] = Field(None, description="主机IP")
    cpu_state: Optional[int] = Field(None, description="-1:异常；0:正常；1：警告")
    disk_state: Optional[int] = Field(None, description="-1:异常；0:正常；1：警告")
    memory_state: Optional[int] = Field(None, description="-1:异常；0:正常；1：警告")
    cpu_percent: Optional[float] = Field(None, description="CPU使用率")
    memory_percent: Optional[float] = Field(None, description="内存使用率")
    disk_percent: Optional[float] = Field(None, description="磁盘使用率")
    warn_num: Optional[int] = Field(None, description="警告数量")
    network_state: Optional[int] = Field(None, description="-1:异常；0:正常；1：警告")


class SysSafeHostCheckInfo(SysSafeHostCheckInfoBase):
    id: int = Field(..., description="主键ID")

    class Config:
        orm_mode = True


class SysSafeHostCheckInfoCreate(SysSafeHostCheckInfoBase):
    pass


class PageResponse(BaseModel):
    total: int = Field(..., description="总记录数")
    list: List[Any] = Field(..., description="数据列表")
    pageNum: int = Field(..., description="当前页码")
    pageSize: int = Field(..., description="每页记录数")
    pages: int = Field(..., description="总页数")