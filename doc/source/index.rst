..
      Licensed under the Apache License, Version 2.0 (the "License"); you may
      not use this file except in compliance with the License. You may obtain
      a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

      Unless required by applicable law or agreed to in writing, software
      distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
      WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
      License for the specific language governing permissions and limitations
      under the License.

==================================================
Skyline Console (UI of OpenStack Modern Dashboard)
==================================================

Introduction
============

`Skyline Console <https://github.com/openstack/skyline-console>`_ is one part
of OpenStack Modern Dashboard, which provides a web based user interface to
OpenStack services including Nova, Swift, Keystone, etc.

Using Skyline Console
=====================

How to use Skyline Console in your own projects.

.. toctree::
   :maxdepth: 2

   install/index
   configuration/index
   User Documentation <user/index>
   admin/index

Contributor Docs
================

.. toctree::
   :maxdepth: 2

   contributor/index
   development/index
   test/index

Release Notes
=============

See https://docs.openstack.org/releasenotes/skyline-console

Information
===========

.. toctree::
   :maxdepth: 1

   glossary
