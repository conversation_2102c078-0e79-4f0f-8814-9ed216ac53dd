import Base from '../client/base';
import { thirdpartyBase } from '../client/constants';

export class UStackClient extends Base {
  get baseUrl() {
    return thirdpartyBase();
  }

  get resources() {
    return [
      {
        name: '',
        key: '',
        isResource: false,
        extendOperations: [
          {
            name: 'tokenInfo',
            key: 'token_info',
            method: 'get',
          },
          {
            name: 'getVisUrl',
            key: 'get_vis_url',
            method: 'get',
          },
          {
            name: 'getUccpsUrl',
            key: 'get_uccps_url',
            method: 'get',
          },
          {
            name: 'getUploadFile',
            key: 'upload_file/',
            method: 'get',
          },
          {
            key: 'UploadFile',
            generate: (id, body, conf = {}) => {
              return this.request.post(`upload_file`, body, null, {
                headers: {
                  'content-type': 'application/octet-stream',
                },
                ...conf,
              });
            },
          },
          {
            key: 'mergeChunk',
            generate: (key, conf = {}) => {
              return this.request.put(`upload_file/${key}`, {}, null, {
                headers: {
                  'content-type': 'application/octet-stream',
                },
                ...conf,
              });
            },
          },
          {
            key: 'deleteUploadFile',
            generate: (key) => {
              return this.request.delete(`upload_file/${key}`);
            },
          },
          {
            key: 'getVmMetrics',
            generate: (key) => {
              return this.request.get(`vm_metrics/${key}`);
            },
          },
          {
            name: 'getOtherSettings',
            key: 'other_settings',
            method: 'get',
          },
          {
            name: 'updateOtherSettings',
            key: 'other_settings',
            method: 'put',
          },
          {
            name: 'get_captcha',
            key: 'captcha',
            method: 'get',
          },
          {
            name: 'upload_login_page_logo',
            key: 'upload_login_page_logo',
            method: 'put',
          },
          {
            name: 'get_login_page_logo',
            key: 'login_page_logo',
            method: 'get',
          },
          {
            name: 'delete_login_page_logo',
            key: 'login_page_logo',
            method: 'delete',
          },
          {
            name: 'get_favicon',
            key: 'favicon',
            method: 'get',
          },
          {
            name: 'upload_favicon',
            key: 'favicon',
            method: 'put',
          },
          {
            name: 'delete_favicon',
            key: 'favicon',
            method: 'delete',
          },
        ],
      },
    ];
  }
}
const ustackClient = new UStackClient();
export default ustackClient;
