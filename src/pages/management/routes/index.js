// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import BaseLayout from 'layouts/Basic';
import E404 from 'pages/base/containers/404';
import InstanceDetail from 'pages/compute/containers/Instance/Detail';
import RecycleBin from '../containers/RecycleBin';

const PATH = '/management';
export default [
  {
    path: PATH,
    component: BaseLayout,
    routes: [
      { path: `${PATH}/recycle-bin`, component: RecycleBin, exact: true },
      {
        path: `${PATH}/recycle-bin/detail/:id`,
        component: InstanceDetail,
        exact: true,
      },
      { path: `${PATH}/recycle-bin-admin`, component: RecycleBin, exact: true },
      {
        path: `${PATH}/recycle-bin-admin/detail/:id`,
        component: InstanceDetail,
        exact: true,
      },
      { path: '*', component: E404 },
    ],
  },
];
