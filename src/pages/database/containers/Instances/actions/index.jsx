// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import Delete from './Delete';
import Create from './StepCreate';
import Edit from './Edit';
import Restart from './Restart';
import Stop from './Stop';
import Reboot from './Reboot';
import ResizeVolume from './ResizeVolume';

const actionConfigs = {
  rowActions: {
    firstAction: Delete,
    moreActions: [
      {
        action: Edit,
      },
      {
        title: t('Database Instance Status'),
        actions: [Restart, Stop, Reboot],
      },
      {
        title: t('Configuration Update'),
        actions: [ResizeVolume],
      },
    ],
  },
  primaryActions: [Create],
  batchActions: [Delete],
};

const actionConfigsAdmin = {
  rowActions: {
    firstAction: Delete,
  },
  primaryActions: [],
  batchActions: [Delete],
};

export default { actionConfigs, actionConfigsAdmin };
