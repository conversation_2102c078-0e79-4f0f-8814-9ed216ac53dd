// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { ConfirmAction } from 'containers/Action';
import globalIronicStore from 'stores/ironic/ironic';

export default class ClearMaintenance extends ConfirmAction {
  get id() {
    return 'ClearMaintenance';
  }

  get title() {
    return t('Leave Maintenance Mode');
  }

  get actionName() {
    return t('Leave Maintenance Mode');
  }

  policy = 'baremetal:node:clear_maintenance';

  getItemId = (data) => data.uuid;

  allowedCheckFunc = (item) => item.maintenance;

  onSubmit = () => {
    const { uuid } = this.item;
    return globalIronicStore.clearMaintenance(uuid);
  };
}
