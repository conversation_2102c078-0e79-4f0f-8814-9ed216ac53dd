// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { inject, observer } from 'mobx-react';
import Create from './Create';

export class Edit extends Create {
  static id = 'edit-node';

  static title = t('Edit Bare Metal Node');

  static buttonText = t('Edit');

  static path = (item) => `/compute/baremetal-node-admin/edit/${item.uuid}`;

  get listUrl() {
    return this.getRoutePath('baremetalNode');
  }

  get name() {
    return t('Edit Bare Metal Node');
  }

  static policy = 'baremetal:node:update';

  static allowed() {
    return Promise.resolve(true);
  }
}

export default inject('rootStore')(observer(Edit));
