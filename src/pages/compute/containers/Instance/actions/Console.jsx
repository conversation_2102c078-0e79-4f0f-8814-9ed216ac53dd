// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
import React from 'react';
import { ConfirmAction } from 'containers/Action';
import globalServerStore from 'stores/nova/instance';
import { isActive, isIronicInstance } from 'resources/nova/instance';
import i18n from 'core/i18n';
import { Radio } from 'antd';

const { getLocale } = i18n;

export default class Console extends ConfirmAction {
  constructor(props) {
    super(props);
    this.state = { value: 'VNC' };
    this.bodyType = {
      VNC: { protocol: 'vnc', type: 'novnc' },
      SPICE: { protocol: 'spice', type: 'spice-html5' },
    };
  }

  get id() {
    return 'console';
  }

  get title() {
    return t('Jump to Console');
  }

  get buttonText() {
    return t('Console');
  }

  get actionName() {
    return t('jump to the console');
  }

  policy = 'os_compute_api:os-remote-consoles';

  onChange = (e, _this) => {
    _this.state.value = e.target.value;
  };

  confirmContext = () => {
    const _this = this;
    console.log('this', this);
    return (
      <>
        <div>
          {t(
            'Are you sure to jump directly to the console? The console will open in a new page later.'
          )}
        </div>
        <br />
        {isIronicInstance(this.item) ? null : (
          <Radio.Group
            defaultValue="VNC"
            onChange={(e) => _this.onChange(e, _this)}
          >
            <Radio value="VNC">{t('VNC')}</Radio>
            <Radio value="SPICE">{t('SPICE')}</Radio>
          </Radio.Group>
        )}
      </>
    );
  };

  allowedCheckFunc = (item) => {
    if (!item) {
      return true;
    }
    return isActive(item) && !isIronicInstance(item);
  };

  performErrorMsg = () => {
    const errorMsg = t('You are not allowed to jump to the console.');
    return errorMsg;
  };

  onSubmit = async () => {
    const { id } = this.item;
    const store = globalServerStore;
    let res;
    if (isIronicInstance(this.item)) {
      res = await store.getConsoleIronic({ id });
    } else {
      const body = {
        remote_console: this.bodyType[this.state.value],
      };
      res = await store.getConsole({ id, body });
      this.state.value = 'VNC';
    }
    const { url } = res.remote_console;
    const selectedLang = getLocale();
    window.open(`${url}&language=${selectedLang}`);
  };
}
