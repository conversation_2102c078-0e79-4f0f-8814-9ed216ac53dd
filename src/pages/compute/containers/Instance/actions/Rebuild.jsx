// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import globalServerStore from 'stores/nova/instance';
import { ConfirmAction } from 'containers/Action';
import {
  isActiveOrShutOff,
  isNotLockedOrAdmin,
  isIsoInstance,
} from 'resources/nova/instance';

export default class Rebuild extends ConfirmAction {
  get id() {
    return 'rebuild';
  }

  get title() {
    return t('Rebuild Instance');
  }

  get isDanger() {
    return true;
  }

  get actionName() {
    return t('rebuild instance');
  }

  policy = 'os_compute_api:servers:rebuild';

  allowedCheckFunc = (item) => {
    if (!item) {
      return true;
    }
    return (
      !this.isAdminPage &&
      isActiveOrShutOff(item) &&
      isNotLockedOrAdmin(item, this.isAdminPage) &&
      this.isRootVolumeInUse(item) &&
      !isIsoInstance(item)
    );
  };

  onSubmit = (item) => {
    const { id, image } = item;
    return globalServerStore.rebuild({ id, image });
  };

  isRootVolumeInUse = () => true;
}
