import { message as $message } from 'antd';

export function uploadError(error) {
  console.log('uploadError', error);
  if (error && error.response) {
    const {
      response: { data },
    } = error;
    if (data) $message.error(data.detail);
  }
}

export function uploadSuccess(store) {
  store.setBody({});
  store.setChunkIndex(0);
  store.setIsContinue(false);
  store.setFile(undefined);
  store.setKey('');
  store.setHasChunks(false);
}
