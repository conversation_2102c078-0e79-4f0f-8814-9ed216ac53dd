import React from 'react';
import { ExclamationCircleOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Modal, Upload, message as $message } from 'antd';
import SparkMD5 from 'spark-md5';
import { calculateMD5 } from 'src/stores/glance/upload';
import globalImageStore from 'stores/glance/image';

const { confirm } = Modal;
const state = { isSame: false };
const store = globalImageStore;
// componentDidMount(){
//   this.checkUploading();
// }

const handleKeyDown = (e) => {
  if (e.key === 'Escape') {
    // 检查按键是否为ESC
    onClose();
  }
};
// 监听键盘事件
document.addEventListener('keydown', handleKeyDown);
export function checkUploading(callback, page, routing) {
  return new Promise((resolve) => {
    store.uploadInfo().then((result) => {
      if (result.chunk_list) {
        store.setHasChunks(true);
        if (
          !store.isConfirm &&
          (page !== 'list' ||
            (page === 'list' && !routing.location.pathname.includes('/create')))
        ) {
          showConfirm(result, callback, resolve);
        }
      } else {
        store.setHasChunks(false);
      }
    });
  });
}

const ModalContent = (props) => {
  const {
    others: { result, callback, resolve },
    modal,
    isSame,
  } = props;
  return (
    <div>
      <Upload
        fileList={state.fileList}
        maxCount={1}
        onChange={({ file, fileList }) =>
          handleChange(modal, file, fileList, result, callback, resolve)
        }
        beforeUpload={() => false}
      >
        <Button icon={<UploadOutlined />}>{t('Select File')}</Button>
      </Upload>
      <br />
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div key="close">
          <Button onClick={onClose}>
            {t('Not dealt with for the time being')}
          </Button>
        </div>
        <div>
          <Button key="cancel" onClick={() => onCancel(result)}>
            {t('Delete')}
          </Button>
          <Button
            key="submit"
            style={{ marginLeft: '5px' }}
            type="primary"
            onClick={() => onOk(isSame, result, callback, resolve)}
            disabled={!isSame}
          >
            {t('Continue')}
          </Button>
        </div>
      </div>
    </div>
  );
};

function handleChange(modal, file, fileList, result, callback, resolve) {
  const spark = new SparkMD5.ArrayBuffer(); // 追加数组缓冲区。
  if (fileList && fileList.length <= 0) {
    state.isSame = false;
    modal &&
      modal.update((prev) => ({
        ...prev,
        content: (
          <ModalContent
            modal={modal}
            others={{ result, callback, resolve }}
            isSame={state.isSame}
          />
        ),
      }));
  } else {
    calculateMD5(file, spark, (spark1) => {
      const key = spark1.end();
      if (key === result.key) {
        state.isSame = true;
        store.setFile(file);
      } else {
        state.isSame = false;
        $message.error(
          t(`Please select: {name} or an image file that is the same as it`, {
            name: JSON.parse(result.image_body).file_name,
          })
        );
      }
      modal &&
        modal.update((prev) => ({
          ...prev,
          content: (
            <ModalContent
              modal={modal}
              others={{ result, callback, resolve }}
              isSame={state.isSame}
            />
          ),
        }));
    });
  }
}

function onOk(isSame, result, callback, resolve) {
  store.setIsContinue(true);
  if (isSame) {
    const chunkIndex = Object.values(JSON.parse(result.chunk_list)).length;
    store.setBody(JSON.parse(result.image_body));
    store.setChunkIndex(chunkIndex);
    store.setKey(result.key);
    if (callback) {
      callback();
    }
    resolve();
    onClose();
  } else {
    $message.error(
      t(`Please select: {name} or an image file that is the same as it`, {
        name: JSON.parse(result.image_body).file_name,
      })
    );
  }
}

function onCancel(result) {
  store.setIsContinue(false);
  store
    .deleteFiles(result.key)
    .then(() => {})
    .catch((err) => {
      $message.error(err.response.data.detail);
    });
  onClose();
}

const onClose = () => {
  state.isSame = false;
  Modal.destroyAll();
  store.setIsConfirm(false);
};

function showConfirm(result, callback, resolve) {
  const modal = confirm();
  store.setIsConfirm(true);
  modal &&
    modal.update({
      title: t(
        `Uploads with mirrors are interrupted. The name of the mirror file is: {name}. Do you want to continue the image? Please select the mirror file in the button below and click the "Continue" button; Tips: If the wrong file is selected, it will not be interrupted. Clicking the "Delete" button will delete the task of uploading the image.`,
        {
          name: JSON.parse(result.image_body).file_name,
        }
      ),
      content: (
        <ModalContent
          modal={modal}
          others={{ result, callback, resolve }}
          isSame={state.isSame}
        />
      ),
      icon: <ExclamationCircleOutlined />,
      okButtonProps: { style: { display: 'none' } },
      cancelButtonProps: { style: { display: 'none' } },
      keyboard: true,
    });
}
