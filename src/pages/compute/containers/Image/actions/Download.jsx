// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { ModalAction } from 'containers/Action';
import { inject, observer } from 'mobx-react';
import client from 'client';
import FileSaver from 'file-saver';

export class DownloadAction extends ModalAction {
  static id = 'download-image';

  static title = t('Download Image');

  static buttonText = t('Download');

  static policy = 'download_image';

  static allowed = () => {
    return Promise.resolve(true);
  };

  get name() {
    return t('download image');
  }

  get formItems() {
    return [
      {
        name: 'name',
        label: t('Rename'),
        type: 'input-name',
        isImage: true,
        required: true,
      },
    ];
  }

  onSubmit = async (values) => {
    if (this.item.id) {
      return client.glance.images.downloadFile(this.item.id).then((res) => {
        const { name } = values;
        if (res.data) {
          return FileSaver.saveAs(
            new Blob([res.data]),
            `${name}.${this.item.disk_format}`
          );
        }
        return FileSaver.saveAs(
          new Blob([res]),
          `${name}.${this.item.disk_format}`
        );
      });
    }
  };
}

export default inject('rootStore')(observer(DownloadAction));
