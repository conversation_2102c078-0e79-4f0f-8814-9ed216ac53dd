// Copyright 2022 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import CreateVolume from './CreateVolume';
import CreateInstance from './CreateInstance';
import Edit from './Edit';
import Delete from './Delete';

const actionConfigs = {
  rowActions: {
    firstAction: Edit,
    moreActions: [
      {
        action: CreateInstance,
      },
      {
        action: CreateVolume,
      },
      {
        action: Delete,
      },
    ],
  },
  batchActions: [Delete],
};

const adminConfigs = {
  rowActions: {
    firstAction: Edit,
    moreActions: [
      {
        action: Delete,
      },
    ],
  },
  batchActions: [Delete],
};

export default { actionConfigs, adminConfigs };
