@import '~styles/variables';

.register {
  float: right;
}

.welcome {
  margin-top: 24px;
  margin-bottom: 24px;
}

.hint {
  padding: 8px 12px;
  color: rgba(0, 0, 0, 65%);
  font-size: 14px;
  line-height: 1.5;
  background: #fffbe6;
  border: 1px solid #ffe58f;
  border-radius: 4px;

  :global {
    .anticon {
      margin-right: 8px;
    }
  }
}

.reset {
  margin-top: 24px;

  .title {
    margin-bottom: 24px;
  }

  .info {
    margin-bottom: 24px;
  }

  .between {
    :global {
      .ant-form-item-control-input-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }
}

.error {
  padding-left: 12px;
  color: @login-error;
  font-size: 14px;
  line-height: 38px;
  background: #f2dede;
  border: 1px solid @login-error;
  border-radius: 4px;

  :global {
    .anticon {
      margin-right: 8px;
    }
  }
}
