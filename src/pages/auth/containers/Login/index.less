@import '~styles/variables';

.register {
  float: right;
}

.welcome {
  margin-top: 24px;
  margin-bottom: 24px;
}

.login-form {
  width: 310px;
}

.login-error {
  padding: 8px 12px;
  color: @login-error;
  font-size: 14px;
  line-height: 1.5;
  background: #f2dede;
  border: 1px solid @login-error;
  border-radius: 4px;

  :global {
    .anticon {
      margin-right: 8px;
    }
  }
}

.healthInspectionButton {
  margin-top: 16px;
  text-align: center;
}

.captchaContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  margin-bottom: 16px;

  :global {
    .ant-input {
      flex: none;
    }
  }
}

.captchaImage {
  width: 70%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: visible;
  transition: all 0.3s;

  :global {
    .ant-image {
      border: 1px solid #d9d9d9;
      border-radius: 1px;
    }

    .ant-image-img {
      height: 38px;
      min-width: 135px;
    }

    .ant-btn-link {
      padding: 0 0 0 4px;
      font-size: 13px;
    }
  }
}
