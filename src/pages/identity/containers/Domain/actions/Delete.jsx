// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
import React from 'react';
import { ConfirmAction } from 'containers/Action';
import globalDomainStore from 'stores/keystone/domain';

export default class DeleteAction extends ConfirmAction {
  get id() {
    return 'delete';
  }

  get title() {
    return t('Delete Domain');
  }

  get isDanger() {
    return true;
  }

  get buttonText() {
    return t('Delete');
  }

  get actionName() {
    return t('delete domain');
  }

  policy = 'identity:delete_domain';

  allowedCheckFunc = (data) => !data.enabled;

  confirmContext = (data) => {
    const name = this.getName(data);
    return (
      <div>
        <div>
          {t('Are you sure to {action} (instance: {name})?', {
            action: this.actionNameDisplay || this.title,
            name,
          })}
        </div>
        <div>
          {t(
            'Please note that when deleting a domain, all projects, users, and user groups under the domain will be deleted directly!'
          )}
        </div>
      </div>
    );
  };

  onSubmit = (data) => {
    const { id } = data;
    return globalDomainStore.delete({ id });
  };
}
