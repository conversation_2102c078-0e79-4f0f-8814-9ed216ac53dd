@import '~styles/variables';

.wrapper {
  min-height: calc(100vh - 108px);
  padding: 0 @body-padding;
}

.tablewrapper {
  min-height: calc(100vh - 108px);
  margin-top: -@body-padding;
}

.collapse {
  margin-bottom: 24px;
  overflow: hidden;
}

.panel {
  margin-bottom: 24px;
  padding-top: 12px;
  padding-bottom: 12px;
  font-size: larger;
  background: #fff;
}

.header-divider {
  width: 2px;
  margin: 0 5px;
  background-color: @color-text-caption;
}

.image {
  width: 100px;
  padding: 10px;
}

.radio-button {
  :global {
    .ant-radio-button-wrapper {
      width: 250px;
      height: 100px;
      margin-right: 40px;
      padding: 15px;
    }
  }
}

.security-group-text {
  font-size: 14px;
}

.header-title {
  font-style: italic;
}

.title-label {
  margin-right: 8px;
  font-style: italic;
}

.header-button {
  float: right;
}
