// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import BaseLayout from 'layouts/Basic';
import E404 from 'pages/base/containers/404';
import TicketManagement from '../containers/TicketManagement';
import MyTickets from '../containers/TicketManagement/MyTickets';
import PendingTickets from '../containers/TicketManagement/PendingTickets';
import CompletedTickets from '../containers/TicketManagement/CompletedTickets';

// 工单管理路由配置
const TICKET_PATH = '/admin/tickets';

export default [
  {
    path: TICKET_PATH,
    component: BaseLayout,
    routes: [
      // 主工单管理页面
      {
        path: `${TICKET_PATH}/index`,
        component: TicketManagement,
        exact: true,
      },
      // 我的工单页面
      {
        path: `${TICKET_PATH}/my`,
        component: MyTickets,
        exact: true,
      },
      // 待办工单页面
      {
        path: `${TICKET_PATH}/ready`,
        component: PendingTickets,
        exact: true,
      },
      // 已办工单页面
      {
        path: `${TICKET_PATH}/already`,
        component: CompletedTickets,
        exact: true,
      },
      // 404页面，处理未匹配的路由
      {
        path: '*',
        component: E404,
      },
    ],
  },
];
