// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import React from 'react';
import { observer } from 'mobx-react';
import {
  Form,
  Switch,
  InputNumber,
  Button,
  message,
  Upload,
  Image,
  Modal,
  Input,
} from 'antd';
import BaseDetail from 'src/containers/BaseDetail';
import ustackClient from 'client/ustack';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';

@observer
export default class OtherSetting extends BaseDetail {
  state = {
    loading: false,
    initialValues: {},
    fetching: true,
    fileList: [],
    logoUrl: null,
    faviconFileList: [],
    faviconUrl: null,
  };

  componentDidMount() {
    console.log('其他设置组件挂载，开始初始化数据');
    // 获取配置数据
    this.fetchSettings();
    // 延迟获取Logo图片，避免并发请求
    setTimeout(() => {
      this.fetchLogoImage();
    }, 500);
    // 获取Favicon图片
    this.fetchFaviconImage();
  }

  fetchSettings = async () => {
    this.setState({ fetching: true });
    try {
      // 获取其他设置配置
      console.log('开始获取其他设置配置...');
      const settings = await ustackClient.getOtherSettings();
      console.log('获取其他设置配置成功:', settings);
      this.setState({ initialValues: settings, fetching: false });
    } catch (error) {
      // 增强错误处理和日志记录
      console.error('获取其他设置配置失败:', {
        message: error.message,
        stack: error.stack,
        response: error.response,
        timestamp: new Date().toISOString(),
      });
      const errorMessage =
        error.response?.data?.message || error.message || error.toString();
      message.error(`获取配置失败: ${errorMessage}`);
      this.setState({ fetching: false });
    }
  };

  fetchLogoImage = async () => {
    try {
      console.log('开始获取登录页面Logo');
      const response = await ustackClient.get_login_page_logo();
      console.log('获取登录页面Logo成功:', response);

      const { logo } = response || {};

      if (logo) {
        // 如果没有 data:image 前缀，则添加默认 png 前缀
        const logoUrl = logo.startsWith('data:')
          ? logo
          : `data:image/png;base64,${logo}`;
        console.log('创建Logo URL成功');
        this.setState({
          logoUrl,
          fileList: [
            {
              uid: '-1',
              name: 'login-page-logo.png',
              status: 'done',
              url: logoUrl,
            },
          ],
        });
      } else {
        console.log('返回数据中没有logo字段');
      }
    } catch (error) {
      console.error('获取登录页面Logo失败:', {
        message: error.message,
        stack: error.stack,
        response: error.response,
        timestamp: new Date().toISOString(),
      });
    }
  };

  fetchFaviconImage = async () => {
    try {
      console.log('开始获取Favicon');
      const response = await ustackClient.get_favicon();
      console.log('获取Favicon成功:', response);

      const { favicon } = response || {};

      if (favicon) {
        // 如果没有 data:image 前缀，则添加默认 ico 前缀
        const faviconUrl = favicon.startsWith('data:')
          ? favicon
          : `data:image/x-icon;base64,${favicon}`;
        console.log('创建Favicon URL成功');
        this.setState({
          faviconUrl,
          faviconFileList: [
            {
              uid: '-1',
              name: 'favicon.ico',
              status: 'done',
              url: faviconUrl,
            },
          ],
        });
      } else {
        console.log('返回数据中没有favicon字段');
      }
    } catch (error) {
      console.error('获取Favicon失败:', {
        message: error.message,
        stack: error.stack,
        response: error.response,
        timestamp: new Date().toISOString(),
      });
    }
  };

  get leftCards() {
    return [
      {
        title: t('Other Settings'),
        render: () => this.renderForm(),
      },
    ];
  }

  handleSubmit = async (values) => {
    this.setState({ loading: true });
    try {
      // 保存其他设置配置
      console.log('开始保存其他设置配置:', values);
      await ustackClient.updateOtherSettings(values);
      console.log('保存其他设置配置成功');
      message.success('配置保存成功！');
      this.setState({ loading: false });

      // 如果更新了运营中心菜单设置，提示用户刷新页面
      if (values.show_operation_center_menu !== undefined) {
        console.log(
          '运营中心菜单设置已更新，当前值:',
          values.show_operation_center_menu
        );
        // message.info('菜单设置已更新，刷新页面后生效');
      }
    } catch (error) {
      // 增强错误处理和日志记录
      console.error('保存其他设置配置失败:', {
        message: error.message,
        stack: error.stack,
        response: error.response,
        submitValues: values,
        timestamp: new Date().toISOString(),
      });
      const errorMessage =
        error.response?.data?.message || error.message || error.toString();
      message.error(`保存配置失败: ${errorMessage}`);
      this.setState({ loading: false });
    }
  };

  handleUpload = async (file) => {
    try {
      console.log('开始上传Logo文件:', file.name);
      const formData = new FormData();
      formData.append('file', file);

      await ustackClient.upload_login_page_logo(formData, null, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        isFormData: true,
      });
      console.log('上传Logo成功');
      message.success('图片上传成功');
      // 重新获取Logo显示
      await this.fetchLogoImage();
    } catch (error) {
      console.error('上传Logo失败:', {
        message: error.message,
        stack: error.stack,
        response: error.response,
        fileName: file.name,
        timestamp: new Date().toISOString(),
      });
      message.error('图片上传失败');
    }
  };

  handleFaviconUpload = async (file) => {
    try {
      console.log('开始上传Favicon文件:', file.name);
      const formData = new FormData();
      formData.append('file', file);

      await ustackClient.upload_favicon(formData, null, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        isFormData: true,
      });
      console.log('上传Favicon成功');
      message.success('图片上传成功');
      // 重新获取Favicon显示
      await this.fetchFaviconImage();
    } catch (error) {
      console.error('上传Favicon失败:', {
        message: error.message,
        stack: error.stack,
        response: error.response,
        fileName: file.name,
        timestamp: new Date().toISOString(),
      });
      message.error('图片上传失败');
    }
  };

  handleDelete = () => {
    Modal.confirm({
      title: '删除登录页面Logo',
      content: '确定要删除此图片吗？',
      onOk: async () => {
        try {
          console.log('开始删除登录页面Logo');
          await ustackClient.delete_login_page_logo();
          console.log('删除登录页面Logo成功');
          message.success('图片删除成功');
          this.setState({
            logoUrl: null,
            fileList: [],
          });
        } catch (error) {
          console.error('删除登录页面Logo失败:', {
            message: error.message,
            stack: error.stack,
            response: error.response,
            timestamp: new Date().toISOString(),
          });
          message.error('图片删除失败');
        }
      },
    });
  };

  handleFaviconDelete = () => {
    Modal.confirm({
      title: '删除Favicon',
      content: '确定要删除此图片吗？',
      onOk: async () => {
        try {
          console.log('开始删除Favicon');
          await ustackClient.delete_favicon();
          console.log('删除Favicon成功');
          message.success('图片删除成功');
          this.setState({
            faviconUrl: null,
            faviconFileList: [],
          });
        } catch (error) {
          console.error('删除Favicon失败:', {
            message: error.message,
            stack: error.stack,
            response: error.response,
            timestamp: new Date().toISOString(),
          });
          message.error('图片删除失败');
        }
      },
    });
  };

  beforeUpload = (file) => {
    // 文件类型验证
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件');
      return false;
    }
    // 文件大小验证
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      const num = 5;
      message.error(`图片大小不能超过 ${num}MB！`);
      return false;
    }
    return true;
  };

  beforeFaviconUpload = (file) => {
    // ICO文件类型验证
    const isValidType =
      file.type === 'image/x-icon' || file.type === 'image/vnd.microsoft.icon';
    if (!isValidType) {
      message.error('只能上传ICO格式的文件');
      return false;
    }
    // 文件大小验证
    const isLt1M = file.size / 1024 / 1024 < 1;
    if (!isLt1M) {
      const num = 1;
      message.error(`图片大小不能超过 ${num}MB！`);
      return false;
    }
    return true;
  };

  renderForm() {
    const {
      loading,
      initialValues,
      fetching,
      fileList,
      logoUrl,
      faviconFileList,
      faviconUrl,
    } = this.state;

    console.log('渲染配置表单，当前状态:', {
      loading,
      fetching,
      hasInitialValues: !!initialValues,
      logoUrl: !!logoUrl,
      faviconUrl: !!faviconUrl,
    });

    // 表单布局配置
    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 8 },
      },
    };

    // Logo上传配置
    const uploadProps = {
      beforeUpload: this.beforeUpload,
      customRequest: ({ file }) => this.handleUpload(file),
      fileList,
      accept: 'image/*',
      showUploadList: false,
    };

    // Favicon上传配置
    const faviconUploadProps = {
      beforeUpload: this.beforeFaviconUpload,
      customRequest: ({ file }) => this.handleFaviconUpload(file),
      fileList: faviconFileList,
      accept: 'image/*',
      showUploadList: false,
    };

    return (
      <Form
        layout="horizontal"
        className="form-container"
        onFinish={this.handleSubmit}
        initialValues={initialValues}
        key={JSON.stringify(initialValues)}
        style={{ minWidth: '700px', margin: '0 10px', padding: '0 24px' }}
      >
        <Form.Item
          name="enable_captcha"
          {...formItemLayout}
          label={t('Login Captcha Verification')}
          valuePropName="checked"
        >
          <Switch disabled={fetching} />
        </Form.Item>
        <Form.Item
          name="enable_login_failure_check"
          {...formItemLayout}
          label={t('Login Failure Check')}
          valuePropName="checked"
        >
          <Switch disabled={fetching} />
        </Form.Item>
        <Form.Item
          name="max_login_failures"
          {...formItemLayout}
          label={t('Login Failure Count')}
        >
          <InputNumber min={1} max={10} disabled={fetching} />
        </Form.Item>
        <Form.Item
          name="login_page_title"
          {...formItemLayout}
          label={t('Login Page Title')}
          rules={[
            {
              max: 30,
              message: t('Maximum 30 characters allowed'),
            },
          ]}
        >
          <Input disabled={fetching} />
        </Form.Item>
        <Form.Item
          name="home_page_title"
          {...formItemLayout}
          label={t('Home Page Title')}
          rules={[
            {
              max: 30,
              message: t('Maximum 30 characters allowed'),
            },
          ]}
        >
          <Input disabled={fetching} />
        </Form.Item>
        <Form.Item
          name="page_title"
          {...formItemLayout}
          label={t('Browser Tab Title')}
          rules={[
            {
              max: 30,
              message: t('Maximum 30 characters allowed'),
            },
          ]}
        >
          <Input disabled={fetching} />
        </Form.Item>
        <Form.Item
          name="show_uccps_button"
          {...formItemLayout}
          label={t('Show UCCPS Button')}
          valuePropName="checked"
        >
          <Switch disabled={fetching} />
        </Form.Item>
        <Form.Item
          name="show_large_screen_button"
          {...formItemLayout}
          label={t('Show Large Screen Button')}
          valuePropName="checked"
        >
          <Switch disabled={fetching} />
        </Form.Item>
        <Form.Item
          name="show_health_inspection_button"
          {...formItemLayout}
          label={t('Show Health Inspection Button')}
          valuePropName="checked"
        >
          <Switch disabled={fetching} />
        </Form.Item>
        <Form.Item
          name="show_new_health_inspection_button"
          {...formItemLayout}
          label={t('Show New Health Inspection Button')}
          valuePropName="checked"
        >
          <Switch disabled={fetching} />
        </Form.Item>
        <Form.Item
          name="show_ticket_management_menu"
          {...formItemLayout}
          label={t('Show Ticket Management Menu')}
          valuePropName="checked"
        >
          <Switch disabled={fetching} />
        </Form.Item>
        {/* 运营中心菜单显示/隐藏开关 */}
        <Form.Item
          name="show_operation_center_menu"
          {...formItemLayout}
          label="显示运营中心菜单"
          valuePropName="checked"
        >
          <Switch disabled={fetching} />
        </Form.Item>
        <Form.Item {...formItemLayout} label={t('Login Page Logo')}>
          {logoUrl ? (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{ marginRight: '16px' }}>
                <Image
                  src={logoUrl}
                  alt="Login Logo"
                  width={120}
                  height={80}
                  preview={{ mask: t('Preview') }}
                  style={{
                    desplay: 'flex',
                    maxWidth: '100%',
                    maxHeight: '100%',
                    objectFit: 'contain',
                    border: '1px solid #d9d9d9',
                    borderRadius: '2px',
                    backgroundColor: '#fafafa',
                  }}
                />
              </div>
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={this.handleDelete}
              >
                {t('Delete')}
              </Button>
            </div>
          ) : (
            <Upload {...uploadProps}>
              <Button icon={<PlusOutlined />}>{t('Upload Image')}</Button>
            </Upload>
          )}
          <div style={{ marginTop: 8 }}>
            {t('Supported formats: PNG, JPG, JPEG. Max file size: 5MB')}
          </div>
        </Form.Item>
        <Form.Item {...formItemLayout} label={t('Favicon')}>
          {faviconUrl ? (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{ marginRight: '16px' }}>
                <Image
                  src={faviconUrl}
                  alt="Favicon"
                  width={32}
                  height={32}
                  preview={{ mask: t('Preview') }}
                  style={{
                    desplay: 'flex',
                    maxWidth: '100%',
                    maxHeight: '100%',
                    objectFit: 'contain',
                    border: '1px solid #d9d9d9',
                    borderRadius: '2px',
                    backgroundColor: '#fafafa',
                  }}
                />
              </div>
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={this.handleFaviconDelete}
              >
                {t('Delete')}
              </Button>
            </div>
          ) : (
            <Upload {...faviconUploadProps}>
              <Button icon={<PlusOutlined />}>{t('Upload Image')}</Button>
            </Upload>
          )}
          <div style={{ marginTop: 8 }}>
            {t('Supported formats: ICO. Max file size: 1MB')}
          </div>
        </Form.Item>
        <Form.Item
          wrapperCol={{
            xs: { span: 15, offset: 0 },
            sm: { span: 8, offset: 5 },
          }}
        >
          <Button
            type="primary"
            htmlType="submit"
            loading={loading || fetching}
          >
            {t('Save')}
          </Button>
        </Form.Item>
      </Form>
    );
  }

  render() {
    return (
      <div
        style={{
          height: '100%',
          overflow: 'auto',
          padding: '16px',
        }}
      >
        {super.render()}
      </div>
    );
  }
}
