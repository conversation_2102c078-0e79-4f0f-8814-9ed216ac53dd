// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { ConfirmAction } from 'containers/Action';
import globalSettingStore from 'stores/skyline/setting';
import { onlyAdminCanChangePolicy } from 'resources/skyline/policy';

export default class Reset extends ConfirmAction {
  get id() {
    return 'reset';
  }

  get title() {
    return t('Reset To Initial Value');
  }

  get buttonType() {
    return 'primary';
  }

  get isDanger() {
    return true;
  }

  get actionName() {
    return t('Reset To Initial Value');
  }

  getItemName = (data) => data.key;

  policy = onlyAdminCanChangePolicy;

  onSubmit = (item) => {
    const { key: id } = item || this.item;
    return globalSettingStore.delete({ id });
  };
}
