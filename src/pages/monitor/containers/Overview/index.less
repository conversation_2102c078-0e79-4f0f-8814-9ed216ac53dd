.container {
  .card {
    height: 100%;
    padding: 16px;
    color: #000;
    background-color: #fff;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 5%), 0 0 10px 0 rgba(0, 0, 0, 5%);

    .tabs {
      :global {
        .ant-tabs-tab {
          margin-right: 20px;
          border-bottom: 1px solid #f0f0f0;
        }

        .ant-tabs-nav::before {
          border-bottom: none;
        }
      }
    }
  }

  .alert-card-line {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .number {
      color: rgb(232, 104, 4);
      font-size: 36px;
    }
  }
}

.outer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-size: 14px;

  .inner {
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: scroll;
  }

  .inner::-webkit-scrollbar {
    display: none;
  }
}

.top-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  font-weight: 500;
  font-size: 24px;
}

.tabs {
  :global {
    .ant-tabs-tab {
      margin-right: 20px;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-tabs-nav::before {
      border-bottom: none;
    }
  }
}
