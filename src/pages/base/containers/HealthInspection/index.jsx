import React, { useEffect, useState } from 'react';
import cookieStore from 'stores/ustack/cookies';
import {
  clearLocalStorage,
  getLocalStorageItem,
  setLocalStorageItem,
} from 'src/utils/local-storage';
import { notification } from 'antd';
import { statusMap } from 'src/utils/code';
import styles from './index.less';

export default function HealthInspection() {
  const [ustackToken, setUstackToken] = useState(
    getLocalStorageItem('ustack_token')
  );
  const [isFirst, setIsFirst] = useState(true);
  const [ustackUrl, setUstackUrl] = useState(getLocalStorageItem('ustack_url'));
  function UStackToken(iframe, clearToken) {
    if (ustackToken && !clearToken) {
      iframe.src = `${ustackUrl}/admin/safe/health?token=${ustackToken}&page=/admin/safe/health`;
      document.getElementById('health-container').appendChild(iframe);
      // 如果需要监听iframe加载完成，可以使用load事件
      iframe.onload = function () {
        const BFrame = window.frames[0];
        BFrame.postMessage({ page: 'health', token: ustackToken }, ustackUrl);
      };
    } else {
      cookieStore
        .getUStackToken()
        .then((res) => {
          const tempUrl = res.url;
          setUstackToken(res.token);
          setUstackUrl(tempUrl);
          setLocalStorageItem('ustack_token', res.token, null, res.expire);
          setLocalStorageItem('ustack_url', tempUrl, null, res.expire);
          iframe.src = `${tempUrl}?token=${res.token}&page=/admin/safe/health`;
          if (isFirst) {
            setIsFirst(false);
            document.getElementById('health-container').appendChild(iframe);

            // 如果需要监听iframe加载完成，可以使用load事件
            iframe.onload = function () {
              const BFrame = window.frames[0];
              BFrame.postMessage({ page: 'health', token: res.token }, tempUrl);
            };
          }
        })
        .catch((error) => {
          notification.error({
            message: error.response.data.detail
              ? error.response.data.detail
              : statusMap[error.response.status],
          });
        });
    }
  }
  function loadUrlAsync() {
    const iframe = document.createElement('iframe');
    iframe.id = 'reportFrame';
    iframe.style.width = '100%';
    iframe.style.frameBorder = 0;
    UStackToken(iframe);
  }
  useEffect(() => {
    // js创建iframe
    // 使用函数异步加载URL
    loadUrlAsync();
    window.addEventListener('message', function (event) {
      if (event.data === 'clear') {
        clearLocalStorage(['keystone_token', 'loglevel:webpack-dev-server']);
        setUstackToken('');
        const iframeY = document.getElementById('reportFrame');
        UStackToken(iframeY, true);
      }
    });
  }, []);
  return <div id="health-container" className={styles.container} />;
}
