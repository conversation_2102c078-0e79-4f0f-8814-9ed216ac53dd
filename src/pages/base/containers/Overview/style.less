@import '~antd/lib/style/themes/default.less';
@import '~styles/variables';

.container {
  height: 100%;
  padding: @overview-padding;
  overflow: auto;

  .main-icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .action-button {
    color: #a3a3a3;
    font-size: 18px;
    line-height: 88px;
    background: #fff;
    border-radius: @border-radius;
    box-shadow: 0 2px 20px 0 rgba(0, 0, 0, 9%);
    opacity: 0.9;
  }

  .action-icon {
    display: block;
    width: 50px;
    height: 50px;
    margin-left: 60px;
  }

  :global {
    .ant-card {
      box-shadow: 0 2px 20px 0 rgba(0, 0, 0, 9%);
    }

    .ant-card-head {
      font-size: 16px;
    }
  }

  .left {
    height: 100%;

    .top {
      .title {
        display: table-cell;
        vertical-align: bottom;

        .text {
          color: #252525;
          font-size: 21px;
        }

        .action {
          margin-left: 40px;
          color: #000;
          font-size: 16px;
        }
      }
    }
  }

  .right {
    height: 100%;

    .project {
      position: relative;

      .meta {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px dashed #e8e8e8;
      }

      :global {
        .ant-descriptions-item-label {
          width: 130px;
        }
      }
    }
  }
}

.outer {
  position: relative;
  width: 100%;
  height: 10rem;
  overflow: hidden;
  font-size: 12px;

  .inner {
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: scroll;
  }

  .inner::-webkit-scrollbar {
    display: none;
  }
}

.bottom {
  border-radius: @border-radius;

  :global {
    .ant-card {
      box-shadow: unset;
    }

    .ant-card-body {
      padding: 0 !important;
    }
  }

  .title {
    .text {
      color: #252525;
      font-size: 16px;
    }

    .badge {
      margin-left: 22px;

      :global {
        .ant-badge-status-dot {
          width: 10px;
          height: 10px;
        }

        .ant-badge-status-text {
          font-size: 14px;
        }
      }
    }
  }

  .action {
    float: right;
    margin-top: 6px;
    color: @primary-color;
    font-size: 12px;
    cursor: pointer;
  }

  .content {
    .card {
      padding: 8px;

      :global {
        .ant-card-head {
          border-bottom: none;

          .ant-card-head-title {
            padding-bottom: 0;
            color: #565656;
            font-weight: 500;
            font-size: 16px;
          }
        }

        .ant-card-body {
          padding: 12px !important;
          box-shadow: unset !important;
        }
      }

      .progress-title {
        overflow: hidden;
        font-size: 14px;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
