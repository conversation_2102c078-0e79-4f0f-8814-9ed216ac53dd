// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { inject, observer } from 'mobx-react';
import { PortDetail as Base } from 'pages/network/containers/Router/Port/Detail';

class PortDetail extends Base {
  get listUrl() {
    const { routerId, firewallId } = this.params;
    if (routerId) {
      return this.getRoutePath(
        'routerDetail',
        { id: routerId },
        { tab: 'interfaces' }
      );
    }
    return this.getRoutePath(
      'firewallDetail',
      { id: firewallId },
      { tab: 'ports' }
    );
  }
}

export default inject('rootStore')(observer(PortDetail));
