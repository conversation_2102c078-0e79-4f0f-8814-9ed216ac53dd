// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { inject, observer } from 'mobx-react';
import Base from 'containers/TabDetail';
import globalReverseStore from 'stores/designate/reverse';
import BaseDetail from './BaseDetail';
import actionConfigs from '../actions';

export class ReverseDetail extends Base {
  init() {
    this.store = globalReverseStore;
  }

  get name() {
    return t('Reverse Detail');
  }

  get policy() {
    return 'get_image';
  }

  get listUrl() {
    return this.getRoutePath('dns-reverse');
  }

  get actionConfigs() {
    return actionConfigs;
  }

  get tabs() {
    const tabs = [
      {
        title: t('Detail'),
        key: 'detail',
        component: BaseDetail,
      },
    ];
    return tabs;
  }
}

export default inject('rootStore')(observer(ReverseDetail));
