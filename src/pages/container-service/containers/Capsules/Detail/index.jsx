// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { inject, observer } from 'mobx-react';
import Base from 'containers/TabDetail';
import globalCapsulesStore from 'src/stores/zun/capsules';
import { capsuleStatus } from 'resources/zun/capsule';
import BaseDetail from './BaseDetail';
import actionConfigs from '../actions';

export class CapsulesDetail extends Base {
  init() {
    this.store = globalCapsulesStore;
  }

  get name() {
    return t('Cluster Template Detail');
  }

  get listUrl() {
    return this.getRoutePath('zunCapsules');
  }

  get policy() {
    return 'capsule:get_one_all_projects';
  }

  get actionConfigs() {
    return actionConfigs;
  }

  get detailInfos() {
    return [
      {
        title: t('Name'),
        dataIndex: 'meta_name',
      },
      {
        title: t('Status'),
        dataIndex: 'status',
        valueMap: capsuleStatus,
      },
      {
        title: t('Created At'),
        dataIndex: 'created_at',
        valueRender: 'toLocalTime',
      },
      {
        title: t('Updated At'),
        dataIndex: 'updated_at',
        valueRender: 'toLocalTime',
      },
    ];
  }

  get tabs() {
    return [
      {
        title: t('Detail'),
        key: 'general_info',
        component: BaseDetail,
      },
    ];
  }
}

export default inject('rootStore')(observer(CapsulesDetail));
