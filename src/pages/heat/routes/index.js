// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import BaseLayout from 'layouts/Basic';
import E404 from 'pages/base/containers/404';
import Stack from '../containers/Stack';
import StackDetail from '../containers/Stack/Detail';
import CreateStack from '../containers/Stack/actions/Create';

const PATH = '/heat';
export default [
  {
    path: PATH,
    component: BaseLayout,
    routes: [
      { path: `${PATH}/stack`, component: Stack, exact: true },
      { path: `${PATH}/stack/create`, component: CreateStack, exact: true },
      {
        path: `${PATH}/stack/edit/:id/:name`,
        component: CreateStack,
        exact: true,
      },
      {
        path: `${PATH}/stack/detail/:id/:name`,
        component: StackDetail,
        exact: true,
      },
      // remove heat page in the administrator,
      // because the heat api has a problem with the permission determination
      // of the scope.system.all=true level.
      // { path: `${PATH}/stack-admin`, component: Stack, exact: true },
      // {
      //   path: `${PATH}/stack-admin/create`,
      //   component: CreateStack,
      //   exact: true,
      // },
      // {
      //   path: `${PATH}/stack-admin/edit/:id/:name`,
      //   component: CreateStack,
      //   exact: true,
      // },
      // {
      //   path: `${PATH}/stack-admin/detail/:id/:name`,
      //   component: StackDetail,
      //   exact: true,
      // },
      { path: '*', component: E404 },
    ],
  },
];
