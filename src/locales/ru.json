{"3600": "3600", " You can go to the console to ": "Вы можете перейти в консоль, чтобы", "\"Shared\" volume can be mounted on multiple instances": "\"Shared\" volume можно примонтировать на нескольких инстансах", "\"v=spf1 ipv4=********* include:examplesender.email +all\" <ul><li><b>v=spf1:</b> Tells the server that this contains an SPF record. Every SPF record must begin with this string.</li> <li><b>Guest List:</b> Then comes the “guest list” portion of the SPF record or the list of authorized IP addresses. In this example, the SPF record is telling the server that ipv4=********* is authorized to send emails on behalf of the domain.</li> <li><b>include:examplesender.net:</b> is an example of the include tag, which tells the server what third-party organizations are authorized to send emails on behalf of the domain. This tag signals that the content of the SPF record for the included domain (examplesender.net) should be checked and the IP addresses it contains should also be considered authorized. Multiple domains can be included within an SPF record but this tag will only work for valid domains.</li><li><b>-all:</b> Tells, the server that addresses not listed in the SPF record are not authorized to send emails and should be rejected.</li></ul>": "", "'ip' rule represents IPv4 or IPv6 address, 'cert' rule represents TLS certificate, 'user' rule represents username or usergroup, 'cephx' rule represents ceph auth ID.": "'ip' правило представляет собой IPv4 или IPv6 адрес, 'cert' правило представляет собой сертификат TLS, 'user' правило представляет собой имя пользователя или группу пользователей, 'cephx' правило представляет собой идентификатор аутентификации ceph.", "-1 means no connection limit": "-1 означает отсутствие ограничения по соединениям", ".": ".", "0 iodef mailto:<EMAIL> <ul><li><b>0:</b> is flag. An unsigned integer between 0-255.</li> <li><b>iodef:</b> An ASCII string that represents the identifier of the property represented by the record.<br />Available Tags: \"issue\", \"issuewild\", \"iodef\"</li><li><b>mailto:<EMAIL>:</b> The value associated with the tag.</li></ul>": "", "1. The backup can only capture the data that has been written to the volume at the beginning of the backup task, excluding the data in the cache at that time.": "1. Резервное копирование может захватывать только данные, записанные на диске в начале задачи по резервному копированию, исключая данные в кэше на тот момент.", "1. The name of the custom resource class property should start with CUSTOM_, can only contain uppercase letters A ~ Z, numbers 0 ~ 9 or underscores, and the length should not exceed 255 characters (for example: CUSTOM_BAREMETAL_SMALL).": "1. Имя свойства пользовательского класса ресурсов должно начинаться с CUSTOM_, может содержать только заглавные буквы A ~ Z, цифры 0 ~ 9 или подчеркивания, и длина не должна превышать 255 символов (например: CUSTOM_BAREMETAL_SMALL).", "1. The name of the trait should start with CUSTOM_, can only contain uppercase letters A ~ Z, numbers 0 ~ 9 or underscores, and the length should not exceed 255 characters (for example: CUSTOM_TRAIT1).": "1. Имя характеристики должно начинаться с CUSTOM_, может содержать только заглавные буквы A ~ Z, цифры 0 ~ 9 или подчеркивания, и длина не должна превышать 255 символов (например: CUSTOM_TRAIT1).", "1. The volume associated with the backup is available.": "1. <PERSON><PERSON><PERSON><PERSON><PERSON>, связанный с резервной копией, доступен.", "1. You can create {resources} using ports or port ranges.": "1. Вы можете создавать {ресурсы} с использованием портов или диапазонов портов.", "10 0 5060 server1.example.com. <ul><li>\"10\" is the priority of the record. The lower the value, the higher the priority.</li><li>0 is the weight of the record. This is the weight of which this record has a chance to be used when there are multiple matching SRV records of the same priority.</li><li>5060 is the port of the record. This specifies the port on which the application or service is running.</li> <li>server1.example.com is the target of the record. This specifies the domain of the application or service the record is for. SRV records must specify a target which is either an A record or AAAA record, and may not use CNAME records.</li></ul>": "", "10 mail.example.com <ul><li><b>10:</b> Priority</li> <li><b>mail.example.com:</b> Value</li></ul>": "", "10s": "10с", "1D": "1Д", "1H": "1Ч", "1min": "1М", "2. In the same protocol, you cannot create multiple {resources} for the same source port or source port range.": "2. В рамках одного протокола вы не можете создавать несколько {ресурсов} для одного и того же исходного порта или диапазона исходных портов.", "2. The trait of the scheduled node needs to correspond to the trait of the flavor used by the ironic instance; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all the necessary traits (for example, the ironic instance which use the flavor that has CUSTOM_TRAIT1 as a necessary trait, can be scheduled to the node which has the trait of CUSTOM_TRAIT1).": "2. Характеристика запланированного узла должна соответствовать характеристике образца, используемого инстансом ironic; путем внедрения необходимых характеристик в ironic, вычислительная служба будет планировать инстанс только на узел без железа с необходимыми характеристиками (например, инстанс ironic, который использует образец с CUSTOM_TRAIT1 как необходимую характеристику, может быть запланирован на узел с характеристикой CUSTOM_TRAIT1).", "2. The volume associated with the backup has been mounted, and the instance is shut down.": "2. <PERSON><PERSON><PERSON><PERSON><PERSON>, связанный с резервной копией, был подключен, и инстанс был выключен.", "2. To ensure the integrity of the data, it is recommended that you suspend the write operation of all files when creating a backup.": "2. Для обеспечения целостности данных рекомендуется приостановить операцию записи всех файлов при создании резервной копии.", "2. You can customize the resource class name of the flavor, but it needs to correspond to the resource class of the scheduled node (for example, the resource class name of the scheduling node is baremetal.with-GPU, and the custom resource class name of the flavor is CUSTOM_BAREMETAL_WITH_GPU=1).": "2. Вы можете настроить имя класса ресурсов образца, но оно должно соответствовать классу ресурсов запланированного узла (например, имя класса ресурсов узла планирования - baremetal.with-GPU, а пользовательское имя класса ресурсов образца - CUSTOM_BAREMETAL_WITH_GPU=1).", "3. When using a port range to create a port mapping, the size of the external port range is required to be the same as the size of the internal port range. For example, the external port range is 80:90 and the internal port range is 8080:8090.": "3. При использовании диапазона портов для создания отображения портов требуется, чтобы размер внешнего диапазона портов был таким же, как размер внутреннего диапазона портов. Например, внешний диапазон портов - 80:90, а внутренний диапазон портов - 8080:8090.", "4 2 123456789abcdef67890123456789abcdef67890123456789abcdef123456789 <ul> <li><b>4 is Algorithm:</b> Algorithm (0: reserved; 1: RSA; 2: DSA, 3: ECDSA; 4: Ed25519; 6:Ed448)</li> <li><b>2 is Type:</b> Algorithm used to hash the public key (0: reserved; 1: SHA-1; 2: SHA-256)</li> <li><b>Last parameter is Fingerprint:</b> Hexadecimal representation of the hash result, as text</li> </ul>": "", "4. When you use a port range to create {resources}, multiple {resources} will be created in batches. ": "4. При использовании диапазона портов для создания {ресурсов} будет создано несколько {ресурсов} пакетами.", "5min": "5 минут", "8 to 16 characters, at least one uppercase letter, one lowercase letter, one number.": "8 до 16 символов, как минимум одна заглавная буква, одна строчная буква и одна цифра.", "8 to 32 characters, at least one uppercase letter, one lowercase letter, one number and one special character.": "", "<username> or <username>@<domain>": "<имя пользователя> или <имя пользователя>@<домен>", "A command that will be sent to the container": "Команда, которая будет отправлена в контейнер", "A container with the same name already exists": "Контейнер с таким же именем уже существует", "A dynamic scheduling algorithm that estimates the server load based on the number of currently active connections. The system allocates new connection requests to the server with the least number of current connections. Commonly used for long connection services, such as database connections and other services.": "Динамический алгоритм планирования, который оценивает нагрузку сервера на основе количества активных соединений. Система выделяет новые запросы на соединение серверу с наименьшим количеством текущих соединений. Часто используется для долгосрочных сервисов с соединением, таких как подключения к базам данных и другие сервисы.", "A host aggregate can be associated with at most one AZ. Once the association is established, the AZ cannot be disassociated.": "Хост-агрегат может быть связан максимум с одной зоной доступности. После установления связи, её нельзя разорвать.", "A public container will allow anyone to use the objects in your container through a public URL.": "Публичный контейнер позволит любому пользователю использовать объекты в вашем контейнере по общедоступному URL-адресу.", "A rule specified before insertion or after insertion a rule. If both are not specified, the new rule is inserted as the first rule of the policy.": "", "A snapshot is an image which preserves the disk state of a running instance, which can be used to start a new instance.": "Снимок - это образ, который сохраняет состояние диска работающего инстанса и может быть использовано для запуска нового инстанса.", "A template is a YAML file that contains configuration information, please enter the correct format.": "Шаблон - это YAML-файл, который содержит информацию о конфигурации. Пожалуйста, введите правильный формат.", "A template is a YAML file that contains configuration information.": "Шаблон - это YAML-файл, который содержит информацию о конфигурации.", "ADMINISTRATOR": "АДМИНИСТРАТОР", "ADOPT COMPLETE": "ПРИНЯТО ЗАВЕРШЕНО", "AH": "AH", "AKI - Amazon kernel image format": "AKI - формат образа ядра Amazon", "ALLOW": "", "AMI - Amazon server image format": "AMI - формат образа сервера Amazon", "ANY": "ЛЮБОЙ", "API Address": "Адрес API", "ARI - Amazon ramdisk image format": "ARI - формат ramdisk от Amazon", "ARM Architecture": "Архитектура ARM", "Abandon Stack": "Отказаться от стека", "Abandoning this stack will preserve the resources deployed by the stack.": "Отмена этого стека сохранит ресурсы, развернутые стеком.", "Abort Upload": "Прервать загрузку", "Accept Volume Transfer": "Принять передачу диска", "Access Control": "Контроль доступа", "Access Key": "Ключ доступа", "Access Level": "Уровень доступа", "Access Rules": "Правила доступа", "Access Rules Status": "Статус правил доступа", "Access To": "Доступ к", "Access Type": "Тип доступа", "Access Type Setting": "Настройка типа доступа", "Action": "Действие", "Action Logs": "<PERSON>у<PERSON><PERSON><PERSON> действий", "Active": "Акти<PERSON><PERSON>н", "Active Status": "Активный статус", "Add": "Добавить", "Add Access Rule": "Добавить правило доступа", "Add Custom Metadata": "Добавить пользовательские метаданные", "Add Data Disks": "Добавить диски", "Add Environment Variable": "Добавить переменную окружения", "Add Exposed Ports": "Добавить открытые порты", "Add External Members": "Добавить внешних участников", "Add Extra Info": "Добавить дополнительную информацию", "Add Extra Spec": "Добавить дополнительную спецификацию", "Add Host": "", "Add IP": "Добавить IP", "Add Label": "Добавить ярлык", "Add Member": "Добавить участника", "Add Metadata": "Добавить метаданные", "Add NUMA Node": "Добавить узел NUMA", "Add Network": "Добавить сеть", "Add Policy": "", "Add Property": "Добавить свойство", "Add Router": "Добавить маршрутизатор", "Add Virtual LAN": "Добавить виртуальную LAN", "Add hosts to the aggregate or remove hosts from it. Hosts can be in multiple aggregates.": "Добавьте хосты в агрегат или удалите хосты из него. Хосты могут находиться в нескольких агрегатах.", "Add network": "Добавить сеть", "Add scheduler hints": "Добавить подсказки планировщика", "Additional Labels": "Дополнительные ярлыки", "Additional routes announced to the instance, one entry per line(e.g. *************/24,***********)": "Дополнительные маршруты, объявленные для инстанса, по одной записи на строку (например, *************/24,***********)", "Additional routes announced to the instance, one entry per line(e.g. {ip})": "Дополнительные маршруты, объявленные для инстанса, по одной записи на строку (например, {ip})", "Address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Address Record": "Запись адреса", "Addresses": "Адреса", "Admin State": "Административное состояние", "Admin State Up": "Административное состояние - Включено", "Admin Status": "Административный статус", "Administrator": "Администратор", "Adopt Complete": "Процесс принятия завершен", "Adopt Failed": "Ошибка при принятии", "Adopt In Progress": "Процесс принятия выполняется", "Advanced": "Дополнительно", "Advanced Options": "Дополнительные опции", "Advanced Params": "Дополнительные параметры", "Affiliated Domain": "До<PERSON><PERSON>н аффилирован", "Affiliated Domain ID/Name": "Идентификатор/Название аффилированного домена", "Affinity": "Аф<PERSON>инность", "Affinity (mandatory):": "Аффинность (обязательная):", "Affinity (not mandatory):": "Аффинность (необязательная):", "Afghanistan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "After attaching interface, you may need to login the instance to update the network interface configuration and restart the network service.": "После подключения интерфейса вам может потребоваться войти в инстанс, чтобы обновить конфигурацию сетевого интерфейса и перезапустить сетевую службу.", "After disable the compute service, the new instance will not schedule to the compute node.": "После отключения службы вычисления, новый инстанс не будет планироваться на вычислительном узле.", "After shelving, the instance will be shut down, resources will be released, and the snapshot will be saved to Glance. This will take about a few minutes, please be patient. You also can choose to unshelve to restore the instance.": "После перевода в режим ожидания, инстанс будет выключен, ресурсы будут освобождены, и снимок будет сохранен в Glance. Это займет несколько минут, пожалуйста, будьте терпеливы. Вы также можете выбрать восстановление для выхода из режима ожидания.", "After the share is expanded, the share cannot be reduced.": "После расширения общего доступа его нельзя уменьшить.", "After the volume is expanded, the volume cannot be reduced.": "После расширения объема его нельзя уменьшить.", "Agent": "Агент", "Agree to force shutdown": "Согласиться на принудительное выключение", "Albania": "Албания", "Algeria": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "All": "Все", "All Flavors": "Все типы инста<PERSON><PERSON>ов", "All ICMP": "Все ICMP", "All Images": "Все образы", "All Networks": "Все сети", "All Port": "Все порты", "All Proto": "Все протоколы", "All QoS Policies": "Все политики QoS", "All TCP": "Все TCP", "All UDP": "Все UDP", "All data downloaded.": "Все данные загружены.", "All network segments are indicated by \"*\", not \"0.0.0.0/0\"": "Все сегменты сети обозначаются символом \"*\", а не \"0.0.0.0/0\"", "Allocate IP": "Выделить IP", "Allocation Pools": "Пулы выделения", "Allowed Address Pairs": "Разрешенные пары адресов", "Allowed Host": "Разрешенный хост", "Always": "Всегда", "American Samoa": "Американское Самоа", "An object with the same name already exists": "Объект с таким же именем уже существует", "Andorra": "Андорра", "Angola": "Ангола", "Anguilla": "Ангилья", "Anti-Affinity": "Анти-аффинность", "Anti-affinity (mandatory):": "Анти-аффинность (обязательная):", "Anti-affinity (not mandatory):": "Анти-аффинность (необязательная):", "Antigua and Barbuda": "Антигуа и Барбуда", "Any": "Любой", "Any(Random)": "Люб<PERSON>й (случайный)", "Application Credentials": "Учетные данные приложения", "Application Template": "Шаблон приложения", "Apply Latency(ms)": "Применить задержку (мс)", "Applying": "Применение", "Arch": "<PERSON>р<PERSON>.", "Architecture": "Архитектура", "Are you sure set the project { project } as the default project? User login is automatically logged into the default project.": "", "Are you sure to cancel transfer volume { name }? ": "Вы уверены, что хотите отменить передачу диска { name }?", "Are you sure to delete instance { name }? ": "Вы уверены, что хотите удалить инстанс { name }?", "Are you sure to delete volume { name }? ": "Вы уверены, что хотите удалить диск { name }?", "Are you sure to download data?": "Вы уверены, что хотите загрузить данные?", "Are you sure to forbidden domain { name }? Forbidden the domain will have negative effect, and users associated with the domain will not be able to log in if they are only assigned to the domain": "Вы уверены, что хотите запретить домен { name }? Запрет домена окажет негативное воздействие, и пользователи, связанные с доменом, не смогут войти, если им разрешен доступ только к домену", "Are you sure to forbidden project { name }? Forbidden the project will have negative effect, and users associated with the project will not be able to log in if they are only assigned to the project": "Вы уверены, что хотите запретить проект { name }? Запрет проекта окажет негативное воздействие, и пользователи, связанные с проектом, не смогут войти, если им разрешен доступ только к проекту", "Are you sure to forbidden user { name }? Forbidden the user will not allow login in ": "Вы уверены, что хотите запретить пользователя { name }? Запрет пользователя не позволит ему входить в систему", "Are you sure to jump directly to the console? The console will open in a new page later.": "Вы уверены, что хотите перейти непосредственно в консоль? Консоль будет открыта в новой вкладке.", "Are you sure to remove the default project?": "", "Are you sure to shelve instance { name }? ": "Вы уверены, что хотите приостановить инстанс { name }?", "Are you sure to { action } {name}?": "Вы уверены, что хотите { action } {name}?", "Are you sure to {action} (Host: {name})?": "", "Are you sure to {action} (Segment: {name})?": "", "Are you sure to {action} (instance: {name})?": "Вы уверены, что хотите {action} (инстанс: {name})?", "Are you sure to {action}?": "Вы уверены, что хотите {action}?", "Are you sure to {action}? (Record Set: {name} - {id})": "Вы уверены, что хотите {action}? (Набор записей: {name} - {id})", "Are you sure to {action}? (Zone: {name})": "Вы уверены, что хотите {action}? (З<PERSON><PERSON>: {name})", "Argentina": "Арген<PERSON><PERSON>на", "Armenia": "Армения", "Aruba": "Аруба", "Associate": "Ассоциировать", "Associate Floating IP": "Ассоциировать плавающий IP", "Associate IP": "Ассоциировать IP", "Associate Network": "Ассоциировать сеть", "Associated Ports": "", "Associated QoS Spec ID": "Идентификатор связанной спецификации QoS", "Associated QoS Spec ID/Name": "Идентификатор/имя связанной спецификации QoS", "Associated Resource": "Связанный ресурс", "Associated Resource Types": "Типы связанных ресурсов", "Associated Resources": "Связанные ресурсы", "Associations": "Ассоциации", "Attach": "Прикрепить", "Attach Instance": "Прикрепить инстанс", "Attach Interface": "Прикрепить интерфейс", "Attach Network": "Прикрепить сеть", "Attach Security Group": "Прикрепить группу безопасности", "Attach USB": "Прикрепить USB", "Attach Volume": "Прикрепить Диск", "Attach volume": "Прикрепить диск", "Attached Device": "Подключенное устройство", "Attached To": "Прикреплен к", "Attaching": "Прикрепление", "Attachments Info": "Информация о прикреплениях", "Attributes": "Атрибуты", "Audited": "", "Australia": "Австралия", "Austria": "Австрия", "Auth Algorithm": "Алгоритм аутентификации", "Auth Key": "<PERSON><PERSON><PERSON><PERSON> аутентификации", "Auto": "Авто", "Auto Healing": "Автоматическое восстановление", "Auto Inspect": "Автоматическая проверка", "Auto Scaling": "Автомасштабирование", "Auto allocate mac address": "Автоматическое выделение MAC-адреса", "Auto scaling feature will be enabled": "Функция автоматического масштабирования будет включена", "Automatically Assigned Address": "Автоматически назначенный адрес", "Automatically repair unhealhty nodes": "", "Availability Zone": "Зона доступности", "Availability Zone Hints": "Подсказки зоны доступности", "Availability Zone Info": "Информация о зоне доступности", "Availability Zone Name": "Название зоны доступности", "Availability Zones": "Зоны доступности", "Availability zone refers to a physical area where power and network are independent of each other in the same area. In the same region, the availability zone and the availability zone can communicate with each other in the intranet, and the available zones can achieve fault isolation.": "Зона доступности относится к физической области, где электропитание и сеть независимы друг от друга в одной области. В одном регионе зона доступности и зона доступности могут взаимодействовать друг с другом внутри сети, и доступные зоны могут обеспечить изоляцию от сбоев.", "Available": "Доступно", "Available Zone": "Доступная зона", "Average PGs per OSD": "Среднее количество PG на OSD", "Awaiting Transfer": "Ожидание передачи", "Azerbaijan": "Азерб<PERSON>йджан", "BLOCK I/O(B)": "BLOCK I/O(B)", "Back": "Назад", "Back End": "Серверная часть", "Back to Home": "Вернуться на главную", "Back to login page": "Вернуться на страницу входа", "Backend": "Серверная часть", "Backend Name": "Имя серверной части", "Backing Up": "Резервное копирование", "Backup": "Резервное копирование", "Backup Detail": "Детали резервного копирования", "Backup File": "Файл резервной копии", "Backup File Location": "Местоположение файла резервной копии", "Backup Mode": "Режим резервного копирования", "Backups": "Резервные копии", "Backups & Snapshots": "Резервные копии и снимки", "Bad Gateway (code: 502) ": "Плохой шлюз (код: 502)", "Bahamas": "Багамские острова", "Bahrain": "Б<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BandWidth Limit Egress": "Ограничение исходящей пропускной способности", "BandWidth Limit Ingress": "Ограничение входящей пропускной способности", "Bandwidth limit": "Лимит пропускной способности", "Bangladesh": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Barbados": "Б<PERSON>рб<PERSON>д<PERSON>с", "Bare Metal": "Физические серверы", "Bare Metal Enroll": "Регистрация физических узлов", "Bare Metal Node Detail": "Детали физического сервера", "Bare Metal Nodes": "Физические серверы", "BareMetal Parameters": "Параметры физических узлов", "Base Config": "Базовая конфигурация", "Base Info": "Базовая информация", "Basic Parameters": "Основные параметры", "Batch Allocate": "Пакетное выделение", "Before deleting the project, it is recommended to clean up the resources under the project.": "Перед удалением проекта рекомендуется очистить ресурсы, привязанные к проекту.", "Belarus": "Беларусь", "Belgium": "Бельгия", "Belize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Benin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Bermuda": "Бермудские острова", "Bhutan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Big Data": "Большие Данные", "Bind Device": "Привязать устройство", "Bind Device Type": "Привязать тип устройства", "Bind Resource": "Привязать ресурс", "Bind Resource Name": "Имя привязанного ресурса", "Binding": "Привязка", "Binding Groups": "Группы привязки", "Binding Instance": "Привязка инстанса", "Binding Profile": "Профиль привязки", "Binding Users": "Привязанные пользователи", "Blank Volume": "Пустой Диск", "Block Device Mapping": "Отображение блочных устройств", "Block Migrate": "Блочная миграция", "Block Storage Services": "Службы блочного хранилища", "Blocked": "Заблокировано", "Bolivia": "Боливия", "Boot Device": "Загрузочное устройство", "Boot From Volume": "", "Boot Interface": "Загрузочный интерфейс", "Boot Mode of BIOS": "Режим загрузки BIOS", "Bootable": "Загрузочный", "Bootable Volume": "Загрузочный Диск", "Bosnia and Herzegovina": "Босния и Герцеговина", "Both of Frontend and Backend": "И передний и задний", "Botswana": "Ботсвана", "Brazil": "Бразилия", "British Indian Ocean Territory": "Британская территория в Индийском океане", "Brunei Darussalam": "<PERSON>ру<PERSON><PERSON><PERSON>-Да<PERSON>у<PERSON>салам", "Build": "Сборка", "Building": "Строится", "Bulgaria": "Болгария", "Burkina Faso": "Буркина-Фасо", "Burst limit": "Лимит всплеска", "Burundi": "Бурунди", "By default, for security reasons, application credentials are forbidden from being used for creating or destructing additional application credentials or keystone trusts. If your application credential needs to be able to perform these actions, check unrestricted.": "По умолчанию по соображениям безопасности учетные данные приложения запрещено использовать для создания или уничтожения дополнительных учетных данных приложения или ключей доверия. Если ваши учетные данные приложения должны иметь возможность выполнять эти действия, установите флажок «Без ограничений».", "CA Certificate": "Сертификат ЦС", "CA Certificates": "Сертификаты ЦС", "CHECK COMPLETE": "ПРОВЕРКА ЗАВЕРШЕНА", "CIDR": "CIDR", "CIDR Format Error(e.g. ***********/24, 2001:DB8::/48)": "Ошибка формата CIDR (например, ***********/24, 2001:DB8::/48)", "CIFS": "CIFS", "CMD": "CMD", "COE": "COE", "COE Version": "Версия COE", "CPU": "CPU", "CPU %": "CPU %", "CPU (Core)": "CPU (ядро)", "CPU Arch": "Архитектура CPU", "CPU Cores": "Ядра CPU", "CPU Policy": "Политика CPU", "CPU Thread Policy": "Политика потоков CPU", "CPU Usage(%)": "Использование CPU(%)", "CPU Usages (Core)": "Использование CPU (Ядер)", "CPU value is { cpu }, NUMA CPU value is { totalCpu }, need to be equal. ": "Значение CPU равно { cpu }, значение CPU NUMA равно { totalCpu }, должно быть равным.", "CPU(Core)": "CPU(ядро)", "CREATE COMPLETE": "СОЗДАНИЕ ЗАВЕРШЕНО", "CREATE FAILED": "СОЗДАНИЕ НЕ УДАЛОСЬ", "CREATE IN PROGRESS": "СОЗДАНИЕ В ПРОЦЕССЕ", "Cache Service": "Служба кэширования", "Cameroon": "Камер<PERSON>н", "Can add { number } {name}": "Можно добавить { number } {name}", "Canada": "Канада", "Cancel": "Отмена", "Cancel Download": "Отменить загрузку", "Cancel Select": "Отменить выбор", "Cancel Transfer": "Отменить передачу", "Cancel download successfully.": "Отмена загрузки прошла успешно.", "Cancel upload successfully.": "Отмена загрузки прошла успешно.", "Canonical Name Record": "Запись CNAME", "Capacity & Type": "", "Capacity (GiB)": "Емкость (ГиБ)", "Cape Verde": "Кабо-Верде", "Capsule Detail": "Подробности о капсуле", "Capsule Type": "Тип капсулы", "Capsules": "Капсулы", "Cascading deletion": "Каскадное удаление", "Cast Rules To Read Only": "Привести правила к режиму только чтения", "Category": "Категория", "Cayman Islands": "Каймановы острова", "CentOS": "CentOS", "Central African Republic": "Центрально-Африканская Республика", "CephFS": "CephFS", "Cephx": "Cephx", "Cert": "Сертификат", "Certificate Authority Authorization Record": "Запись об авторизации удостоверяющего центра", "Certificate Content": "Содержание сертификата", "Certificate Detail": "Подробности о сертификате", "Certificate Name": "Имя сертификата", "Certificate Type": "Тип сертификата", "Certificates": "Сертификаты", "Chad": "Чад", "Change Password": "Изменить пароль", "Change Type": "Изменить тип", "Change password": "Изменить пароль", "Change type": "Изменить тип", "Changed Node Count": "Количество измененных узлов", "Channel": "<PERSON><PERSON><PERSON><PERSON>", "Chassis ID": "Идентификатор шасси", "Check Can Live Migrate Destination": "Проверить возможность живой миграции на целевой узел", "Check Can Live Migrate Source": "Проверить возможность живой миграции на исходный узел", "Check Complete": "Проверка завершена", "Check Failed": "Ошибка проверки", "Check In Progress": "Выполняется проверка", "Checksum": "Контрольная сумма", "Chile": "Чили", "China": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Choose a Network Driver": "Выберите драйвер сети", "Choose a host to live migrate instance to. If not selected, the scheduler will auto select target host.": "Выберите хост для живой миграции инстанса. Если не выбрано, планировщик автоматически выберет целевой хост.", "Choose a host to migrate instance to. If not selected, the scheduler will auto select target host.": "Выберите хост для миграции инстанса. Если не выбрано, планировщик автоматически выберет целевой хост.", "Choosing a QoS policy can limit bandwidth and DSCP": "Выбор политики QoS может ограничить пропускную способность и DSCP", "Christmas Island": "Остров Рождества", "Cidr": "CIDR", "Cinder Service": "Служба Cinder", "Cipher": "<PERSON><PERSON><PERSON><PERSON>", "Clean Failed": "Ошибка очистки", "Clean Wait": "Ожидание очистки", "Cleaning": "Очистка", "Clear Gateway": "Очистить шлюз", "Clear selected": "Очистить выбранное", "Click To View": "Нажмите для просмотра", "Click here for filters.": "Нажмите здесь для фильтров.", "Click to Upload": "Нажмите для загрузки", "Click to show detail": "Нажмите для просмотра подробностей", "Clone Volume": "Клонировать Диск", "Clone volume": "Клонировать диск", "Close": "Закрыть", "Close External Gateway": "Закрыть внешний шлюз", "Close all notifications.": "Закрыть все уведомления.", "Close external gateway": "Закрыть внешний шлюз", "Cloud": "Облако", "Cloud Container Engine": "Облачный контейнерный движок", "Cluster Detail": "Подробности о кластере", "Cluster Distro": "Распределение кластера", "Cluster Info": "Информация о кластере", "Cluster Management": "Управление кластером", "Cluster Name": "Имя кластера", "Cluster Network": "Сеть кластера", "Cluster Template": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> кластера", "Cluster Template Detail": "Подробности о шаблоне кластера", "Cluster Template Name": "Имя шаблона кластера", "Cluster Templates": "Шаблоны кластера", "Cluster Type": "<PERSON>и<PERSON> кластера", "Clusters": "Кластеры", "Clusters Management": "Управление кластерами", "Cocos (Keeling) Islands": "Кокосовые (Килинг) острова", "Code": "<PERSON>од", "Cold Migrate": "Холодная миграция", "Colombia": "Колумбия", "Command": "Команда", "Command to run to check health": "Команда для проверки состояния", "Command was successfully executed at container {name}.": "Команда успешно выполнена в контейнере {name}.", "Commas ‘,’ are not allowed to be in a tag name in order to simplify requests that specify lists of tags": "Запятые ‘,’ не допускаются в имени тега для упрощения запросов, указывающих списки тегов", "Commit Latency(ms)": "Задержка фиксации (мс)", "Common Server": "Об<PERSON>ий сервер", "Comoros": "Коморские острова", "Compute": "Вычисления", "Compute Hosts": "Хосты вычислений", "Compute Live Migration": "Живая миграция вычислений", "Compute Live Resize Instance": "Изменение размера инстанса в режиме реального времени", "Compute Node status": "Состояние узла вычислений", "Compute Optimized": "Оптимизированный для вычислений", "Compute Optimized Info": "Информация об оптимизации вычислений", "Compute Optimized Type": "Тип оптимизированный для вычислений", "Compute Optimized Type with GPU": "Тип оптимизированный для вычислений с GPU", "Compute Pause Instance": "Приостановить инстанс вычислений", "Compute Reboot Instance": "Перезагрузить инстанс вычислений", "Compute Resume Instance": "Возобновить инстанс вычислений", "Compute Service": "Вычислительная служба", "Compute Services": "Вычислительные службы", "Compute Start Instance": "Запустить инстанс вычислений", "Compute Stop Instance": "Остановить инстанс вычислений", "Compute Suspend Instance": "Приостановить инстанс вычислений", "Compute Unpause Instance": "Возобновить инстанс вычислений", "Conductor Live Migrate Instance": "Живая миграция инстанса с использованием Conductor", "Conductor Live Resize Instance": "Изменение размера инстанса в режиме реального времени с использованием Conductor", "Conductor Migrate Server": "Миграция сервера с использованием Conductor", "Config Overview": "Обзор конфигурации", "Configuration": "Конфигурация", "Configuration Detail": "Подробности конфигурации", "Configuration Group": "Группа конфигурации", "Configuration Group ID/Name": "ID/Имя группы конфигурации", "Configuration Groups": "Группы конфигурации", "Configuration Update": "Обновление конфигурации", "Configured Disk (GiB)": "Настроенный диск (ГБ)", "Configured Memory (GiB)": "Настроенная память (ГБ)", "Confirm": "Подтвердить", "Confirm Config": "Подтвердить конфигурацию", "Confirm Password": "Подтвердите пароль", "Confirm Resize or Migrate": "Подтвердить изменение размера или миграцию", "Confirm Shared Key": "Подтвердите общий ключ", "Confirming Resize or Migrate": "Подтверждение изменения размера или миграции", "Connect Subnet": "Подключить подсеть", "Connect router": "Подключить маршрутизатор", "Connected Threads": "Подключенные потоки", "Connection Examples": "Примеры подключения", "Connection Information": "Информация о подключении", "Connection Limit": "Лимит подключений", "Consecutive failures needed to report unhealthy": "Количество последовательных сбоев для отчета о ненормальном состоянии", "Console": "консоли", "Console Interface": "Интерфейс консоли", "Console Log": "<PERSON><PERSON><PERSON><PERSON><PERSON> консоли", "Consumer": "Потребитель", "Container": "Кон<PERSON>ейнер", "Container Creating": "Создание контейнера", "Container Deleting": "Удаление контейнера", "Container Detail": "Подробности о контейнере", "Container Format": "Формат контейнера", "Container Killing": "Завершение работы контейнера", "Container Name": "Имя контейнера", "Container Pausing": "Приостановка контейнера", "Container Rebooting": "Перезагрузка контейнера", "Container Rebuilding": "Пересборка контейнера", "Container Restarting": "Перезапуск контейнера", "Container Starting": "Запуск контейнера", "Container Status": "Статус контейнера", "Container Stopping": "Остановка контейнера", "Container Unpausing": "Возобновление работы контейнера", "Container Version": "Версия контейнера", "Containers": "Контейнеры", "Containers CPU": "Процессор контейнеров", "Containers Disk (GiB)": "<PERSON><PERSON>с<PERSON> конте<PERSON><PERSON><PERSON><PERSON><PERSON> (ГБ)", "Containers Info": "Информация о контейнерах", "Containers Management": "Управление контейнерами", "Containers Memory (MiB)": "Память конте<PERSON><PERSON><PERSON><PERSON><PERSON> (МиБ)", "Content": "Содержимое", "Content Type": "Тип содержимого", "Continue": "Продолжать, продолжить", "Continue Upload": "Продолжить загрузку", "Control Attribute": "", "Control Attributes": "", "Control Location": "Местоположение управления", "Cook Islands": "Острова Кука", "Copy": "Копировать", "Copy File": "Копировать файл", "CoreOS": "CoreOS", "Costa Rica": "Коста-Рика", "Cote D'Ivoire": "Кот-д'Иву<PERSON>р", "Count": "Количество", "Crashed": "Сбой", "Create": "Создать", "Create Allowed Address Pair": "Создать разрешенную пару адресов", "Create Application Credentials": "Создать учетные данные приложения", "Create Backup": "Создать резервную копию", "Create Bandwidth Limit Rule": "Создать правило ограничения пропускной способности", "Create Bare Metal Node": "Создать выделенный физический узел", "Create Capsule": "Создать капсулу", "Create Certificate": "Создать сертификат", "Create Cluster": "Создать кластер", "Create Cluster Template": "Создать шаблон кластера", "Create Complete": "", "Create Configurations": "", "Create Container": "", "Create DSCP Marking Rule": "", "Create Database": "", "Create Database Backup": "", "Create Database Instance": "", "Create Default Pool": "", "Create Domain": "", "Create Encryption": "", "Create Extra Spec": "", "Create Failed": "", "Create Firewall": "", "Create Firewall Policy": "", "Create Flavor": "", "Create Folder": "", "Create Host Aggregate": "", "Create IPsec Site Connection": "", "Create Image": "", "Create In Progress": "Создание в процессе", "Create Instance": "Создать инстанс", "Create Instance Snapshot": "Создать снимок инстанса", "Create Ironic Instance": "Создать инстанс Ironic", "Create Keypair": "Создать ключевую пару", "Create Listener": "Создать слушателя", "Create Loadbalancer": "Создать балансировщик нагрузки", "Create Network": "Создать сеть", "Create New Network": "Создать новую сеть", "Create Node": "Создать узел", "Create Policy": "", "Create Port": "Создать порт", "Create Port Forwarding": "Создать переадресацию портов", "Create Port Group": "Создать группу портов", "Create Project": "Создать проект", "Create QoS Policy": "Создать политику QoS", "Create QoS Spec": "Создать спецификацию QoS", "Create RBAC Policy": "", "Create Record Set": "Создать набор записей", "Create Role": "Создать роль", "Create Router": "Создать маршрутизатор", "Create Rule": "Создать правило", "Create Security Group": "Создать группу безопасности", "Create Segment": "", "Create Server Group": "Создать группу серверов", "Create Share": "Создать ресурс", "Create Share Group": "Создать группу ресурсов", "Create Share Group Type": "Создать тип группы ресурсов", "Create Share Metadata": "Создать метаданные ресурса", "Create Share Network": "Создать сеть ресурса", "Create Share Type": "Создать тип ресурса", "Create Snapshot": "Создать снимок", "Create Stack": "Создать стек", "Create Static Route": "Создать статический маршрут", "Create Subnet": "Создать подсеть", "Create Time": "Время создания", "Create Transfer": "Создать передачу", "Create Type": "Создать тип", "Create User": "Создать пользователя", "Create User Group": "Создать группу пользователей", "Create VPN": "Создать VPN", "Create VPN Endpoint Group": "Создать группу конечных точек VPN", "Create VPN IKE Policy": "Создать политику VPN IKE", "Create VPN IPsec Policy": "Создать политику VPN IPsec", "Create Virtual Adapter": "Создать виртуальный адаптер", "Create Volume": "Создать диск", "Create Volume Backup": "Создать резервную копию диска", "Create Volume Snapshot": "Создать снимок диска", "Create Volume Type": "Создать тип диск", "Create Zone": "Создать зону", "Create a full backup, the system will automatically create a new backup chain, the full backup name is the backup chain name; Create an incremental backup, the system will automatically create an incremental backup under the newly created backup chain.": "Создать полную резервную копию, система автоматически создаст новую цепочку резервных копий, имя полной резервной копии будет именем цепочки резервных копий; Создать инкрементную резервную копию, система автоматически создаст инкрементную резервную копию вновь созданной цепочки резервных копий.", "Create firewall": "", "Create host aggregate": "Создать хост-агрегат", "Create image": "Создать образ", "Create instance": "Создать инстанс", "Create ironic instance": "Создать инстанс Ironic", "Create new AZ": "Создать новую доступность зоны", "Create rule": "Создать правило", "Create security group": "Создать группу безопасности", "Create server group": "Создать группу серверов", "Create static route": "Создать статический маршрут", "Create volume": "Создать диск", "Create volume backup": "Создать резервную копию диска", "Created": "Создано", "Created At": "Создано", "Created Time": "Время создания", "Created Volumes": "Созданные диска", "Creating": "Создание", "Creating From Snapshot": "Создание из снимка", "Creation Timeout (Minutes)": "Тайм-аут создания (минуты)", "Credential Type": "Тип учетных данных", "Croatia (local name: Hrvatska)": "Хорватия (местное название: Hrvatska)", "Cuba": "Куба", "Current Availability Zones": "", "Current Compute Host": "Текущий хост вычислений", "Current Connections": "Текущие соединения", "Current Disk (GiB)": "Текущий диск (ГиБ)", "Current Flavor": "Текущий тип", "Current Host": "Текущий хост", "Current Interface": "Текущий интерфейс", "Current Master Node Count": "Текущее количество мастер-узлов", "Current Node Count": "Текущее количество узлов", "Current Password": "Текущий пароль", "Current Path: ": "Текущий путь: ", "Current Project": "Текущий проект", "Current Project Images": "Образы текущего проекта", "Current Project Networks": "Сети текущего проекта", "Current Project QoS Policies": "Политики QoS текущего проекта", "Current QoS policy name": "Имя текущей политики QoS", "Current Rules": "", "Current Status": "Текущий статус", "Current Storage Backend": "Текущий бэкенд хранилища", "Current data downloaded.": "Текущие загруженные данные.", "Custom": "Пользовательский", "Custom Headers": "", "Custom ICMP Rule": "Пользовательское правило ICMP", "Custom Metadata": "Пользовательские метаданные", "Custom Properties Info": "Информация о пользовательских свойствах", "Custom TCP Rule": "Пользовательское правило TCP", "Custom Trait": "Пользовательская черта", "Custom UDP Rule": "Пользовательское правило UDP", "Cut": "Вырезать", "Cut File": "Вырезать файл", "Cyprus": "<PERSON><PERSON><PERSON><PERSON>", "Czech Republic": "Чешская Республика", "DC/OS": "DC/OS", "DCCP": "DCCP", "DEGRADED: One or more of the entity’s components are in ERROR": "DEGRADED: Од<PERSON><PERSON> или несколько компонентов сущности находятся в состоянии ошибки (ERROR)", "DELETE COMPLETE": "УДАЛЕНИЕ ЗАВЕРШЕНО", "DELETE FAILED": "УДАЛЕНИЕ НЕ УДАЛОСЬ", "DELETE_IN PROGRESS": "УДАЛЕНИЕ В ПРОЦЕССЕ", "DENY": "", "DHCP": "DHCP", "DHCP Agent": "Агент DHCP", "DHCP Agents": "Агенты DHCP", "DISK IOPS": "Дисковые операции ввода-вывода в секунду (IOPS)", "DISK Usage(%)": "Использование диска(%)", "DNS": "DNS", "DNS Assignment": "Назначение DNS", "DNS Name": "DNS-имя", "DNS Nameservers": "DNS-серверы", "DNS Reverse": "Обратный DNS", "DNS Zones": "Зоны DNS", "DNS Zones Detail": "Подробности зон DNS", "DPD Action": "Действие DPD", "DPD Interval (sec)": "Интервал DPD (сек)", "DPD actions controls the use of Dead Peer Detection Protocol.": "Действия DPD контролируют использование протокола Dead Peer Detection Protocol (DPD).", "DPD timeout (sec)": "Таймаут DPD (сек)", "DRAINING: The member is not accepting new connections": "ОБСЛУЖИВАНИЕ: Элемент не принимает новые соединения", "DSCP Marking": "Маркировка DSCP", "Danger": "Опасность", "Data Disk": "Дисковые данные", "Data Disks": "Дисковые данные", "Data Protection": "Защита данных", "Data Source Type": "Тип источника данных", "Database": "База данных", "Database Backup Detail": "Подробности резервной копии базы данных", "Database Disk (GiB)": "Диск базы данных (ГиБ)", "Database Flavor": "Тип базы данных", "Database Instance": "Инстанс базы данных", "Database Instance Detail": "Подробности инстанса базы данных", "Database Instance Name": "Имя инстанса базы данных", "Database Instance Status": "Статус инстанса базы данных", "Database Instances": "Инстансы баз данных", "Database Name": "Имя базы данных", "Database Port": "Порт базы данных", "Database Service": "Служба базы данных", "Databases": "Базы данных", "Datastore": "Хранилище данных", "Datastore Type": "Тип хранилища данных", "Datastore Version": "Версия хранилища данных", "Deactivated": "Деактивировано", "Debian": "Debian", "Dedicated": "Выделенный", "Default Policy": "Политика по умолчанию", "Default Project": "", "Default Project ID/Name": "", "Default is slaac, for details, see https://docs.openstack.org/neutron/latest/admin/config-ipv6.html": "По умолчан<PERSON><PERSON> - <PERSON><PERSON><PERSON>, подробности см. по адресу https://docs.openstack.org/neutron/latest/admin/config-ipv6.html", "Defaults": "По умолчанию", "Defines the admin state of the health monitor.": "Определяет административное состояние монитора состояния.", "Defines the admin state of the listener.": "Определяет административное состояние listener.", "Defines the admin state of the pool.": "Определяет административное состояние пула.", "Defines the admin state of the port.": "Определяет административное состояние порта.", "Degraded": "В состоянии деградации", "Delay Interval(s)": "Интервал задержки (сек)", "Delete": "Удалить", "Delete Allowed Address Pair": "Удалить разрешенную пару адресов", "Delete Application Credential": "Удалить учетные данные приложения", "Delete Bandwidth Egress Rules": "Удалить правила исходящей полосы пропускания", "Delete Bandwidth Ingress Rules": "Удалить правила входящей полосы пропускания", "Delete Capsule": "Удалить капсулу", "Delete Certificate": "Удалить сертификат", "Delete Cluster": "Удалить кластер", "Delete Cluster Template": "Удалить шаблон кластера", "Delete Complete": "Удаление завершено", "Delete Configuration": "Удалить тип инстанса", "Delete Container": "Удалить контейнер", "Delete DSCP Marking Rules": "Удалить правила маркировки DSCP", "Delete Database": "Удалить базу данных", "Delete Database Backup": "Удалить резервную копию базы данных", "Delete Default Pool": "Удалить пул по умолчанию", "Delete Domain": "Удалить домен", "Delete Encryption": "Удалить шифрование", "Delete Extra Specs": "Удалить дополнительные характеристики", "Delete Failed": "Удаление не удалось", "Delete File": "Удалить файл", "Delete Firewall": "", "Delete Flavor": "Удалить тип", "Delete Folder": "Удалить папку", "Delete Group": "Удалить группу", "Delete Host Aggregate": "Удалить хост-агрегат", "Delete IPsec Site Connection": "Удалить IPsec-соединение", "Delete Image": "Удалить образ", "Delete In Progress": "Удаление в процессе", "Delete Instance": "Удалить инстанс", "Delete Instance Snapshot": "Удалить снимок инстанса", "Delete Keypair": "Удалить пару ключей", "Delete Listener": "Удалить прослушиватель", "Delete Load Balancer": "Удалить балансировщик нагрузки", "Delete Member": "Удалить участника", "Delete Metadata": "Удалить метаданные", "Delete Network": "Удалить сеть", "Delete Node": "Удалить узел", "Delete Policy": "", "Delete Port": "Удалить порт", "Delete Port Forwarding": "Удалить перенаправление порта", "Delete Port Group": "Удалить группу портов", "Delete Project": "Удалить проект", "Delete QoS Policy": "Удалить политику QoS", "Delete QoS Spec": "Удалить спецификацию QoS", "Delete RBAC Policy": "", "Delete Record Set": "Удалить набор записей", "Delete Role": "Удалить роль", "Delete Router": "Удалить маршрутизатор", "Delete Rule": "Удалить правило", "Delete Security Group": "Удалить группу безопасности", "Delete Server Group": "Удалить группу серверов", "Delete Share": "Удалить общий ресурс", "Delete Share Access Rule": "Удалить правило доступа к общему ресурсу", "Delete Share Group": "Удалить группу общих ресурсов", "Delete Share Group Type": "Удалить тип группы общих ресурсов", "Delete Share Metadata": "Удалить метаданные общего ресурса", "Delete Share Network": "Удалить сеть общего ресурса", "Delete Share Server": "Удалить сервер общего ресурса", "Delete Share Type": "Удалить тип общего ресурса", "Delete Static Route": "Удалить статический маршрут", "Delete Subnet": "Удалить подсеть", "Delete User": "Удалить пользователя", "Delete VPN": "Удалить VPN", "Delete VPN EndPoint Groups": "Удалить группы конечных точек VPN", "Delete VPN IKE Policy": "Удалить политику VPN IKE", "Delete VPN IPsec Policy": "Удалить политику VPN IPsec", "Delete Virtual Adapter": "Удалить виртуальный адаптер", "Delete Volume": "Удалить диск", "Delete Volume Backup": "Удалить резервную копию диска", "Delete Volume Snapshot": "Удалить снимок диска", "Delete Volume Type": "Удалить тип диска", "Delete Volume on Instance Delete": "", "Delete Zone": "Удалить зону", "Delete metadata": "Удалить метаданные", "Deleted": "Удалено", "Deleted At": "Удалено в", "Deleted with the instance": "Удалено вместе с инстансом", "Deleting": "Удаление", "Deleting this stack will delete all resources deployed by the stack.": "Удаление этого стека приведет к удалению всех ресурсов, развернутых стеком.", "Democratic Republic of the Congo": "Демократическая Республика Конго", "Denmark": "Дания", "Denying": "Отказано", "Deploy Failed": "Не удалось развернуть", "Deploy Wait": "Ожидание развёртывания", "Deploying": "Развёртывание", "Deployment Parameters": "Параметры развёртывания", "Description": "Описание", "Dest Folder": "Папка назначения", "Destination": "Место назначения", "Destination CIDR": "CIDR-адрес места назначения", "Destination IP": "", "Destination IP Address/Subnet": "", "Destination Port": "", "Destination Port/Port Range": "Порт/диапазон портов места назначения", "Detach": "Отсоединить", "Detach Instance": "Отсоединить инстанс", "Detach Interface": "Отсоединить интерфейс", "Detach Network": "Отсоединить сеть", "Detach Security Group": "Отсоединить группу безопасности", "Detach Volume": "Отсоединить диск", "Detach interface": "Отсоединить интерфейс", "Detaching": "Отсоединение", "Detail": "Подробность", "Detail Info": "Детальная Информация", "Details": "Подробности", "Details *": "Подробности *", "Details about the PTR record.": "Подробности о записи PTR.", "Device": "", "Device ID": "Идентификатор устройства", "Device ID/Name": "", "Device Owner": "Владелец устройства", "Devicemapper": "<PERSON><PERSON>mapper", "Direct": "Прям<PERSON>й", "Direction": "Направление", "Disable": "Отключить", "Disable Cinder Service": "Отключить службу Cinder", "Disable Compute Host": "Отключить узел вычислений", "Disable Compute Service": "Отключить службу вычислений", "Disable Gateway": "Отключить шлюз", "Disable Neutron Agent": "Отключить агента Neutron", "Disable SNAT": "Отключить SNAT", "Disable TLS": "Отключить TLS", "Disable compute host": "Отключить узел вычислений", "Disabled": "Отключено", "Disabling port security will turn off the security group policy protection and anti-spoofing protection on the port. General applicable scenarios: NFV or operation and maintenance Debug.": "Отключение безопасности порта отключит защиту политики группы безопасности и защиту anti-spoofing на порту. Общие сценарии применения: NFV или отладка операций и отладка.", "Disabling the project will have a negative impact. If the users associated with the project are only assigned to the project, they will not be able to log in": "Отключение проекта повлияет негативно. Если пользователи, связанные с проектом, состоят только в этом проекте, они не смогут войти", "Disassociate": "Отсоединить", "Disassociate Floating IP": "Отсоединить плавающий IP", "Disassociate Floating Ip": "Отсоединить плавающий IP", "Disconnect Subnet": "Отключить подсеть", "Discovery URL": "URL обнаружения", "Disk": "Диск", "Disk (GiB)": "<PERSON><PERSON><PERSON><PERSON> (ГиБ)", "Disk Format": "Формат диска", "Disk Info": "Информация о диске", "Disk Tag": "Метка диска", "Disk allocation (GiB)": "Распределение диска (ГиБ)", "Disk size is limited by the min disk of flavor, image, etc.": "Размер диска ограничен минимальным значением диска у виртуального сервера, образа и т. д.", "Djibouti": "Дж<PERSON><PERSON>ути", "Do Build And Run Instance": "Создать и запустить инстанс", "Do HH:mm": "Сделать HH:mm", "Do not reset the normally mounted volume to the \"available\"、\"maintenance\" or \"error\" status. The reset state does not remove the volume from the instance. If you need to remove the volume from the instance, please go to the console of the corresponding project and use the \"detach\" operation.": "Не сбрасывайте обычно примонтированный диск в состояние \"доступен\", \"обслуживание\" или \"ошибка\". Сброшенное состояние не удаляет диск из инстанса. Если вам нужно удалить диск из инстанса, перейдите в консоль соответствующего проекта и используйте операцию \"отсоединить\".", "Do not set with a backend": "Не устанавливать с бэкендом", "Docker": "<PERSON>er", "Docker Hub": "<PERSON><PERSON>", "Docker Storage Driver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> хране<PERSON><PERSON><PERSON> <PERSON>er", "Docker Swarm": "Docker Swarm", "Docker Swarm Mode": "<PERSON><PERSON><PERSON><PERSON>м Docker Swarm", "Docker Volume Size (GiB)": "Размер ди<PERSON><PERSON><PERSON> (ГиБ)", "Domain": "До<PERSON><PERSON>н", "Domain Detail": "Подробности домена", "Domain ID": "", "Domain ID/Name": "Идентификатор/имя домена", "Domain Manager": "Менеджер домена", "Domain Name": "Имя домена", "Domain name ending in.": "", "Domains": "Домены", "Dominica": "Доминика", "Down": "Выключено", "Download": "Загружать", "Download File": "Загрузить файл", "Download Image": "Скачать изображение", "Download all data": "Загрузить все данные", "Download canceled!": "Загрузка отменена!", "Download current data": "Загрузить текущие данные", "Download progress": "Прогресс загрузки", "Downloading": "Загрузка", "Draining": "Обслуживание", "Driver": "Др<PERSON><PERSON><PERSON><PERSON><PERSON>", "Driver Handles Share Servers": "Драйвер обрабатывает серверы обмена", "Driver Info": "Информация о драйвере", "Driver Interface": "Интерфейс драйвера", "Duplicate tag name: {tag}": "Дублирование имени метки: {tag}", "EGP": "EGP", "ENTRYPOINT": "Точка входа", "ESP": "ESP", "Each instance belongs to at least one security group, which needs to be specified when it is created. Instances in the same security group can communicate with each other on the network, and instances in different security groups are disconnected from the internal network by default.": "Каждый инстанс принадлежит как минимум к одной группе безопасности, которую необходимо указать при создании. инстансы в одной и той же группе безопасности могут общаться друг с другом в сети, а инстансы в разных группах безопасности отключены от внутренней сети по умолчанию.", "Each new connection request is assigned to the next server in order, and all requests are finally divided equally among all servers. Commonly used for short connection services, such as HTTP services.": "Каждый новый запрос на соединение назначается следующему серверу по порядку, и все запросы в конечном итоге равномерно распределяются между всеми серверами. Обычно используется для короткосрочных соединений, таких как HTTP-сервисы.", "Each server can have up to 50 tags": "Каждый сервер может иметь до 50 меток", "East Timor": "Восточный Тимор", "Ecuador": "Эквадор", "Edit": "Редактировать", "Edit Bandwidth Egress Limit Rule": "Изменить правило ограничения исходящей полосы пропускания", "Edit Bandwidth Ingress Limit Rule": "Изменить правило ограничения входящей полосы пропускания", "Edit Bare Metal Node": "Изменить физический узел", "Edit Consumer": "Изменить потребителя", "Edit Container": "Изменить контейнер", "Edit DSCP Marking Rule": "Изменить правило маркировки DSCP", "Edit Default Pool": "Изменить пул ресурсов по умолчанию", "Edit Domain": "Изменить домен", "Edit Domain Permission": "Изменить правила доступа домена", "Edit Extra Spec": "Изменить дополнительную спецификацию", "Edit Flavor": "Изменить тип инстанса", "Edit Health Monitor": "Изменить мониторинг состояния", "Edit Host Aggregate": "Изменить агрегацию хостов", "Edit IPsec Site Connection": "Изменить соединение сайта IPsec", "Edit Image": "Изменить образ", "Edit Instance": "Изменить инстанс", "Edit Instance Snapshot": "Изменить снимок инстанса", "Edit Listener": "Изменить слушателя", "Edit Load Balancer": "Изменить балансировщик нагрузки", "Edit Member": "Изменить участника", "Edit Metadata": "Изменить метаданные", "Edit Port": "Изменить порт", "Edit Port Forwarding": "Изменить переадресацию портов", "Edit Port Group": "Изменить группу портов", "Edit Project": "Изменить проект", "Edit QoS Policy": "Изменить политику QoS", "Edit Quota": "Изменить квоту", "Edit Role": "Изменить роль", "Edit Router": "Изменить маршрутизатор", "Edit Rule": "Изменить правило", "Edit Share Metadata": "Изменить метаданные общего доступа", "Edit Subnet": "Изменить подсеть", "Edit System Permission": "Изменить разрешение системы", "Edit User": "Изменить пользователя", "Edit User Group": "Изменить группу пользователей", "Edit VPN": "Изменить VPN", "Edit VPN EndPoint Groups": "Изменить группы конечных точек VPN", "Edit VPN IKE Policy": "Изменить политику VPN IKE", "Edit VPN IPsec Policy": "Изменить политику VPN IPsec", "Edit Volume Backup": "Изменить резервную копию диска", "Edit host aggregate": "Изменить группу хостов", "Edit metadata": "Изменить метаданные", "Edit quota": "Изменить квоты", "Edit rule": "", "Editing only changes the content of the file, not the file name.": "Редактирование изменяет только содержимое файла, не его имя.", "Effective Mode": "Эффективный режим", "Effective mode after configuration changes": "Эффективный режим после изменения типа инстанса", "Egress": "Исходящий", "Egress Policy": "", "Egress Policy ID": "", "Egress Policy Name": "", "Egypt": "Египет", "Eject": "Извлечь", "El Salvador": "Сальвадор", "Email": "Электронная почта", "Email Address": "Адрес электронной почты", "Email for the zone. Used in SOA records for the zone.": "", "Enable": "Включить", "Enable Admin State": "Включить состояние администратора", "Enable Compute Host": "Включить хост вычислений", "Enable Compute Service": "Включить службу вычислений", "Enable DHCP": "Включить DHCP", "Enable Domain": "Включить домен", "Enable Floating IP": "Включить плавающий IP", "Enable Health Check": "Включить проверку состояния", "Enable Health Monitor": "Включить Health Monitor", "Enable Load Balancer": "Включить балансировщик нагрузки", "Enable Neutron Agent": "Включить агент Neutron", "Enable Project": "Включить проект", "Enable QoS Policy": "Включить политику QoS", "Enable Registry": "Включить реестр", "Enable SNAT": "Включить SNAT", "Enable Service": "Включить сервис", "Enable User": "Включить пользователя", "Enable auto heal": "Включить автоматическое восстановление", "Enable auto remove": "Включить автоматическое удаление", "Enable compute host": "Включить хост вычислений", "Enable interactive mode": "Включить интерактивный режим", "Enabled": "Включено", "Enabled Load Balancer for Master Nodes": "Включить балансировщик нагрузки для управляющих узлов", "Enabled Network": "Включена сеть", "Encapsulation Mode": "Режим инкапсуляции", "Encrypted": "Зашифровано", "Encryption": "Шифрование", "Encryption Algorithm": "Алгоритм шифрования", "Encryption Info": "Информация о шифровании", "End Time": "Время завершения", "Endpoint Counts": "Количество конечных точек", "Endpoints": "Конечные точки", "Engine ID": "Engine ID", "Enroll": "Запись", "Enter Maintenance Mode": "Войти в режим обслуживания", "Enter an integer value between 1 and 65535.": "Введите целое значение между 1 и 65535.", "Enter query conditions to filter": "Введите условия запроса для фильтрации", "Entered: {length, plural, =1 {one character} other {# characters} }(maximum {maxCount} characters)": "Введено: {length, plural, =1 {один символ} other {# символов} }(максимум {maxCount} символов)", "Environment": "Окружение", "Environment Variable": "Переменная окружения", "Environment Variables": "Переменные окружения", "Ephemeral Disk (GiB)": "Эфемерный диск (ГБ)", "Equatorial Guinea": "Экваториальная Гвинея", "Eritrea": "Эритрея", "Error": "Ошибка", "Error Deleting": "Ошибка при удалении", "Error Extending": "Ошибка при расширении", "Error Restoring": "Ошибка при восстановлении", "Estonia": "Эстония", "Ether Type": "<PERSON><PERSON><PERSON>", "Ethiopia": "Эфиопия", "Event": "", "Event Time": "Время события", "Evictions": "Вытеснения", "Execute Command": "Выполнить команду", "Execution Result": "Результат выполнения", "Existing Volume": "Существующий диск", "Exit Policy": "Политика выхода", "Exp: ": "", "Expand": "Ра<PERSON><PERSON><PERSON><PERSON>ить", "Expand Advanced Options": "Развернуть дополнительные параметры", "Expired Time": "Время истечения срока действия", "Expires At": "Истекает в", "Export Location": "Местоположение для экспорта", "Export Locations": "Местоположения для экспорта", "Exposed Ports": "Открытые порты", "Extend Root Volume": "Расширить корневой диск", "Extend Share": "Расширить ресурс общего доступа", "Extend Volume": "Расширить Диск", "Extend volume": "Расширить диск", "Extending": "Расширение", "Extending Error": "Ошибка расширения", "External": "Внешний", "External Fixed IP": "Внешний фиксированный IP", "External Fixed IPs": "Внешние фиксированные IP", "External Gateway": "Внешний шлюз", "External IP": "Внешний IP", "External IP(V4)": "Внешний IP (IPv4)", "External IP(V6)": "Внешний IP (IPv6)", "External Network": "Внешняя сеть", "External Network ID/Name": "Идентификатор/имя внешней сети", "External Network Info": "Информация о внешней сети", "External Networks": "Внешние сети", "External Port": "Внешний порт", "External Port/Port Range": "Внешний порт/диапазон портов", "Extra Infos": "Дополнительная информация", "Extra Specs": "Дополнительные спецификации", "FAKE": "FAKE", "FLAT": "FLAT", "Fail Rollback": "Откат с ошибкой", "Failed": "Не удалось", "Failover Segment": "", "Falkland Islands (Malvinas)": "Фолклендские острова (Мальвинские острова)", "Faroe Islands": "Фарерские острова", "Fault": "Неисправность", "Fedora": "<PERSON><PERSON>", "Fiji": "Fiji", "File": "File", "File System Used Space": "Используемое пространство файловой системы", "File URL": "URL файла", "Filename": "Имя файла", "Files: {names}": "Файлы: {names}", "Fill In The Parameters": "Заполните параметры", "Fingerprint": "Отпечаток", "Finish Resize": "Завершить изменение размера", "Finland": "Финляндия", "Firewall": "", "Firewall Detail": "", "Firewall Policies": "", "Firewall Policy": "", "Firewall Port": "", "Firewall Rule": "", "Firewall Rules": "", "Firewalls": "", "Fixed IP": "Фиксированный IP", "Fixed IP Address": "Фиксированный IP-адрес", "Fixed IPs": "Фиксированные IP-адреса", "Fixed Network": "Фиксированная сеть", "Fixed Subnet": "Фиксированная подсеть", "Flavor": "Тип инстанса", "Flavor Detail": "Подробности о типе инстанса", "Flavor Info": "Информация о типе инстанса", "Flavor Name": "Имя типа инстанса", "Flavor families, used to configure the instance flavor classification": "", "Flavor of Master Nodes": "Тип инстанса мастер-узлов", "Flavor of Nodes": "Тип инста<PERSON><PERSON><PERSON> узлов", "Flavors": "Типы инста<PERSON><PERSON>ов", "Floating IP": "Плавающий IP", "Floating IP Address": "Адрес плавающего IP", "Floating IP Enabled": "Плавающий IP включен", "Floating IPs": "Плавающие IP", "Floating Ip": "Плавающий IP", "Floating Ip Address": "Адрес плавающего IP", "Floating Ip Detail": "Подробности о плавающем IP", "Floating ip has already been associate, Please check Force release": "Плавающий IP уже ассоциирован. Пожалуйста, попробуйте принудительное освобождение", "Folder Detail": "Подробности о папке", "Folder Name": "Имя папки", "For GPU type, you need to install GPU drivers in the instance operating system.": "Для типа GPU вам необходимо установить драйверы GPU в операционной системе инстанса.", "For GRE networks, valid segmentation IDs are 1 to 4294967295": "Для сетей GRE допустимыми идентификаторами сегментации являются числа от 1 до 4294967295", "For VLAN networks, valid segmentation IDs are 1 to 4094": "Для сетей VLAN допустимыми идентификаторами сегментации являются числа от 1 до 4094", "For VXLAN networks, valid segmentation IDs are 1 to 16777215": "Для сетей VXLAN допустимыми идентификаторами сегментации являются числа от 1 до 16777215", "Forbidden": "Запрещено", "Forbidden Domain": "Запрещен домен", "Forbidden Project": "Запрещен проект", "Forbidden User": "Запрещен пользователь", "Forbidden the domain will have a negative impact, all project and user in domain will be forbidden": "Запрет домена повлечет негативные последствия, все проекты и пользователи в этом домене будут запрещены", "Force Delete": "Принудительное удаление", "Force Delete Container": "Принудительное удаление контейнера", "Force Delete Share Instance": "Принудительное удаление инстанса общей папки", "Force release": "Принудительное освобождение", "Force shutdown must be checked!": "", "Forced Down": "Принудительное отключение", "Forced Shutdown": "Принудительное выключение", "Forced shutdown may result in data loss or file system damage. You can also take the initiative to shut down and perform operations.": "Принудительное выключение может привести к потере данных или повреждению файловой системы. Вы также можете сами выключить систему и выполнить необходимые операции.", "Forgot your password?": "Забыли пароль?", "Format": "Формат", "Forward Slash ‘/’ is not allowed to be in a tag name": "Символ '/' (косая черта) не допускается в названии тега", "France": "Франция", "Free": "Свободно", "FreeBSD": "FreeBSD", "French Guiana": "Французская Гвиана", "French Polynesia": "Французская Полинезия", "Frequent login failure will cause the account to be temporarily locked, please operate after 5 minutes": "Частые неудачные попытки входа приведут к временной блокировке учетной записи. Повторите попытку через 5 минут.", "From port": "Исходный порт", "Front End": "Фронт-энд", "Frontend": "Фронтенд", "Full": "Полный", "Full Backup": "Полное резервное копирование", "GPU Count": "Количество GPU", "GPU Info": "Информация о GPU", "GPU Model": "Модель GPU", "GPU Parameters": "Параметры GPU", "GPU Type": "Тип GPU", "GPU model, used when configuring Compute Optimized Type with GPU": "", "GPU pass-through will load GPU devices directly to the instance for use. VGPU is a GPU virtualization solution. GPU resources will be segmented and distributed to multiple instances for shared use.": "GPU-проход загрузит устройства GPU напрямую в инстанс для использования. VGPU - это виртуализационное решение для GPU. Ресурсы GPU будут разделены и распределены между несколькими инстансами для совместного использования.", "GRE": "GRE", "Gabon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gambia": "Гамбия", "Gateway": "<PERSON><PERSON><PERSON><PERSON>", "Gateway IP": "IP-адрес шлюза", "Gateway Time-out (code: 504) ": "Время ожидания шлюза (код: 504)", "Gateway ip {gateway_ip} conflicts with allocation pool {pool}": "IP-адрес шлюза {gateway_ip} конфликтует с пулом адресов {pool}", "General Purpose": "Универсальное назначение", "Generated Time": "", "Georgia": "Грузия", "Germany": "Германия", "Get OpenRC file": "Получить файл OpenRC", "Get Token": "Получить токен", "Get {name} detail error.": "Ошибка получения подробной информации о {name}.", "Get {name} error.": "Ошибка получения {name}.", "Ghana": "<PERSON><PERSON><PERSON>", "Gibraltar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Given IP": "Указанный IP", "Glance": "Glance", "Glance Image": "<PERSON><PERSON><PERSON><PERSON><PERSON> Glance", "Global Setting": "Глобальные настройки", "GlusterFS": "GlusterFS", "Grant Databases Access": "Предоставить доступ к базам данных", "Greece": "Греция", "Greenland": "Гренландия", "Grenada": "Гренада", "Guadeloupe": "Гваделу<PERSON>а", "Guam": "<PERSON><PERSON><PERSON><PERSON>", "Guatemala": "Гватемала", "Guinea": "Гвинея", "Guinea Bissau": "Гвинея-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Guyana": "Г<PERSON><PERSON><PERSON><PERSON>", "HDFS": "HDFS", "HEALTHY": "ЗДОРОВ", "HTTP Proxy": "HTTP-прокси", "HTTP Version not supported (code: 505) ": "HTTP-версия не поддерживается (код: 505)", "HTTPS Proxy": "HTTPS-прокси", "Haiti": "<PERSON>аити", "Hard Reboot": "Жесткая перезагрузка", "Hard Rebooting": "Выполняется жесткая перезагрузка", "Hash": "Хэш", "Health Check CMD": "Команда проверки состояния", "Health Check Interval": "Интервал проверки состояния", "Health Check Retries": "Попытки проверки состояния", "Health Check Timeout": "Тайм-аут проверки состояния", "Health Checking Log": "Жу<PERSON>нал проверки состояния", "Health Inspection": "Медицинская инспекция", "Health Monitor": "Монитор состояния", "Health Monitor Delay": "Задержка монитора состояния", "Health Monitor Detail": "Подробности монитора состояния", "Health Monitor Max Retries": "Максимальное количество попыток монитора состояния", "Health Monitor Name": "Имя монитора состояния", "Health Monitor Timeout": "Тайм-аут монитора состояния", "Health Monitor Type": "Тип монитора состояния", "Health Status": "Состояние здоровья", "HealthMonitor Type": "Тип монитора состояния", "Healthy": "Здоровый", "Heartbeat Timestamp": "Метка времени сердцебиения", "Hello, {name}": "Привет, {name}", "Heterogeneous Computing": "Гетерогенные вычисления", "Hidden": "Скрыто", "Hide Advanced Options": "Скрыть расширенные опции", "Hide Default Firewalls": "", "Hide Default Policies": "", "Hide Default Rules": "", "High Clock Speed": "Высокая тактовая частота", "Home": "Главная", "Home page": "Главная страница", "Honduras": "Гон<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hong Kong": "Гонконг", "Host": "Хо<PERSON>т", "Host Aggregate": "Агрега<PERSON><PERSON><PERSON> хостов", "Host Aggregates": "Агрегато<PERSON>ы хостов", "Host Average Network IO": "Средний сетевой ввод-вывод хоста", "Host CPU Usage": "Использование процессора хоста", "Host Detail": "Подробности о хосте", "Host Disk Average IOPS": "Средние IOPS диска хоста", "Host Memory Usage": "Использование памяти хоста", "Host Name": "", "Host Routes": "Мар<PERSON>руты хоста", "Host Routes Format Error(e.g. *************/24,***********)": "Ошибка формата маршрутов хоста (например, *************/24,***********)", "Host Routes Format Error(e.g. ::0a38:01fe/24,::0a38:01fe)": "Ошибка формата маршрутов хоста (например, ::a38:01fe/24,::0a38:01fe)", "Hostname": "Имя хоста", "Hosts": "Хо<PERSON>ты", "Hosts Detail": "Подробности о хостах", "Hungary": "Венгрия", "Hypervisor Detail": "Подробности о гипервизоре", "Hypervisors": "Гипервизоры", "ICMP": "", "ICMP Code": "Код ICMP", "ICMP Type": "Тип ICMP", "ICMP Type/ICMP Code": "Тип ICMP/Код ICMP", "ID": "ID", "ID/Floating IP": "ID/Плавающий IP", "ID/Name": "ID/Имя", "IGMP": "IGMP", "IKE Policies": "Политики IKE", "IKE Policy": "Политика IKE", "IKE Version": "Версия IKE", "IP": "IP", "IP Address": "IP-адрес", "IP Distribution Mode": "Режим распределения IP", "IP Protocol": "Протокол IP", "IP Usage": "Использование IP", "IP Version": "Версия IP", "IP address allocation polls, one enter per line(e.g. ***********,***********00)": "Опрос выделения IP-адреса, по одному адресу на строку (например, ***********,***********00)", "IP address allocation polls, one enter per line(e.g. {ip})": "Опрос выделения IP-адреса, по одному адресу на строку (например, {ip})", "IPMI Address": "IPMI-адрес", "IPMI Bridge": "IPMI-мост", "IPMI Password": "Пароль IPMI", "IPMI Port": "Порт IPMI", "IPMI Privilege Level": "Уровень привилегий IPMI", "IPMI Protocol Version": "Версия протокола IPMI", "IPMI Username": "Имя пользователя IPMI", "IPMITool": "IPMITool", "IPXE": "IPXE", "IPsec Policies": "Политики IPsec", "IPsec Policy": "Политика IPsec", "IPsec Site Connection": "Соединение IPsec Site", "IPsec Site Connections": "Соединения IPsec Site", "IPsec site connection Detail": "Подробности о соединении IPsec Site", "IPv4": "IPv4", "IPv4 Address": "IPv4-адрес", "IPv6": "IPv6", "IPv6 Address": "IPv6-адрес", "IPv6 Address Record": "Запись IPv6-адреса", "IPv6-Encap": "IPv6-Encap", "IPv6-Frag": "IPv6-Frag", "IPv6-ICMP": "IPv6-ICMP", "IPv6-NoNxt": "IPv6-NoNxt", "IPv6-Opts": "IPv6-Opts", "IPv6-Route": "IPv6-Route", "ISO - Optical disc image format": "ISO - Формат оптического диска", "Iceland": "Исландия", "Id": "", "Identifier of the physical port on the switch to which node’s port is connected to": "Идентификатор физического порта на коммутаторе, к которому подключен порт узла", "Identity": "Идентификация", "If \"Enable\" fails to roll back, the resource will be deleted after the creation fails; if \"Disable\" fails to roll back, the resource will be retained after the creation fails.": "Если \"Включение\" не удается откатить, ресурс будет удален после неудачного создания; если \"Отключение\" не удается откатить, ресурс останется после неудачного создания.", "If OS is Linux, system will reset root password, if OS is Windows, system will reset Administrator password.": "Если ОС - Linux, система сбросит пароль root, если ОС - Windows, система сбросит пароль Администратора.", "If an instance is using this flavor, deleting it will cause the instance's flavor data to be missing. Are you sure to delete {name}?": "Если инстанс использует этот флейвор, его удаление приведет к отсутствию данных о флейворе у инстанса. Вы уверены, что хотите удалить {name}?", "If checked, the network will be enable.": "Если отмечено, сеть будет включена.", "If exposed port is specified, this parameter will be ignored.": "Если указан выставленный порт, этот параметр будет проигнорирован.", "If it is an SNI type certificate, a domain name needs to be specified": "Если это сертификат типа SNI, необходимо указать имя домена.", "If it’s not set, the value of this in the template will be used.": "Если не установлено, будет использовано значение из шаблона.", "If no gateway is specified, the first IP address will be defaulted.": "Если не указан шлюз, будет использоваться первый IP-адрес по умолчанию.", "If not provided, the roles assigned to the application credential will be the same as the roles in the current token.": "Если не предоставлено, роли, назначенные приложенному учетному данным, будут такими же, как роли в текущем токене.", "If nova-compute on the host is disabled, it will be forbidden to be selected as the target host.": "Если nova-compute на хосте отключен, его нельзя будет выбрать в качестве целевого хоста.", "If set then all tenants will be able to see this share.": "Если установлено, все арендаторы смогут видеть эту долю.", "If the capacity of the disk is large, the type modify operation may take several hours. Please be cautious.": "Если объем диска большой, операция изменения типа может занять несколько часов. Пожалуйста, будьте осторожны.", "If the listener has an SNI certificate installed, it cannot be removed. Please delete the listener or replace the SNI certificate": "Если у слушателя установлен сертификат SNI, его нельзя удалить. Пожалуйста, удалите слушателя или замените сертификат SNI", "If the root disk has a snapshot, it will affect the deletion of the original disk during reconstruction or the recovery of the instance snapshot.": "", "If the value is set to 0, it means unlimited": "Если значение установлено как 0, это означает неограниченное количество", "If the volume associated with the snapshot has changed the volume type, please modify this option manually; if the volume associated with the snapshot keeps the volume type unchanged, please ignore this option. (no need to change).": "Если тип диска, связанного со снимком, был изменен, пожалуйста, измените эту опцию вручную. Если тип диска, связанного со снимком, остался неизменным, проигнорируйте эту опцию (не требуется изменять).", "If this parameter is selected, resumable uploads are supported, but the total upload time may be increased by a small amount. Images smaller than 200M are not recommended.": "Если выбран этот параметр, поддерживаются возобновляемые загрузки, но общее время загрузки может быть увеличено на небольшую сумму. Изображения размером менее 200M не рекомендуются.", "If this parameter is specified, Zun will create a security group with a set of rules to open the ports that should be exposed, and associate the security group to the container.": "Если этот параметр указан, Zun создаст группу безопасности с набором правил для открытия портов, которые должны быть доступны, и свяжет группу безопасности с контейнером.", "If you are not authorized to access any project, or if the project you are involved in has been deleted or disabled, contact the platform administrator to reassign the project": "Если у вас нет разрешения на доступ к какому-либо проекту, или если проект, в котором вы участвуете, был удален или отключен, обратитесь к администратору платформы для повторного назначения проекта", "If you are not sure which authentication method to use, please contact your administrator.": "Если вы не уверены, какой метод аутентификации использовать, обратитесь к вашему администратору.", "If you choose a port which subnet is different from the subnet of LB, please ensure connectivity between the two.": "Если вы выбираете порт, который находится в другой подсети по сравнению с подсетью балансировщика нагрузки, пожалуйста, обеспечьте соединение между ними.", "If you do not fill in parameters such as cpus, memory_mb, local_gb, cpu_arch, etc., you can automatically inject the configuration and Mac address of the physical machine by performing the \"Auto Inspect\" operation.": "Если вы не заполняете параметры, такие как cpus, memory_mb, local_gb, cpu_arch и др., вы можете автоматически внедрить конфигурацию и MAC-адрес физической машины, выполнив операцию \"Автоинспекция\".", "If you still want to keep the disk data, it is recommended that you create a backup for the disk before deleting.": "Если вы все еще хотите сохранить данные на диске, рекомендуется создать резервную копию диска перед удалением.", "Illegal JSON scheme": "Недопустимая схема JSON", "Image": "Об<PERSON><PERSON><PERSON>", "Image & OS": "Образ и ОС", "Image Backup": "Резервное копирование образа", "Image Detail": "Подробная информация об образе", "Image Driver": "Драйвер образа", "Image Info": "Информация об образе", "Image Name": "Имя образа", "Image Pending Upload": "Ожидание загрузки образа", "Image Pulling": "Загрузка образа", "Image Size": "Размер образа", "Image Snapshot Pending": "Ожидание снимка образа", "Image Uploading": "Загрузка образа", "Images": "Образы", "Immediate effect": "Немедленный эффект", "Immediately delete": "Немедленное удаление", "Implied Roles": "Подразумеваемые роли", "Import Keypair": "Импорт ключевой пары", "Import Metadata": "Импорт метаданных", "Import metadata": "Импорт метаданных", "Importing": "Импорт", "In Cluster": "В кластере", "In Use": "Используется", "In general, administrator for Windows, root for Linux, please fill by image uploading.": "Обычно, администратор для Windows, root для Linux, заполняется загрузкой образа.", "In order to avoid data loss, the instance will shut down and interrupt your business. Please confirm carefully.": "Чтобы избежать потери данных, инстанс будет выключен, что может прервать вашу работу. Пожалуйста, подтвердите внимательно.", "In the last 30 days": "За последние 30 дней", "In the last 7 days": "За последние 7 дней", "In the last hour": "За последний час", "In-use": "Используется", "Inactive": "Неактивный", "Increment Backup": "Инкрементное резервное копирование", "Incremental": "Инкрементный", "Incremental Backup": "Инкрементное резервное копирование", "India": "Индия", "Indicates whether this VPN can only respond to connections or both respond to and initiate connections.": "Указывает, может ли этот VPN только отвечать на подключения или инициировать и отвечать на подключения.", "Indonesia": "Индонезия", "Infinity": "Бесконечность", "Info": "Информация", "Ingress": "Входящий", "Ingress Policy": "", "Ingress Policy ID": "", "Ingress Policy Name": "", "Init Complete": "Завершено инициализация", "Init Failed": "Инициализация не удалась", "Init In Progress": "Идет инициализация", "Initial Admin User": "Начальный администратор", "Initial Databases": "Начальные базы данных", "Initial Volume Size": "Начальный размер диска", "Initialize Databases": "Инициализировать базы данных", "Initiator Mode": "Режим инициатора", "Input destination port or port range (example: 80 or 80:160)": "Введите порт назначения или диапазон портов (пример: 80 или 80:160)", "Input external port or port range (example: 80 or 80:160)": "Введите внешний порт или диапазон портов (пример: 80 или 80:160)", "Input internal port or port range (example: 80 or 80:160)": "Введите внутренний порт или диапазон портов (пример: 80 или 80:160)", "Input source port or port range (example: 80 or 80:160)": "Введите исходный порт или диапазон портов (пример: 80 или 80:160)", "Insecure Registry": "Ненадежный реестр", "Insert": "", "Insert After": "", "Insert Before": "", "Insert Rule": "", "Inspect Failed": "Проверка не удалась", "Inspecting": "Проверка", "Instance": "<PERSON>нст<PERSON><PERSON><PERSON>", "Instance \"{ name }\" has already been locked.": "Инстан<PERSON> \"{ name }\" уже заблокирован.", "Instance \"{ name }\" is ironic, can not soft reboot it.": "Инстан<PERSON> \"{ name }\" является инста<PERSON><PERSON><PERSON><PERSON>, его нельзя мягко перезагрузить.", "Instance \"{ name }\" is locked, can not delete it.": "Инстан<PERSON> \"{ name }\" заблокирован, его нельзя удалить.", "Instance \"{ name }\" is locked, can not pause it.": "Инстан<PERSON> \"{ name }\" заблокирован, его нельзя приостановить.", "Instance \"{ name }\" is locked, can not reboot it.": "Инстан<PERSON> \"{ name }\" заблокирован, его нельзя перезагрузить.", "Instance \"{ name }\" is locked, can not resume it.": "Инстан<PERSON> \"{ name }\" заблокирован, его нельзя возобновить.", "Instance \"{ name }\" is locked, can not soft reboot it.": "Инстан<PERSON> \"{ name }\" заблокирован, его нельзя мягко перезагрузить.", "Instance \"{ name }\" is locked, can not start it.": "Инстан<PERSON> \"{ name }\" заблокирован, его нельзя запустить.", "Instance \"{ name }\" is locked, can not stop it.": "Инстан<PERSON> \"{ name }\" заблокирован, его нельзя остановить.", "Instance \"{ name }\" is locked, can not suspend it.": "Инстан<PERSON> \"{ name }\" заблокирован, его нельзя приостановить.", "Instance \"{ name }\" is locked, can not unpause it.": "Инстан<PERSON> \"{ name }\" заблокирован, его нельзя снять с паузы.", "Instance \"{ name }\" is not locked, can not unlock it.": "Инстан<PERSON> \"{ name }\" не заблокирован, его нельзя разблокировать.", "Instance \"{ name }\" status is not active, can not soft reboot it.": "Статус инстанса \"{ name }\" не активен, его нельзя мягко перезагрузить.", "Instance \"{ name }\" status is not in active or shutoff, can not reboot it.": "Статус инстанса \"{ name }\" не активен или выключен, его нельзя перезагрузить.", "Instance \"{ name }\" status is not in active or suspended, can not stop it.": "Статус инстанса \"{ name }\" не активен или приостановлен, его нельзя остановить.", "Instance \"{ name }\" status is not in active, can not pause it.": "Статус инстанса \"{ name }\" не активен, его нельзя приостановить.", "Instance \"{ name }\" status is not in active, can not suspend it.": "Статус инстанса \"{ name }\" не активен, его нельзя приостановить.", "Instance \"{ name }\" status is not in paused, can not unpause it.": "Статус инстанса \"{ name }\" не приостановлен, его нельзя снять с паузы.", "Instance \"{ name }\" status is not in suspended, can not resume it.": "Статус инстанса \"{ name }\" не приостановлен, его нельзя возобновить.", "Instance \"{ name }\" status is not shutoff, can not start it.": "Статус инстанса \"{ name }\" не выключен, его нельзя запустить.", "Instance Addr": "Адрес инстанса", "Instance Architecture": "Архитектура инстанса", "Instance Console Log": "<PERSON><PERSON><PERSON><PERSON><PERSON> консоли экземпляра", "Instance Detail": "Подробности инстанса", "Instance ID": "Идентификатор инстанса", "Instance IP": "IP-адрес инстанса", "Instance Info": "Информация об инстансе", "Instance Port": "", "Instance Related": "Связанный инстанс", "Instance Snapshot": "Снимок инстанса", "Instance Snapshot Detail": "Подробности снимка инстанса", "Instance Snapshot Name": "Название снимка инстанса", "Instance Snapshots": "Снимки инстанса", "Instance Status": "Статус инстанса", "Instance UUID": "", "Instance-HA": "", "Instances": "Инстансы", "Instances \"{ name }\" are locked, can not delete them.": "Инстансы \"{ name }\" заблокированы, нельзя их удалить.", "Insufficient {name} quota to create resources (left { quota }, input { input }).": "Недостаточно квоты {name} для создания ресурсов (осталось { quota }, введено { input }).", "Interface Info": "Информация об интерфейсе", "Interface Name:": "Имя интерфейса:", "Interface for vendor-specific functionality on this node": "Интерфейс для функциональности, специфичной для вендора, на этом узле", "Interface used for attaching and detaching volumes on this node": "Интерфейс, используемый для подключения и отключения дисков на этом узле", "Interface used for configuring RAID on this node": "Интерфейс, используемый для настройки RAID на этом узле", "Interfaces": "Интерфейсы", "Internal Ip Address": "Внутренний IP-адрес", "Internal Network Bandwidth (Gbps)": "Пропускная способность внутренней сети (Гбит/с)", "Internal Port": "Внутренний порт", "Internal Port/Port Range": "Внутренний порт/диапазон портов", "Internal Server Error (code: 500) ": "Внутренняя ошибка сервера (код: 500) ", "Invalid": "Недопустимо", "Invalid CIDR.": "Недопустимый CIDR.", "Invalid IP Address": "Недопустимый IP-адрес", "Invalid IP Address and Port": "Недопустимый IP-адрес и порт", "Invalid Mac Address. Please Use \":\" as separator.": "Недопустимый MAC-адрес. Пожалуйста, используйте \":\" в качестве разделителя.", "Invalid Tag Value: {tag}": "Недопустимое значение тега: {tag}", "Invalid combination": "Недопустимая комбинация", "Invalid: ": "Недопустимо: ", "Invalid: Allocation Pools Format Error(e.g. ***********,***********00) and start ip should be less than end ip": "Недопустимо: ошибка формата диапазонов выделения (например, ***********,***********00) и начальный IP-адрес должен быть меньше конечного IP-адреса", "Invalid: Allocation Pools Format Error(e.g. fd00:dead:beef:58::9,fd00:dead:beef:58::13) and start ip should be less than end ip": "Недопустимо: ошибка формата диапазонов выделения (например, fd00:dead:beef:58::9,fd00:dead:beef:58::13) и начальный IP-адрес должен быть меньше конечного IP-адреса", "Invalid: CIDR Format Error(e.g. **********/24)": "Недопустимо: ошибка формата CIDR (например, **********/24)", "Invalid: DNS Format Error(e.g. 1001:1001::)": "Недопустимо: ошибка формата DNS (например, 1001:1001::)", "Invalid: DNS Format Error(e.g. ***************)": "Недопустимо: ошибка формата DNS (например, ***************)", "Invalid: Domain name cannot be duplicated": "Недопустимо: дублирование имени домена недопустимо", "Invalid: Password must be the same with confirm password.": "Недопустимо: пароль должен совпадать с подтверждением пароля.", "Invalid: Please input a valid ip": "Недопустимо: введите действительный IP-адрес", "Invalid: Please input a valid ipv4": "Недопустимо: введите действительный IPv4", "Invalid: Please input a valid ipv6.": "Недопустимо: введите действительный IPv6.", "Invalid: Project name can not be chinese": "", "Invalid: Project names in the domain can not be repeated": "", "Invalid: Quota value(s) cannot be less than the current usage value(s): { used } used.": "Недопустимо: значения квоты не могут быть меньше текущих значений использования: использовано { used }.", "Invalid: User Group names in the domain can not be repeated": "", "Invalid: User names in the domain can not be repeated": "", "Ip Address": "IP-адрес", "Iran (Islamic Republic of)": "Иран (Исламская Республика)", "Iraq": "Ирак", "Ireland": "Ирландия", "Ironic Instance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ic", "Ironic Instance Name": "Имя инстан<PERSON><PERSON> Ironic", "Is Current Project": "Текущий проект", "Is Public": "Общедоступный", "Is admin only": "Только для администратора", "Is associate to floating ip: ": "Ассоциирован с плавающим IP: ", "Is external network port": "Внешний сетевой порт", "Isolate": "", "Isolate(No multithreading)": "Изолировать (без многопоточности)", "Israel": "Израиль", "It is IPv6 type.": "Это тип IPv6.", "It is recommended that the { instanceType } instance simultaneously set large page memory to large. { instanceType } instances also require faster memory addressing capabilities.": "Рекомендуется одновременно установить большой объем страницы для инстанса { instanceType }. инстансы { instanceType } также требуют более быстрых возможностей адресации памяти.", "It is recommended that you perform this cloning operation on a disk without any reading/writing": "Рекомендуется выполнять эту операцию клонирования на диске без чтения/записи", "It is recommended that you use the private network address 10.0.0.0/8, **********/12, ***********/16": "Рекомендуется использовать частный сетевой адрес 10.0.0.0/8, **********/12, ***********/16", "It is recommended that { instanceType } instance simultaneously set NUMA affinity policy for PCIE device to force or priority matching. This configuration can further improve PCIE computing performance.": "Рекомендуется одновременно установить политику NUMA-аффинности для устройства PCIE для инстанса { instanceType } на принудительное или приоритетное соответствие. Эта конфигурация может дополнительно улучшить производительность вычислений с использованием PCIE.", "It is recommended to install and use this agent. The instance created with this image can be used to modify the password (qemu_guest_agent needs to be installed when creating the image).": "Рекомендуется установить и использовать этот агент. инстанс, созданный с использованием этого образа, может использоваться для изменения пароля (при создании образа необходимо установить qemu_guest_agent).", "It is recommended to refer to the following description format, otherwise it may not be effective": "", "It is recommended to set CPU binding strategy as binding on { instanceType } instance. This configuration further improves the performance of the instance CPU.": "Рекомендуется установить стратегию привязки CPU как привязку для инстанса { instanceType }. Эта конфигурация дополнительно улучшает производительность CPU инстанса.", "It is recommended to set the CPU thread binding policy as thread binding in { instanceType } instance, which can further improve the CPU performance of instance.": "Рекомендуется установить политику привязки потока CPU как привязку потока в инстансе { instanceType }, что может дополнительно улучшить производительность CPU инстанса.", "It is suggested to use the marked AZ directly, too much AZ will lead to the fragmentation of available resources": "Рекомендуется использовать помеченную AZ напрямую, слишком много AZ может привести к фрагментации доступных ресурсов", "It is unreachable for all floating ips.": "Недоступно для всех плавающих IP.", "It is unreachable for this floating ip.": "Недоступно для этого плавающего IP.", "Italy": "Италия", "Items in Cache": "Элементы в кэше", "Jamaica": "Ямайка", "Japan": "Япония", "Jordan": "Иордания", "Jump to Console": "Перейти к консоли", "Kampuchea": "Камбоджа", "Kazakhstan": "Казахстан", "Kenya": "Кения", "Kernel ID": "Идентификатор ядра", "Kernel Image": "Образ ядра", "Kernel Version": "Версия ядра", "Key": "<PERSON><PERSON><PERSON><PERSON>", "Key Pair": "Ключевая пара", "Key Pairs": "Ключевые пары", "Key Size (bits)": "Размер ключа (бит)", "Keypair": "Ключевая пара", "Keypair Detail": "Подробности ключевой пары", "Keypair Info": "Информация о ключевой паре", "Keystone Credentials": "Учетные данные Keystone", "Keystone token is expired.": "Токен Keystone истек, пожалуйста, проверьте правильность времени сервера и подтвердите действительность токена", "Kill": "Убить", "Kill Container": "Убить контейнер", "Kill Signal": "Сигнал завершения", "Killed": "Убит", "Kubernetes": "Kubernetes", "Kuwait": "<PERSON>у<PERSON><PERSON><PERSON><PERSON>", "Kyrgyzstan": "Киргизия", "LB Algorithm": "Алгоритм Балансировки Нагрузки", "LEAST_CONNECTIONS": "Наименьшее Количество Соединений", "Labels": "Метки", "Lao People's Democratic Republic": "Лаосская Народно-Демократическая Республика", "Large": "Больш<PERSON>й", "Large Screen": "Больш<PERSON>й Экран", "Large(Optimal performance)": "Большой (Оптимальная Производительность)", "Last 2 Weeks": "Последние 2 Недели", "Last 24H Status": "Статус За Последние 24 Часа", "Last 7 Days": "Последние 7 Дней", "Last Day": "Последний День", "Last Hour": "Последний Час", "Last Updated": "Последнее Обновление", "Last week alarm trend": "Тренд Аварий За Последнюю Неделю", "Latvia": "Латвия", "Leave Maintenance Mode": "Покинуть Режим Обслуживания", "Lebanon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Left": "Левый", "Lesotho": "Лесото", "Liberia": "Либерия", "Libyan Arab Jamahiriya": "Ливийская Арабская Джамахирия", "Liechtenstein": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "Lifetime": "Срок службы", "Lifetime Value": "Срок службы (продолжительность службы)", "Listener": "Слушатель", "Listener Connection Limit": "Ограничение подключений слушателя", "Listener Description": "Описание слушателя", "Listener Detail": "Подробности слушателя", "Listener Name": "Имя слушателя", "Listener Number": "Номер слушателя", "Listener Protocol": "Протокол слушателя", "Listener Protocol Port": "Порт протокола слушателя", "Listeners": "Слушатели", "Lithuania": "Литва", "Live Migrate": "Живая миграция (перенос)", "Live Migration At Destination": "Живая миграция на конечной точке", "Load Balancer": "Балан<PERSON>ировщик нагрузки", "Load Balancer Detail": "Подробности балансировщика нагрузки", "Load Balancer Name": "Имя балансировщика нагрузки", "Load Balancers": "Балансировщики нагрузки", "Load Template from a file": "Загрузить шаблон из файла", "Load from local files": "Загрузить из локальных файлов", "LoadBalancers Instances": "Балансировщики нагрузки (инстансы)", "Local": "Локальный", "Local Endpoint Group": "Локальная группа конечных точек", "Local Endpoint Group ID": "Идентификатор локальной группы конечных точек", "Local Link Connection": "Локальное соединение через ссылку", "Local Network": "Локальная сеть", "Local SSD": "Локальный SSD", "Local Subnet": "Локальная подсеть", "Locality": "Локальность", "Lock": "Блокировать", "Lock Instance": "Блокировать инстанс", "Lock Status": "Статус блокировки", "Lock instance will lock the operations that have a direct impact on the operation of the instance, such as: shutdown, restart, delete, the mounting and unmounting of volume, etc. It does not involve the capacity expansion and change type of volume.": "Блокировка инстанса заблокирует операции, которые непосредственно влияют на работу инстанса, такие как: выключение, перезапуск, удаление, подключение и отключение дисков и другие. Это не влияет на расширение емкости и изменение типа диска.", "Locked": "Заблокирован", "Log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Log Length": "<PERSON><PERSON><PERSON><PERSON> журнала", "Log in": "Войти", "Login Name": "Имя пользователя", "Login Password": "Пароль", "Login Type": "Тип входа", "Logs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Luxembourg": "Люксембург", "MAC Address": "MAC-адрес", "MAC Learning State": "Состояние обучения MAC-адреса", "MAPRFS": "MAPRFS", "MEM %": "Использование памяти (%)", "MEM LIMIT (MiB)": "Лимит памяти (MiB)", "MEM USAGE (MiB)": "Использование памяти (MiB)", "MTU": "MTU (Максимальный размер передаваемого пакета)", "Mac Address": "MAC-адрес", "MacVTap": "MacVTap", "Macau": "Макао", "Madagascar": "Мадагаскар", "Mail Exchange Record": "Запись обмена почтой", "Maintained": "Обслуживается", "Maintenance": "Обслуживание", "Malawi": "Малави", "Malaysia": "Малайзия", "Maldives": "Мальдивы", "Mali": "Мали", "Malta": "Мальта", "Manage Access": "Управление доступом", "Manage Access Rule": "Управление правилами доступа", "Manage Error": "Управление ошибкой", "Manage Host": "Управление хостом", "Manage Metadata": "Управление метаданными", "Manage Ports": "", "Manage QoS Spec": "Управление спецификацией QoS", "Manage Resource Types": "Управление типами ресурсов", "Manage Security Group": "Управление группой безопасности", "Manage Starting": "Управление запуском", "Manage State": "Управление состоянием", "Manage User": "Управление пользователем", "Manage User Group": "Управление группой пользователей", "Manage host": "Управление хостом", "Manage user": "Управление пользователем", "Manage user group": "Управление группой пользователей", "Manageable": "Управляемый", "Management": "Управление", "Management Reason": "Причина управления", "Mandatory for secondary zones. The servers to slave from to get DNS information.": "", "Manu": "<PERSON>е<PERSON><PERSON>", "Manual input": "Ручной ввод", "Manually Assigned Address": "Адре<PERSON>, назначенный вручную", "Manually Specify": "Задать вручную", "Marshall Islands": "Маршалловы Острова", "Martinique": "Мартиника", "Master Node Addresses": "Адреса мастер-узлов", "Master Node Flavor": "Тип мастер-узла", "Master Node LB Enabled": "Балансировка нагрузки мастер-узла включена", "Masters": "Мастер-узлы", "Mauritania": "Мавритания", "Mauritius": "Маврикий", "Max Avail": "Максимальная доступность", "Max BandWidth": "Максимальная пропускная способность", "Max Burst": "Максимальный всплеск", "Max Retries": "Максимальное количество повторных попыток", "Max Retry": "Максимальное количество повторных попыток", "Max connect": "Максимальное количество подключений", "Maximum interval time for each health check response": "Максимальное временное интервал для каждого ответа проверки состояния", "Maximum time to allow one check to run in seconds": "Максимальное время для выполнения одной проверки в секундах", "Mayotte": "Майотта", "Mem": "Память", "Member Count": "Количество участников", "Member Detail": "Подробности участника", "Member Num": "Номер участника", "Members": "Участники", "Members of Each Group": "Участники каждой группы", "Members of Each Server Group": "Участники каждой группы серверов", "Memory": "Память", "Memory (GiB)": "Пам<PERSON><PERSON>ь (ГиБ)", "Memory (MiB)": "Память (МиБ)", "Memory Optimized": "Оптимизировано под память", "Memory Page": "Страница памяти", "Memory Page Size": "Размер страницы памяти", "Memory Usage": "Использование памяти", "Memory Usage(%)": "Использование памяти(%)", "Memory Usages (GiB)": "Использование памяти (ГиБ)", "Mesos": "Mesos", "Message": "Сообщение", "Message Details": "Подробности сообщения", "Message Queue Service": "Сервис очереди сообщений", "Metadata": "Метаданные", "Metadata Definitions": "Определения метаданных", "Metadata Detail": "Подробности метаданных", "Mexico": "Мексика", "Micronesia": "Микронезия", "Migrate": "Миграция", "Migrate Volume": "Миграция Диска", "Migrate volume": "Миграция диска", "Migrating": "Миграция", "Migrating To": "Миграция на", "Min Memory": "Минимальная память", "Min Memory (GiB)": "Минимальная память (ГиБ)", "Min System Disk": "Минимальный системный диск", "Min System Disk (GiB)": "Минимальный системный диск (ГиБ)", "Min size": "Минимальный размер", "Min. Disk": "Минимальный размер диска", "Min. RAM": "Минимальный объем RAM", "Minimum value is 68 for IPv4, and 1280 for IPv6.": "Минимальное значение - 68 для IPv4 и 1280 для IPv6.", "Miscellaneous": "Разное", "Missing IP Address": "Отсутствует IP-адрес", "Missing Port": "Отсутствует порт", "Missing Subnet": "Отсутствует подсеть", "Missing Weight": "Отсутствует вес", "Modification Times": "Время изменения", "Modify Instance Tags": "Изменить теги инстанса", "Modify Project Tags": "Изменить теги проекта", "Modify QoS": "Изменить качество обслуживания", "Moldova": "Молдова", "Monaco": "Монако", "Mongolia": "Монголия", "Monitor Center": "Центр мониторинга", "Monitor Overview": "Обзор мониторинга", "Montenegro": "Черногория", "Montserrat": "Монтсеррат", "More": "<PERSON><PERSON><PERSON>", "More Actions": "Дополнительные действия", "More than one label is required, such as: \"example.org.\"": "", "Morocco": "Марокко", "Mount ISO": "Монтировать ISO", "Mount snapshot support": "Поддержка монтирования снимка", "Mozambique": "Мозамбик", "Multiple filter tags are separated by enter": "Несколько фильтров разделяются Enter", "My Role": "Моя роль", "MySQL Actions": "Действия с MySQL", "Myanmar": "Мьянма", "N/A": "N/A", "NET I/O(B)": "NET I/O(B)", "NFS": "NFS", "NOOP": "NOOP", "NUMA Node": "Узел NUMA", "NUMA Node Count": "Количество узлов NUMA", "NUMA Nodes": "Узлы NUMA", "Name": "Имя", "Name Server": "Имя сервера", "Name can not be duplicated": "Имя не может быть дублировано", "Name or ID og the container image": "Имя или ID образа контейнера", "Namespace": "Пространство имён", "Namibia": "Намибия", "Nauru": "Науру", "Nepal": "Непал", "Netherlands": "Нидерланды", "Netherlands Antilles": "Нидерландские Антильские острова", "Network": "Сеть", "Network Attaching": "Подключение сети", "Network Config": "Конфигурация сети", "Network Detaching": "Отключение сети", "Network Detail": "Подробности сети", "Network Driver": "Драйвер сети", "Network Dropped Packets": "Сброшенные пакеты сети", "Network Errors": "Ошибки сети", "Network ID": "Идентификатор сети", "Network ID/Name": "", "Network Info": "Информация о сети", "Network Interface": "Сетевой интерфейс", "Network Line": "Линия сети", "Network Name": "Имя сети", "Network Service": "Сетевая служба", "Network Setting": "Настройка сети", "Network Traffic": "Сетевой трафик", "Network Type": "Тип сети", "Network topology page": "Страница топологии сети", "Networking": "Сетевые настройки", "Networking *": "Сетевые настройки *", "Networks": "Сети", "Neutron Agent Detail": "Подробная информация о Neutron Agent", "Neutron Agents": "Агенты Neutron", "Neutron Net": "Сеть Neutron", "Neutron Service": "<PERSON>ер<PERSON><PERSON><PERSON> Neutron", "Neutron Subnet": "Подсеть Neutron", "New": "Новый", "New Availability Zone": "Новая зона доступности", "New Caledonia": "Новая Каледония", "New Status": "Новый статус", "New Tag": "Новый тег", "New Volume": "Новый диск", "New Zealand": "Новая Зеландия", "Next": "Следующий", "Next Hop": "Следующий переход", "Nicaragua": "Никарагуа", "Niger": "<PERSON>и<PERSON><PERSON><PERSON>", "Nigeria": "Нигерия", "No": "Нет", "No - Do not create a new system disk": "", "No Console": "Нет консоли", "No Logs...": "Нет журналов...", "No Monitor": "Нет монитора", "No Outputs": "Нет выводов", "No Proxy": "Нет прокси", "No Raid": "Нет RAID", "No State": "Нет состояния", "No Task": "Нет задачи", "No Vender": "Нет поставщика", "No default pool set": "", "Node": "Узел", "Node Addresses": "Адреса узла", "Node Driver": "Дра<PERSON><PERSON>ер узла", "Node Flavor": "Конфигурация узла", "Node ID/Name": "Идентификатор/имя узла", "Node Info": "Информация о узле", "Node Name": "Имя узла", "Node Spec": "Спецификация узла", "Nodes": "Узлы", "Nodes To Remove": "Узлы для удаления", "Norfolk Island": "Остров Норфолк", "Normal": "Обычный", "North Korea": "Северная Корея", "Northern Mariana Islands": "Северные Марианские острова", "Norway": "Норвегия", "Not Implemented (code: 501) ": "Не реализовано (код: 501)", "Not Open": "Не открыто", "Not dealt with for the time being": "На данный момент он не будет обработан", "Not deleted with the instance": "Не удалено с инстансом", "Not locked": "Не заблокировано", "Not select": "Не выбрано", "Not yet bound": "Пока не привязано", "Not yet selected": "Пока не выбрано", "Note that when using a share type with the driver_handles_share_servers extra spec as False, you should not provide a share network.": "Обратите внимание, что при использовании типа сети с дополнительной спецификацией driver_handles_share_servers, равной False, не следует указывать сеть для общего доступа.", "Note: Are you sure you need to modify the volume type?": "Примечание: Вы уверены, что вам нужно изменить тип диска?", "Note: Please consider the container name carefully since it couldn't be changed after created.": "Примечание: Пож<PERSON><PERSON>уйста, обдумайте имя контейнера тщательно, так как его нельзя будет изменить после создания.", "Note: The security group you use will act on all virtual adapters of the instance.": "Примечание: Группа безопасности, которую вы используете, будет действовать на все виртуальные адаптеры инстанса.", "Notification Detail": "", "Notifications": "", "Nova Service": "Сервис Nova", "Number of GPU": "Количество GPU", "Number of IPs used by all projects": "Количество IP-адресов, используемых всеми проектами", "Number of Master Nodes": "Количество главных узлов", "Number of Nodes": "Количество узлов", "Number of Ports": "Количество портов", "Number of Usb Controller": "Количество контроллеров USB", "OK": "OK", "OS": "Операционная система", "OS Admin": "Администратор ОС", "OS Disk": "Диск ОС", "OS Type": "<PERSON>и<PERSON>", "OS Version": "Версия ОС", "OSDs": "ОСД", "OSPF": "OSPF", "Object": "", "Object Count": "Количество объектов", "Object Count ": "Количество объектов ", "Object ID": "", "Object ID/Name": "", "Object Name": "", "Object Storage": "Хранили<PERSON>е объектов", "Object Type": "", "Off": "Выключено", "Offline": "Не в сети", "Oman": "О<PERSON><PERSON><PERSON>", "On": "Включено", "On Maintenance": "", "On failure": "При сбое", "One entry per line(e.g. ***************)": "Один элемент на строку (например, ***************)", "One entry per line(e.g. {ip})": "Один элемент на строку (например, {ip})", "One-way authentication": "Аутентификация в одну сторону", "Online": "В сети", "Online Resize": "Изменение размера в режиме онлайн", "Only a MAC address or an OpenFlow based datapath_id of the switch are accepted in this field": "В это поле принимается только MAC-адрес или идентификатор datapath_id на основе OpenFlow коммутатора.", "Only libvirt driver is supported.": "Поддерживаются только драйверы libvirt.", "Only subnets that are already connected to the router can be selected.": "Можно выбирать только подсети, которые уже подключены к маршрутизатору.", "Open External Gateway": "Открыть внешний шлюз", "OpenID Connect": "OpenID Connect", "Operating Status": "Состояние работы", "Operating System": "операционную систему", "Operation Center": "Operation Center", "Operation Name": "Наименование операции", "Operation Time": "Время операции", "Optimized Parameters": "Оптимизированные параметры", "Optional list": "Список опций", "Options": "Опции", "Orchestration": "Оркестрация", "Orchestration Services": "Сервисы оркестрации", "Orchestration information": "Информация о оркестрации", "Origin File Name": "Исходное имя файла", "Original Password": "Исходный пароль", "Other Protocol": "Другой протокол", "Other Service": "Другой сервис", "Other Services": "Другие сервисы", "Others": "Другие", "Out Cluster": "Выход из кластера", "Out of Sync": "Не синхронизировано", "Outputs": "Выходные данные", "Overlapping allocation pools: {pools}": "Перекрывающиеся пулы выделения: {pools}", "Overlay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Overlay2": "Оверлей2", "Overview": "Обзор", "Owned Network": "Собственная сеть", "Owned Network ID": "Идентификатор собственной сети", "Owned Network ID/Name": "Идентификатор/имя собственной сети", "Owned Project": "Собственный проект", "Owned Subnet": "Собственная подсеть", "Owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Ownership of a volume can be transferred from one project to another. The transfer process of the volume needs to perform the transfer operation in the original owner's project, and complete the \"accept\" operation in the receiver's project.": "Права на владение диском можно передать из одного проекта в другой. Процесс передачи диска требует выполнения операции передачи в проекте исходного владельца и завершения операции 'принятия' в проекте получателя.", "PEM encoding": "Кодировка PEM", "PFS": "PFS", "PG Count": "Количество PG", "PGM": "PGM", "PING": "PING", "PTR Domain Name": "Имя домена PTR", "PXE": "PXE", "PXE Enabled": "PXE включен", "Pakistan": "Пакистан", "Palau": "Пал<PERSON><PERSON>", "Palestine": "Палестина", "Panama": "Панама", "Papua New Guinea": "Папуа-Новая Гвинея", "Paraguay": "Параг<PERSON><PERSON>й", "Parameter": "Параметр", "Params Setting": "Настройка параметров", "Password": "Пароль", "Password Type": "Тип пароля", "Password changed successfully, please log in again.": "Пароль успешно изменен, пожалуйста, выполните вход снова.", "Password must be the same with confirm password.": "Пароль должен совпадать с подтверждением пароля.", "Paste": "Вставить", "Paste File": "Вставить файл", "Path": "Путь", "Pause": "Приостановить", "Pause Container": "Приостановить контейнер", "Pause Instance": "Приостановить инстанс", "Paused": "Приостановлено", "Pausing": "Приостанавливается", "Payload": "", "Peer": "<PERSON><PERSON><PERSON>", "Peer Address": "Адрес пира", "Peer Cidrs": "CIDR-блоки пира", "Peer Endpoint Group": "Группа конечных точек пира", "Peer Endpoint Group ID": "Идентификатор группы конечных точек пира", "Peer Gateway Public Address": "Общедоступный адрес шлюза пира", "Peer ID": "Идентификатор пира", "Peer Network": "Сеть пира", "Peer Network Segment": "Сегмент сети пира", "Peer gateway public address for the IPsec site connection": "Общедоступный адрес шлюза пира для соединения IPsec между сайтами", "Pending": "<PERSON>ж<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pending Create": "Ожидает создания", "Pending Delete": "Ожида<PERSON>т удаления", "Pending Update": "Ожидает обновления", "Perform a consistent hash operation on the source IP address of the request to obtain a specific value. At the same time, the back-end server is numbered, and the request is distributed to the server with the corresponding number according to the calculation result. This can enable load distribution of visits from different source IPs, and at the same time enable requests from the same client IP to always be dispatched to a specific server. This method is suitable for load balancing TCP protocol without cookie function.": "Выполните операцию согласованного хэширования на исходном IP-адресе запроса, чтобы получить конкретное значение. В то же время сервера на стороне сервера пронумерованы, и запрос распределяется на сервер с соответствующим номером в соответствии с результатом вычисления. Это позволяет распределять нагрузку на посещение с разных исходных IP-адресов и одновременно обеспечивать направление запросов от одного и того же клиентского IP на конкретный сервер. Этот метод подходит для балансировки нагрузки протокола TCP без функции куков.", "Permanent": "Постоянный", "Persistent": "Постоянный", "Peru": "<PERSON>ер<PERSON>", "Phase1 Negotiation Mode": "Режим согласования Phase1", "Philippines": "Филиппины", "Phone": "Телефон", "Physical CPU Usage": "Использование физического CPU", "Physical Network": "Физическая сеть", "Physical Node": "Физический узел", "Physical Nodes": "Физические узлы", "Physical Storage Usage": "Использование физического хранилища", "Pitcairn": "Пит<PERSON><PERSON><PERSON>н", "Platform Info": "Информация о платформе", "Please confirm your password!": "Пожалуйста, подтвердите свой пароль!", "Please enter JSON in the correct format!": "Пожалуйста, введите JSON в правильном формате!", "Please enter URL!": "Пожалуйста, введите URL!", "Please enter a correct certificate content, format is refer to the left tip!": "Пожалуйста, введите правильное содержимое сертификата, формат см. в подсказке слева!", "Please enter a correct domain, format is refer to the left tip!": "Пожалуйста, введите правильное доменное имя, формат см. в подсказке слева!", "Please enter a correct private key, format is refer to the left tip!": "Пожалуйста, введите правильный закрытый ключ, формат см. в подсказке слева!", "Please enter a file link starting with \"http://\" or \"https://\"!": "Пожалуйста, введите ссылку на файл, начиная с \"http://\" или \"https://\"!", "Please enter a memory page size, such as: 1024, 1024MiB": "Пожалуйста, введите размер страницы памяти, например: 1024, 1024 МиБ", "Please enter a valid ASCII code": "Пожалуйста, введите действительный код ASCII", "Please enter a valid Email Address!": "Пожалуйста, введите действительный адрес электронной почты!", "Please enter a valid IPv4 value.": "Пожалуйста, введите действительное значение IPv4.", "Please enter a valid IPv6 value.": "Пожалуйста, введите действительное значение IPv6.", "Please enter a valid Phone Number": "Пожалуйста, введите действительный номер телефона", "Please enter complete key value!": "Пожалуйста, введите полное значение ключа!", "Please enter right format custom trait!": "Пожалуйста, введите правильный формат пользовательского атрибута!", "Please enter right format key value!": "Пожалуйста, введите правильное значение ключа!", "Please enter right format memory page value!": "Пожалуйста, введите правильное значение размера страницы памяти!", "Please enter right format trait!": "Пожалуйста, введите правильный формат атрибута!", "Please enter the correct id": "Пожалуйста, введите правильный идентификатор", "Please enter the server id to be reduced, and separate different id with \",\"": "Пожалуйста, введите идентификатор сервера, который необходимо уменьшить, и разделите разные идентификаторы запятой \",\"", "Please fill in the peer network segment and subnet mask of CIDR format, the written subnets should be under the same router, one per line.": "Пожалуйста, заполните сегмент сети пира и маску подсети в формате CIDR, написанные подсети должны быть под одним маршрутизатором, по одной на строку.", "Please input": "Пожалуйста, введите", "Please input <username> or <username>@<domain name>!": "Пожалуйста, введите <имя пользователя> или <имя пользователя>@<имя домена>!", "Please input ICMP code(0-255)": "Пожалуйста, введите код ICMP (0-255)", "Please input ICMP type(0-255)": "Пожалуйста, введите тип ICMP (0-255)", "Please input IPv4 or IPv6 cidr": "Пожалуйста, введите CIDR IPv4 или IPv6", "Please input IPv4 or IPv6 cidr, (e.g. ***********/24, 2001:DB8::/48)": "Пожал<PERSON>йста, введите CIDR IPv4 или IPv6, (например, ***********/24, 2001:DB8::/48)", "Please input a number": "", "Please input a parameter": "", "Please input a valid ip!": "Пожалуйста, введите действительный IP-адрес!", "Please input a value": "", "Please input at least 2 characters.": "Пожалуйста, введите хотя бы 2 символа.", "Please input at least one record": "", "Please input auth key": "Пожалуйста, введите ключ аутентификации", "Please input cipher": "Пожалуйста, введите шифр", "Please input cluster name": "Пожалуйста, введите имя кластера", "Please input cluster template name": "Пожалуйста, введите имя шаблона кластера", "Please input complete data": "Пожалуйста, введите полные данные", "Please input container name": "Пожалуйста, введите имя контейнера", "Please input file name": "Пожалуйста, введите имя файла", "Please input image": "Пожалуйста, введите образ", "Please input ip address": "", "Please input ipv4": "Пожалуйста, введите IPv4", "Please input ipv6": "Пожалуйста, введите IPv6", "Please input key": "Пожалуйста, введите ключ", "Please input key and value": "Пожалуйста, введите ключ и значение", "Please input key size": "Пожалуйста, введите размер ключа", "Please input metadata": "Пожалуйста, введите метаданные", "Please input name": "Пожалуйста, введите имя", "Please input or load Template from a file": "Пожалуйста, введите или загрузите шаблон из файла", "Please input port and protocol": "Пожалуйста, введите порт и протокол", "Please input prefix": "Пожалуйста, введите префикс", "Please input protocol number if it absent in select list.": "Пожалуйста, введите номер протокола, если его нет в списке выбора.", "Please input provider": "Пожалуйста, введите провайдера", "Please input snapshot name": "Пожалуйста, введите имя снимка", "Please input the correct format:  <username> or <username>@<domain name>.": "Пожалуйста, введите правильный формат: <имя пользователя> или <имя пользователя>@<имя домена>.", "Please input transfer id": "Пожалуйста, введите идентификатор передачи", "Please input user name": "Пожалуйста, введите имя пользователя", "Please input value": "Пожалуйста, введите значение", "Please input your Password!": "Пожалуйста, введите ваш пароль!", "Please input your Username!": "Пожалуйста, введите ваше имя пользователя!", "Please input your current password!": "Пожалуйста, введите ваш текущий пароль!", "Please input your password!": "Пожалуйста, введите ваш пароль!", "Please input {label}": "Пожалуйста, введите {label}", "Please input {label}!": "Пожалуйста, введите {label}!", "Please make sure this IP address be available to avoid creating VM failure.": "Пожалуйста, убеди<PERSON>е<PERSON><PERSON>, что этот IP-адрес доступен, чтобы избежать сбоя при создании виртуальной машины.", "Please make sure this IP address be available.": "Пожалуйста, убедитесь, что этот IP-адрес доступен.", "Please note that when deleting a domain, all projects, users, and user groups under the domain will be deleted directly!": "Пожалуйста, обратите внимание, что при удалении домена будут удалены все проекты, пользователи и группы пользователей внутри домена непосредственно!", "Please reasonably plan the network and subnet to which the virtual network card belongs.": "Пожалуйста, разумно планируйте сеть и подсеть, к которой принадлежит виртуальная сетевая карта.", "Please save your token properly and it will be valid for {left}.": "Пожал<PERSON>йста, сохраните ваш токен правильно, и он будет действителен в течение {left}.", "Please select": "Пожалуйста, выберите", "Please select a file": "Пожалуйста, выберите файл", "Please select a file with the suffix {types}": "Пожалуйста, выберите файл с суффиксом {types}", "Please select a network!": "Пожалуйста, выберите сеть!", "Please select a parameter": "", "Please select a subnet!": "Пожалуйста, выберите подсеть!", "Please select a type!": "Пожалуйста, выберите тип!", "Please select availability zone": "Пожалуйста, выберите доступную зону", "Please select image driver": "Пожалуйста, выберите драйвер образа", "Please select item!": "Пожалуйста, выберите элемент!", "Please select login type!": "Пожалуйста, выберите тип входа!", "Please select policy": "Пожалуйста, выберите политику", "Please select source": "Пожалуйста, выберите источник", "Please select type": "Пожалуйста, выберите тип", "Please select volume type": "Пожалуйста, выберите тип диска", "Please select your Region!": "Пожалуйста, выберите ваш регион!", "Please select {label}!": "Пожалуйста, выберите {label}!", "Please select {name} first": "Пожалуйста, сначала выберите {name}", "Please select: {name} or an image file that is the same as it": "Пожалуйста, выберите: {name} или файл изображения, который совпадает с ним", "Please set CPU && Ram first.": "Пожалуйста, сначала установите CPU и RAM.", "Please set MUNA": "Пожалуйста, установите MUNA", "Please set a size no less than {minSize} GiB!": "", "Please set at least one role!": "Пожалуйста, установите хотя бы одну роль!", "Please set the system disk size!": "", "Please upload files smaller than { size }GiB on the page. It is recommended to upload files over { size }GiB using API.": "Пожалуйста, загружайте файлы размером меньше { size } GiB на странице. Рекомендуется загружать файлы размером более { size } GiB с помощью API.", "Pointer Record": "Запись указателя", "Poland": "Польша", "Policy": "Политика", "Policy Detail": "", "Policy Edit": "", "Policy Name": "Имя политики", "Policy Rules": "", "Pool Algorithm": "Алгоритм пула", "Pool Description": "Описание пула", "Pool Detail": "Подробности о пуле", "Pool ID": "Идентификатор пула", "Pool Info": "Информация о пуле", "Pool Name": "Имя пула", "Pool Protocol": "Протокол пула", "Pools": "Пулы", "Port": "Порт", "Port Count": "Количество портов", "Port Detail": "Подробности о порту", "Port Forwardings": "Пересылка портов", "Port Group": "Группа портов", "Port Groups": "Группы портов", "Port ID": "Идентификатор порта", "Port Info": "Информация о порту", "Port Range": "Диа<PERSON>азон портов", "Port Security": "Безопасность порта", "Port Security Enabled": "Включена безопасность порта", "Port Type": "Тип порта", "Ports": "Порты", "Ports are either single values or ranges": "Порты могут быть как одиночными значениями, так и диапазонами", "Ports provide extra communication channels to your containers. You can select ports instead of networks or a mix of both, If the terminal port and the network are selected at the same time, note that the terminal port is not a terminal port of the selected network, and the container under the same network will only be assigned one IP address (The port executes its own security group rules by default).": "Порты обеспечивают дополнительные каналы связи для ваших контейнеров. Вы можете выбирать порты вместо сетей или комбинировать их. Если выбраны терминальный порт и сеть одновременно, обратите внимание, что терминальный порт не является терминальным портом выбранной сети, и контейнер в той же сети будет назначен только один IP-адрес (по умолчанию порт выполняет свои собственные правила группы безопасности).", "Ports provide extra communication channels to your instances. You can select ports instead of networks or a mix of both (The port executes its own security group rules by default).": "Порты обеспечивают дополнительные каналы связи для ваших инстансов. Вы можете выбирать порты вместо сетей или комбинировать их (по умолчанию порт выполняет свои собственные правила группы безопасности).", "Portugal": "Португалия", "Power Off": "Выключить", "Power On": "Включить", "Power State": "Состояние питания", "Powering Off": "Выключение", "Powering On": "Включение", "Pre Live Migration": "Предварительная живая миграция", "Pre-Shared Key must be the same with Confirm Shared Key.": "Общий ключ должен совпадать с подтверждением общего ключа.", "Pre-Shared Key(PSK) String": "Строка общего ключа (PSK)", "Prefer": "Предпочитать", "Prefer(Thread siblings are preferred)": "Предпочитать (предпочтительны потомки потоков)", "Preferred": "Предпочтительный", "Prefix": "Префикс", "Prep Resize": "Подготовить изменение размера", "Prepare Template": "Подготовить шаблон", "Previous": "Предыдущий", "Primary": "Первичный", "Primary is controlled by Designate, Secondary zones are slaved from another DNS Server.": "", "Private": "Частный", "Private Key": "Приват<PERSON><PERSON>й ключ", "Profile": "Профиль", "Progress": "Прогресс", "Project": "Проект", "Project Console": "Консоль проекта", "Project Detail": "Подробности о проекте", "Project ID": "Идентификатор проекта", "Project ID/Name": "Идентификатор проекта/Название", "Project Name": "Название проекта", "Project Num": "Номер проекта", "Project Quota": "Квота проекта", "Project Range": "Диапазон проекта", "Project Scope": "Область проекта", "Project Scope (Project Name: Role Names)": "Область проекта (Название проекта: Названия ролей)", "Project User Groups": "Группы пользователей проекта", "Project Users": "Пользователи проекта", "Projects": "Проекты", "Promote": "Повысить", "Properties": "Свойства", "Protected": "Защищенный", "Protocol": "Протокол", "Protocol Type": "Тип протокола", "Provider": "Поставщик", "Provider Network Type": "Тип сети поставщика", "Provider Physical Network": "Физическая сеть поставщика", "Provision State": "Состояние Предоставления", "Provisioning Status": "", "Public": "Публичный", "Public Access": "Публичный доступ", "Public Address": "Публичный адрес", "Public Images": "Публичные образы", "Public Key": "Публичный ключ", "Published In": "Опубликовано в", "Published Out": "Опубликовано наружу", "Puerto Rico": "Пуэрто-Рико", "QCOW2 - QEMU image format": "QCOW2 - формат образа QEMU", "Qatar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "QoS Bandwidth Egress Limit": "Лимит исходящей пропускной способности QoS", "QoS Bandwidth Ingress Limit": "Лимит входящей пропускной способности QoS", "QoS Bandwidth Limit": "Лимит пропускной способности QoS", "QoS Detail": "Дета<PERSON>и QoS", "QoS Policies": "Политики QoS", "QoS Policy": "Политика QoS", "QoS Policy Detail": "Детали политики QoS", "QoS Policy ID": "Идентификатор политики QoS", "QoS Policy ID/Name": "Идентификатор/Название политики QoS", "QoS Spec": "Спецификация QoS", "QoS Spec ID": "Идентификатор спецификации QoS", "QoS Specs": "Спецификации QoS", "QoS policies": "Политики QoS", "Qos Policy": "Политика QoS", "Queued": "В очереди", "Queued To Apply": "В очереди для применения", "Queued To Deny": "В очереди для отклонения", "Quota": "Квота", "Quota Overview": "Обзор квот", "Quota exceeded": "Превышение квоты", "Quota is not enough for extend share.": "Квоты недостаточно для расширения общего доступа.", "Quota is not enough for extend volume.": "Квоты недостаточно для расширения объема.", "Quota of key pair means: the number of allowed key pairs for each user.": "Квота на ключевые пары означает: количество разрешенных ключевых пар для каждого пользователя.", "Quota: Insufficient quota to create resources, please adjust resource quantity or quota(left { quota }, input { input }).": "Квота: Исчерпана квота для создания ресурсов, пожалуйста, откорректируйте количество ресурсов или квоту (осталось { quota }, ввод { input }).", "Quota: Insufficient { name } quota to create resources, please adjust resource quantity or quota(left { left }, input { input }).": "Квота: Исчерпана квота { name } для создания ресурсов, пожалуйста, откорректируйте количество ресурсов или квоту (осталось { left }, ввод { input }).", "Quota: Insufficient { name } quota to create resources.": "Квота: Исчерпана квота { name } для создания ресурсов.", "Quota: Project quotas sufficient resources can be created": "Квота: Исчерпана квота проекта для создания ресурсов", "RAM": "ОЗУ", "RAM (MiB)": "ОЗУ (МиБ)", "RAW - Raw disk image format": "RAW", "RBAC Policies": "", "RBAC Policy Detail": "", "REJECT": "", "RESTORE COMPLETE": "ВОССТАНОВЛЕНИЕ ЗАВЕРШЕНО", "RESUME COMPLETE": "ВОЗОБНОВЛЕНИЕ ЗАВЕРШЕНО", "RESUME FAILED": "ВОЗОБНОВЛЕНИЕ НЕ УДАЛОСЬ", "ROLLBACK COMPLETE": "ОТКАТ ЗАВЕРШЕН", "ROLLBACK FAILED": "ОТКАТ НЕ УДАЛСЯ", "ROLLBACK IN PROGRESS": "ОТКАТ В ПРОЦЕССЕ", "ROUND_ROBIN": "ROUND_ROBIN", "RSVP": "RSVP", "Raid Interface": "Интерфейс RAID", "Ram Size (GiB)": "Размер ОЗУ (ГиБ)", "Ram value is { ram }, NUMA RAM value is { totalRam }, need to be equal. ": "Значение ОЗУ равно { ram }, значение ОЗУ NUMA равно { totalRam }, должны быть равны.", "Ramdisk ID": "Идентификатор дискеты ОЗУ", "Ramdisk Image": "Образ дискеты ОЗУ", "Rbac Policy": "", "Read and write": "Чтение и запись", "Read only": "Только чтение", "Real Name": "Фактическое имя", "Reason": "Причина", "Reason: ": "Причина: ", "Reboot": "Перезагрузка", "Reboot Container": "Перезагрузка контейнера", "Reboot Database Instance": "Перезагрузка инстанса базы данных", "Reboot Instance": "Перезагрузка инстанса", "Rebooting": "Перезагрузка", "Rebuild": "Пересборка", "Rebuild Block Device Mapping": "Пересборка отображения блочных устройств", "Rebuild Container": "Пересборка контейнера", "Rebuild Instance": "Пересборка инстанса", "Rebuild Spawning": "Пересборка в процессе создания", "Rebuilding": "Пересборка", "Rebuilt": "Пересобран", "Recently a day": "Недавно за день", "Record Sets": "Наборы записей", "Records": "Записи", "Recordset Detail": "Подробности набора записей", "Recordsets Detail": "Подробности наборов записей", "Recover": "Восстановить", "Recovering": "Восстановление", "Recovery Method": "", "Recycle Bin": "Корзина", "Region": "Регион", "Registry Enabled": "Реестр включен", "Related Policy": "", "Related Resources": "Связанные ресурсы", "Release": "Освободить", "Release Fixed IP": "Освободить фиксированный IP", "Remote Group Id": "Идентификатор удаленной группы", "Remote IP Prefix": "Префикс удаленного IP", "Remote Security Group": "Удаленная группа безопасности", "Remote Type": "Удаленный тип", "Remove": "Удалить", "Remove Default Project": "", "Remove Network": "Удалить сеть", "Remove Router": "Удалить маршрутизатор", "Remove Rule": "", "Remove default project for user": "", "Rename": "Переименовать", "Rename is to copy the current file to the new file address and delete the current file, which will affect the creation time of the file.": "Переименование - это копирование текущего файла по новому адресу файла и удаление текущего файла, что повлияет на время создания файла.", "Replication Change": "Изменение репликации", "Report Count": "Количество отчетов", "Republic of the Congo": "Республика Конго", "Request ID": "Идентификатор запроса", "Require": "Требовать", "Require(Need multithreading)": "Требуется (Требуется многозадачность)", "Required Data Disk": "Обязательный дисковый накопитель", "Rescue": "Спасение", "Rescued": "Спасено", "Rescuing": "Спасение в процессе", "Reserved": "Зарезервировано", "Reset Status": "Сбросить статус", "Reset To Initial Value": "Сбросить до начального значения", "Reset failed, please retry": "Сброс не удался, пожалуйста, попробуйте еще раз", "Resize": "Изменить размер", "Resize Cluster": "Изменить размер кластера", "Resize Instance": "Изменить размер инстанса", "Resize Volume": "Изменить размер диска", "Resized": "Изменено", "Resizing or Migrating": "Изменение размера или миграция", "Resource": "Ресурс", "Resource Class": "Класс ресурса", "Resource Class Properties": "Свойства класса ресурса", "Resource Id": "Идентификатор ресурса", "Resource Not Found": "Ресурс не найден", "Resource Pool": "Ресурсный пул", "Resource Status": "Статус ресурса", "Resource Status Reason": "Причина статуса ресурса", "Resource Type": "Тип ресурса", "Resource Types": "Типы ресурсов", "Resources Synced": "Синхронизированные ресурсы", "Resource Monitor": "Монито<PERSON> ресурсов", "Restart": "Перезапуск", "Restart Container": "Перезапуск контейнера", "Restart Database Service": "Перезапуск службы базы данных", "Restarting": "Перезапуск", "Restore Backup": "Восстановление из резервной копии", "Restore From Snapshot": "Восстановление из снимка", "Restore backup": "Восстановление из резервной копии", "Restore from snapshot": "Восстановление из снимка", "Restoring": "Восстановление", "Restoring Backup": "Восстановление из резервной копии", "Restricted": "Ограниченный", "Restricted Situation": "Ограниченная ситуация", "Resume": "Возобновить", "Resume Complete": "Возобновление завершено", "Resume Failed": "Ошибка возобновления", "Resume In Progress": "Возобновление в процессе", "Resume Instance": "Возобновление инстанса", "Resuming": "Возобновление", "Retry times for restart on failure policy": "Количество повторных попыток перезапуска по политике при сбое", "Retyping": "Изменение типа", "Reunion": "Реюнион", "Reverse DNS Detail": "Подробности обратного DNS", "Reverse Detail": "Обратная детализация", "Reverse Dns": "Обратное DNS", "Revert Resize or Migrate": "Откат изменения размера или миграции", "Revert Resize/Migrate": "Откат изменения размера/миграции", "Reverting": "Откат", "Reverting Error": "Ошибка отката", "Reverting Resize or Migrate": "Откат изменения размера или миграции", "Role": "Роль", "Role Detail": "Подробности роли", "Role Name": "Название роли", "Roles": "Роли", "Rollback Complete": "Откат завершен", "Rollback Failed": "Ошибка отката", "Rollback In Progress": "Откат в процессе", "Romania": "Румыния", "Root Disk": "Корневой диск", "Root Disk (GiB)": "Корневой диск (ГиБ)", "Root directory": "Корневой каталог", "Router": "Мар<PERSON>рутизатор", "Router Advertisements Mode": "Режим рекламы маршрутизатора", "Router Detail": "Подробности маршрутизатора", "Router External": "Внешний маршрутизатор", "Router ID": "Идентификатор маршрутизатора", "Router Port": "", "Routers": "Маршрутизаторы", "Rule": "", "Rule Action": "", "Rule Detail": "", "Rule Edit": "", "Rule Numbers": "Номера правил", "Rules": "Правила", "Rules Number": "Количество правил", "Running": "Запущено", "Running Threads": "Запущенные потоки", "Running Time": "Время работы", "Runtime": "Время выполнения", "Russia": "Россия", "Rwanda": "Руанда", "SCTP": "SCTP", "SNAPSHOT COMPLETE": "СНЯТИЕ СНИМКА ЗАВЕРШЕНО", "SNAT Enabled": "SNAT включен", "SNI Certificate": "Сертификат SNI", "SNI Enabled": "SNI включен", "SOURCE_IP": "Исходный IP", "SSH Public Key Fingerprint": "Отпечаток открытого ключа SSH", "SSL Parsing Method": "Метод разбора SSL", "Saint Vincent and the Grenadines": "Сент-Винсент и Гренадины", "Same subnet with LB": "Та же подсеть, что и у балансировщика нагрузки", "Samoa": "Самоа", "San Marino": "Сан-Марино", "Sao Tome and Principe": "Сан-Томе и Принсипи", "Saudi Arabia": "Саудовская Аравия", "Saving": "Сохранение", "Scheduler Hints": "Советы планировщика", "Scheduling": "Планирование", "Search": "Поиск", "Sec for DPD delay, > 0": "Сек для задержки DPD, > 0", "Sec for DPD timeout, > 0 & > DPD Interval": "Сек для тайм-аута DPD, > 0 & > Интервал DPD", "Secondary": "Вторичный", "Security Group": "Группа безопасности", "Security Group Detail": "Подробности группы безопасности", "Security Group Info": "Информация о группе безопасности", "Security Group Num:": "Номер группы безопасности:", "Security Group Rule": "Правило группы безопасности", "Security Group Rules": "Правила группы безопасности", "Security Groups": "Группы безопасности", "Security Groups Adding": "Добавление групп безопасности", "Security Groups Removing": "Удаление групп безопасности", "Security Info": "Информация о безопасности", "Segment Detail": "", "Segment ID": "", "Segment Name": "", "Segmentation ID": "Идентификатор сегментации", "Segmentation Id": "Идентификатор сегментации", "Segments": "", "Select File": "Выберите файл", "Select Project": "Выберите проект", "Select Project Role": "Выберите роль проекта", "Select User Group": "Выберите группу пользователей", "Select Volume Snapshot": "Выберите снимок диска", "Select a QoS Policy": "", "Select a login type": "Выберите тип входа", "Select a network": "", "Select a project": "", "Select a region": "Выберите регион", "Select an object type": "", "Selected": "Выб<PERSON><PERSON>н", "Selected Members": "Выбранные участники", "Selected list": "Выбранный список", "Sender Policy Framework": "Фреймворк политики отправителя", "Senegal": "Сенегал", "Serbia": "Сербия", "Serial": "Последовательный", "Server Certificate": "Сертификат сервера", "Server Certificates": "Сертификаты сервера", "Server Group": "Группа сервера", "Server Group Detail": "Подробности группы сервера", "Server Group Member": "Участник группы сервера", "Server Groups": "Группы сервера", "Server Status": "Статус сервера", "Server Type": "Тип сервера", "Service": "Сервис", "Service List": "Список сервисов", "Service Locator": "Локатор сервиса", "Service Port ID": "Идентификатор порта сервиса", "Service State": "Состояние сервиса", "Service Status": "Статус сервиса", "Service Status Updated": "Обновление статуса сервиса", "Service Type": "", "Service Unavailable (code: 503) ": "Сервис недоступен (код: 503)", "Services": "Сервисы", "Set": "Установить", "Set Admin Password": "Установить пароль администратора", "Set Boot Device": "Установить устройство загрузки", "Set Default Project": "", "Set Domain Name PTR": "Установить PTR для доменного имени", "Set IP": "Установить IP", "Set default project for user": "", "Seychelles": "Сейшелы", "Share": "Общий доступ", "Share Capacity (GiB)": "Объем общего доступа (ГиБ)", "Share Detail": "Подробности общего доступа", "Share File Storage": "Общее файловое хранилище", "Share Group": "Группа общего доступа", "Share Group Detail": "Подробности группы общего доступа", "Share Group Type": "Тип группы общего доступа", "Share Group Type Detail": "Подробности типа группы общего доступа", "Share Group Types": "Типы групп общего доступа", "Share Groups": "Группы общего доступа", "Share Id": "Идентификатор общего доступа", "Share Instance": "инстанс общего доступа", "Share Instance Detail": "Подробности инстанса общего доступа", "Share Instances": "инстансы общего доступа", "Share Network": "Сеть общего доступа", "Share Network Detail": "Подробности сети общего доступа", "Share Network Subnet": "Подсеть сети общего доступа", "Share Network Subnets": "Подсети сети общего доступа", "Share Networks": "Сети общего доступа", "Share Protocol": "Протокол общего доступа", "Share Replica ID": "Идентификатор реплики общего доступа", "Share Server": "Сервер общего доступа", "Share Server Detail": "Подробности сервера общего доступа", "Share Servers": "Серверы общего доступа", "Share Type": "Тип общего доступа", "Share Type Detail": "Подробности типа общего доступа", "Share Type ID": "Идентификатор типа общего доступа", "Share Type Name": "Имя типа общего доступа", "Share Types": "Типы общего доступа", "Shared": "Общий", "Shared Images": "Общие образы", "Shared Network": "Общая сеть", "Shared Networks": "Общие сети", "Shared QoS Policies": "Общие политики QoS", "Shared QoS Policy": "", "Shared policy only can insert shared rules.": "", "Shares": "Общий доступ", "Shelve": "Архивировать", "Shelve Instance": "Архивировать инстанс", "Shelved": "В архиве", "Shelved Offloaded": "Архивирован и выгружен", "Shelving": "Архивируется", "Shelving Image Pending Upload": "Архивируемый образ ожидает загрузки", "Shelving Image Uploading": "Загрузка архивируемого образа", "Shelving Offloading": "Выгрузка архива", "Show All Domain": "Показать все домены", "Show Instance": "Показать инстанс", "Show all Data": "Показать все данные", "Shrinking": "Уменьшение", "Shrinking Error": "Ошибка уменьшения", "Shrinking Possible Data Loss Error": "Уменьшение может привести к потере данных", "Shut Down": "Выключить", "Shut Off": "Выключить", "Shutoff": "Выключен", "Sierra Leone": "Сьерра-Леоне", "Sign Out": "Выход", "Sign up": "Регистрация", "Signal to send to the container: integer or string like SIGINT. When not set, SIGKILL is set as default value and the container will exit. The supported signals varies between platform. Besides, you can omit \"SIG\" prefix.": "Сигнал для отправки контейнеру: целое число или строка вида SIGINT. Если не установлено, устанавливается значение SIGKILL по умолчанию, и контейнер будет завершен. Поддерживаемые сигналы могут различаться в зависимости от платформы. Кроме того, вы можете опустить префикс \"SIG\".", "Singapore": "Синга<PERSON>ур", "Size": "Размер", "Size (GiB)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ГиБ)", "Slovakia (Slovak Republic)": "Словакия (Словацкая Республика)", "Slovenia": "Словения", "Slow Query": "Медленный запрос", "Small": "Маленький", "Small(Not recommended)": "Маленький (не рекомендуется)", "Smart Scheduling": "Интеллектуальное планирование", "Snapshot Complete": "Снимок завершен", "Snapshot Failed": "Снимок не удался", "Snapshot In Progress": "Снимок в процессе", "Snapshot Instance": "Снимок инстанса", "Snapshot Source": "Источник снимка", "Snapshots can be converted into volume and used to create an instance from the volume.": "Снимки можно преобразовать в диск и использовать для создания инстанса из диска.", "Snapshotting": "Создание снимка", "Soft Delete Instance": "Мягкое удаление инстанса", "Soft Deleted": "Мягко удален", "Soft Deleting": "Мягкое удаление", "Soft Power Off": "Мягкое выключение", "Soft Reboot": "Мягкая перезагрузка", "Soft Reboot Instance": "Мягкая перезагрузка инстанса", "Soft Rebooting": "Мягкая перезагрузка", "Soft-Affinity": "Мягкая аффинность", "Soft-Anti-Affinity": "Мягкая анти-аффинность", "Solomon Islands": "Соломоновы острова", "Somalia": "Сомали", "Sorry, the page you visited does not exist.": "Извините, посещенная вами страница не существует", "Source": "Источник", "Source IP": "", "Source IP Address/Subnet": "", "Source Path: {path}": "Исходный путь: {path}", "Source Port": "", "Source Port/Port Range": "Исходный порт/диапазон портов", "South Africa": "Южная Африка", "South Korea": "Южная Корея", "Spain": "Испания", "Spawning": "Создание", "Spec": "Спецификация", "Specification": "Спецификация", "Specify Physical Node": "Указать физический узел", "Specify mount point.": "Укажите точку монтирования.", "Specify the client IP address": "", "Specify the listener port": "", "Specify whether future replicated instances will be created on the same hypervisor (affinity) or on different hypervisors (anti-affinity). This value is ignored if the instance to be launched is a replica.": "Укажите, будут ли в будущем создаваться реплицированные инстансы на диск же гипервизоре (аффинность) или на разных гипервизорах (анти-аффинность). Это значение игнорируется, если запускаемый инстанс - это реплика.", "Specs": "Характеристики", "Sri Lanka": "Шри-Ланка", "Stack": "Стек", "Stack Detail": "Подробности стека", "Stack Events": "События стека", "Stack Faults": "Неисправности стека", "Stack ID": "Идентифика<PERSON>ор стека", "Stack Name": "Имя стека", "Stack Resource": "Ресурс стека", "Stack Resource Type": "Тип ресурса стека", "Stack Resources": "Ресурсы стека", "Stack Status": "Статус стека", "Stacks": "Стеки", "Stand Alone Ports Supported": "Поддержка отдельных портов", "Standard Trait": "Стандартный атрибут", "Start": "Запустить", "Start Container": "Запустить контейнер", "Start Instance": "Запустить инстанс", "Start Of Authority": "Стартовая точка авторитета", "Start Source": "Источник запуска", "Start Source Name": "Имя источника запуска", "Start Time": "Время запуска", "Start auto refreshing data": "Запустить автообновление данных", "Start refreshing data every {num} seconds": "Запустить обновление данных каждые {num} секунд", "Started At": "Запущено", "Startup Parameters": "Параметры запуска", "State": "Состояние", "Static Routes": "Статические маршруты", "Stats Information": "Информация о статистике", "Status": "Статус", "Status Code": "Код статуса", "Status Detail": "Детали статуса", "Status Reason": "Причина статуса", "Stop": "Остановить", "Stop Container": "Остановить контейнер", "Stop Database Service": "Остановить службу базы данных", "Stop Instance": "Остановить инстанс", "Stop auto refreshing data": "Остановить автообновление данных", "Stop refreshing data every {num} seconds": "Остановить обновление данных каждые {num} секунд", "Stopped": "Остановлено", "Storage": "Храни<PERSON><PERSON><PERSON>е", "Storage Backends": "Задние хранилища", "Storage Capacity(GiB)": "Емкость хранилища (ГиБ)", "Storage Cluster Bandwidth": "Пропускная способность кластера хранилища", "Storage Cluster IOPS": "IOPS кластера хранилища", "Storage Cluster OSD Latency": "Задержка OSD кластера хранилища", "Storage Cluster Status": "Статус кластера хранилища", "Storage Cluster Usage": "Использование кластера хранилища", "Storage Clusters": "Кластеры хранилища", "Storage IOPS": "IOPS хранилища", "Storage Interface": "Интерфейс хранилища", "Storage Policy": "Политика хранения", "Storage Pool Capacity Usage": "Использование емкости пула хранилища", "Storage Types": "Типы хранилищ", "Sub Users": "Дочерние пользователи", "Subnet": "Подсеть", "Subnet Count": "Количество подсетей", "Subnet Detail": "Подробности подсети", "Subnet ID": "Идентификатор подсети", "Subnet ID/Name": "Идентификатор/Имя подсети", "Subnet Name": "Имя подсети", "Subnets": "Подсети", "Subordinate Projects": "Дочерние проекты", "Subordinate User Groups": "Дочерние группы пользователей", "Succeeded": "Успешно", "Success": "Успех", "Sudan": "Судан", "Supports resumable transfer (recommended when uploading a large image)": "Поддержка продолжения точки остревания (рекомендуется при загрузке больших изображений)", "Suriname": "Су<PERSON><PERSON><PERSON><PERSON>", "Suspend": "Приостановить", "Suspend Complete": "Приостановка завершена", "Suspend Failed": "Ошибка при приостановке", "Suspend In Progress": "Приостановка выполняется", "Suspend Instance": "Приостановить инстанс", "Suspended": "Приостановлено", "Suspending": "Приостанавливается", "Swaziland": "Свазиленд", "Sweden": "Швеция", "Switch ID": "Идентификатор переключателя", "Switch Info": "Информация о переключателе", "Switch Language": "Изменить язык", "Switch Project": "Переключить проект", "Switzerland": "Швейцария", "Syncing": "Синхронизация", "Syrian Arab Republic": "Сирийская Арабская Республика", "System": "Система", "System Config": "Конфигурация системы", "System Disk": "Системный диск", "System Info": "Информация о системе", "System Load": "Загрузка системы", "System Roles": "Роли системы", "System Running Time": "Время работы системы", "System is error, please try again later.": "Система в состоянии ошибки, пожалуйста, попробуйте позже.", "TCP": "TCP", "TCP Connections": "TCP-соединения", "TLS Disabled": "TLS отключен", "TTL": "TTL", "TTL (Time to Live) for the zone.": "", "Tag is no longer than 60 characters": "Тег не длиннее 60 символов", "Tags": "Теги", "Tags Info": "Информация о тегах", "Tags are not case sensitive": "Теги не чувствительны к регистру", "Taiwan": "Тайвань", "Tajikistan": "Тад<PERSON><PERSON><PERSON>истан", "Take effect after restart": "Вступает в силу после перезапуска", "Tanzania": "Танзания", "Target Compute Host": "Целевой хост вычислений", "Target IP Address": "Целевой IP-адрес", "Target Port": "Целевой порт", "Target Project": "", "Target Project ID": "", "Target Project ID/Name": "", "Target Project Name": "", "Target Storage Backend": "Целевой бэкенд хранилища", "Target Tenant": "", "Task State": "Состояние задачи", "Template Content": "Содержание шаблона", "Template Name": "Имя шаблона", "Text Record": "Текстовая запись", "Thailand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "That is, after how many consecutive failures of the health check, the health check status of the back-end cloud server is changed from normal to abnormal": "То есть, после скольких последовательных неудачных проверок состояния, статус проверки состояния облачного сервера изменяется с нормального на аномальное", "The DNS nameserver to use for this cluster template": "DNS-сервер, который будет использоваться для этого шаблона кластера", "The Federation of Saint Kitts and Nevis": "Федерация Сент-Китс и Невис", "The Provider is the encryption provider format (e.g. \"luks\")": "Поставщик - это формат поставщика шифрования (например, \"luks\")", "The Republic of Macedonia": "Республика Македония", "The Republic of South Sudan": "Республика Южный Судан", "The SSH key is a way to remotely log in to the cluster instance. If it’s not set, the value of this in the template will be used.": "SSH-ключ - это способ удаленного входа в инстанс кластера. Если не задан, будет использовано значение из этого шаблона.", "The SSH key is a way to remotely log in to the cluster instance. The cloud platform only helps to keep the public key. Please keep your private key properly.": "SSH-ключ - это способ удаленного входа в инстанс кластера. Облачная платформа помогает хранить только открытый ключ. Пожалуйста, храните свой закрытый ключ правильно.", "The SSH key is a way to remotely log in to the instance. The cloud platform only helps to keep the public key. Please keep your private key properly.": "SSH-ключ - это способ удаленного входа в инстанс. Облачная платформа помогает хранить только открытый ключ. Пожалуйста, храните свой закрытый ключ правильно.", "The amphora instance is required for load balancing service setup and is not recommended": "инстанс амфоры необходим для настройки службы балансировки нагрузки и не рекомендуется", "The associated floating IP, virtual adapter, volume and other resources will be automatically disassociated.": "Связанный плавающий IP-адрес, виртуальный адаптер, диск и другие ресурсы будут автоматически отвязаны.", "The certificate contains information such as the public key and signature of the certificate. The extension of the certificate is \"pem\" or \"crt\", you can directly enter certificate content or upload certificate file.": "Сертификат содержит информацию, такую как открытый ключ и подпись сертификата. Расширение сертификата - \"pem\" или \"crt\", вы можете ввести содержание сертификата напрямую или загрузить файл сертификата.", "The changed node count can not be equal to the current value": "Количество измененных узлов не может быть равным текущему значению", "The command to execute": "Команда для выполнения", "The container memory size in MiB": "Размер памяти контейнера в MiB", "The container runtime tool to create container with": "Инструмент для запуска контейнера", "The creation instruction has been issued, please refresh to see the actual situation in the list.": "Инструкция по созданию была выдана, пожалуйста, обновите, чтобы увидеть актуальное состояние в списке.", "The creation instruction was issued successfully, instance: {name}. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.": "Инструкция по созданию была успешно выдана, инстанс: {name}. \n Вы можете подождать несколько секунд, чтобы следить за изменениями в данных списка, или вручную обновить данные, чтобы получить окончательный результат отображения.", "The current operation requires the instance to be shut down:": "Текущая операция требует выключения инстанса:", "The current platform has not yet enabled the {name} management module. Please contact the administrator to enable it": "Текущая платформа еще не включила модуль управления {name}. Вы, администратор, ваш администратор", "The description can be up to 255 characters long.": "Описание может содержать до 255 символов.", "The disk size in GiB for per container": "Размер диска в GiB для каждого контейнера", "The domain name can only be composed of letters, numbers, dashes, in A dash cannot be at the beginning or end, and a single string cannot exceed more than 63 characters, separated by dots; At most can support 30 domain names, separated by commas;The length of a single domain name does not exceed 100 characters, and the total length degree does not exceed 1024 characters.": "Имя домена может состоять только из букв, цифр и дефисов. Дефис не может находиться в начале или конце имени, и длина одной строки не может превышать 63 символа, разделяется точками; Максимально поддерживается 30 имен доменов, разделенных запятыми; Длина одного имени домена не должна превышать 100 символов, а общая длина - 1024 символа.", "The entire inspection process takes 5 to 10 minutes, so you need to be patient. After the registration is completed, the node configuration status will return to the manageable status.": "Весь процесс проверки займет от 5 до 10 минут, поэтому вам нужно быть терпеливыми. После завершения регистрации статус конфигурации узла вернется в управляемое состояние.", "The entrypoint which overwrites the default ENTRYPOINT of the image": "Точка входа, которая перезаписывает стандартную ENTRYPOINT образа", "The feasible configuration of cloud-init or cloudbase-init service in the image is not synced to image's properties, so the Login Name is unknown.": "Возможная конфигурация службы cloud-init или cloudbase-init в образе не синхронизируется с свойствами образа, поэтому имя для входа неизвестно.", "The file with the same name will be overwritten.": "Файл с таким же именем будет перезаписан.", "The floating IP configured with port forwardings cannot be bound": "Плавающий IP, настроенный с перенаправлением портов, не может быть привязан", "The format of the certificate content is: by \"----BEGIN CERTIFICATE-----\" as the beginning,\"-----END CERTIFICATE----\" as the end, 64 characters per line, the last line does not exceed 64 characters, and there cannot be blank lines.": "Формат содержимого сертификата следующий: начинается с \"----BEGIN CERTIFICATE-----\", заканчивается на \"-----END CERTIFICATE----\", по 64 символа в строке, последняя строка не должна превышать 64 символа, и не может быть пустых строк.", "The host name of this container": "", "The http_proxy address to use for nodes in cluster": "Адрес http_proxy для использования на узлах в кластере", "The https_proxy address to use for nodes in cluster": "Адрес https_proxy для использования на узлах в кластере", "The image is not existed": "Образ не существует", "The instance architecture diagram mainly shows the overall architecture composition of the instance. If you need to view the network topology of the instance, please go to: ": "Диаграмма архитектуры инстанса в основном показывает общую архитектурную композицию инстанса. Если вам нужно просмотреть сетевую топологию инстанса, перейдите по ссылке:", "The instance deleted immediately cannot be restored": "инстан<PERSON>, удаленный немедленно, не может быть восстановлен", "The instance has been locked. If you want to do more, please unlock it first.": "инстанс заблокирован. Если вы хотите выполнить дополнительные действия, пожалуйста, сначала разблокируйте его.", "The instance is not shut down, unable to restore.": "инстанс не выключен, восстановление невозможно.", "The instance which is boot from volume will create snapshots for each mounted volumes.": "инстанс, загруженный с диска, будет создавать снимки для каждого подключенного диска.", "The instances in the affinity group are allocated to the same physical machine as much as possible, and when there are no more physical machines to allocate, the normal allocation strategy is returned.": "инстансы в группе схожести распределяются на столько же, насколько это возможно, на один и тот же физический компьютер, и когда больше нет физических компьютеров для выделения, используется нормальная стратегия выделения.", "The instances in the affinity group are strictly allocated to the same physical machine. When there are no more physical machines to allocate, the allocation fails.": "инстансы в группе схожести строго выделяются на один и тот же физический компьютер. Когда больше нет физических компьютеров для выделения, выделение завершается неудачей.", "The instances in the anti-affinity group are allocated to different physical machines as much as possible. When there are no more physical machines to allocate, the normal allocation strategy is returned.": "инстансы в группе антисхожести распределяются на столько же, насколько это возможно, на разные физические компьютеры, и когда больше нет физических компьютеров для выделения, используется нормальная стратегия выделения.", "The instances in the anti-affinity group are strictly allocated to different physical machines. When there are no more physical machines to allocate, the allocation fails.": "инстансы в группе антисхожести строго выделяются на разные физические компьютеры. Когда больше нет физических компьютеров для выделения, выделение завершается неудачей.", "The ip address {ip} is duplicated, please modify it.": "", "The ip is not within the allocated pool!": "IP-адрес не находится в выделенном пуле!", "The ip of external members can be any, including the public network ip.": "IP-адрес внешних участников может быть любым, включая общеоступные сетевые IP-адреса.", "The key pair allows you to SSH into your newly created instance. You can select an existing key pair, import a key pair, or generate a new key pair.": "Ключевая пара позволяет вам подключаться по SSH к вашему только что созданному инстансу. Вы можете выбрать существующую пару ключей, импортировать пару ключей или создать новую пару ключей.", "The kill signal to send": "Сигнал убийства для отправки", "The limit of cluster instance greater than or equal to 1.": "Ограничение на количество инстансов в кластере должно быть больше или равно 1.", "The maximum batch size is {size}, that is, the size of the port range cannot exceed {size}.": "Максимальный размер пакета составляет {size}, то есть размер диапазона портов не может превышать {size}.", "The maximum transmission unit (MTU) value to address fragmentation. Minimum value is 68 for IPv4, and 1280 for IPv6.": "Максимальное значение единицы передачи данных (MTU), предназначенное для устранения фрагментации. Минимальное значение составляет 68 для IPv4 и 1280 для IPv6.", "The min size is {size} GiB": "Минимальный размер составляет {size} GiB", "The name of the physical network to which a port is connected": "Имя физической сети, к которой подключен порт", "The name should be end with \".\"": "", "The name should contain letter or number, the length is 1 to 16, characters can only contain \"0-9, a-z, A-Z, -, _.\"": "Имя должно содержать буквы или цифры, длина от 1 до 16 символов, символы могут содержать только \"0-9, a-z, A-Z, -, _.\"", "The name should contain letter or number, the length is 2 to 64, characters can only contain \"0-9, a-z, A-Z, -, _.\"": "Имя должно содержать буквы или цифры, длина от 2 до 64 символов, символы могут содержать только \"0-9, a-z, A-Z, -, _.\"", "The name should start with letter or number, and be a string of 2 to 255, characters can only contain \"0-9, a-z, A-Z, -, _, .\"": "", "The name should start with upper letter or lower letter, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].:^\".": "", "The name should start with upper letter or lower letter, characters can only contain \"0-9, a-z, A-Z, -, _, .\"": "Имя должно начинаться с заглавной буквы или строчной буквы, символы могут содержать только \"0-9, a-z, A-Z, -, _, .\"", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].\".": "Имя должно начинаться с заглавной буквы, строчной буквы или китайского символа и состоять из 1 до 128 символов, символы могут содержать только \"0-9, a-z, A-Z, \"-'_()[].\".", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].:^\".": "Имя должно начинаться с заглавной буквы, строчной буквы или китайского символа и состоять из 1 до 128 символов, символы могут содержать только \"0-9, a-z, A-Z, \"-'_()[].:^\".", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_.\".": "Имя должно начинаться с заглавной буквы, строчной буквы или китайского символа и состоять из 1 до 128 символов, символы могут содержать только \"0-9, a-z, A-Z, \"-'_.\".", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 64, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].^\".": "Имя должно начинаться с заглавной буквы, строчной буквы или китайского символа и состоять из 1 до 64 символов, символы могут содержать только \"0-9, a-z, A-Z, \"-'_()[].^\".", "The name should start with upper letter, lower letter or chinese, and be a string of 3 to 63, characters can only contain \"0-9, a-z, A-Z, chinese, -, .\".": "Имя должно начинаться с заглавной буквы, строчной буквы или китайского символа и состоять из 3 до 63 символов, символы могут содержать только \"0-9, a-z, A-Z, китайские символы, -, .\".", "The name should start with upper letter, lower letter, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, -, _\".": "Имя должно начинаться с заглавной буквы, строчной буквы и состоять из 1 до 128 символов, символы могут содержать только \"0-9, a-z, A-Z, -, _\".", "The name should start with upper letter, lower letter, and be a string of 2 to 255, characters can only contain \"0-9, a-z, A-Z, -, ., _\".": "Имя должно начинаться с заглавной буквы, строчной буквы и состоять из 2 до 255 символов, символы могут содержать только \"0-9, a-z, A-Z, -, ., _\".", "The name should start with upper letter, lower letter, and be a string of 3 to 63, characters can only contain \"0-9, a-z, A-Z, -\".": "Имя должно начинаться с заглавной буквы, строчной буквы и состоять из 3 до 63 символов, символы могут содержать только \"0-9, a-z, A-Z, -\".", "The new password cannot be identical to the current password.": "Новый пароль не может быть идентичным текущему паролю.", "The no_proxy address to use for nodes in cluster": "Адрес no_proxy для использования на узлах в кластере", "The number of allowed key pairs for each user.": "Количество разрешенных пар ключей для каждого пользователя.", "The number of vCPU cores should not exceed the maximum number of CPU cores of the physical node. Otherwise it will cause fail to schedule to any physical node when creating instance.": "Количество виртуальных ядер vCPU не должно превышать максимальное количество физических ядер CPU на физическом узле. В противном случае это может вызвать сбой в расписании на любом физическом узле при создании инстанса.", "The number of virtual cpu for this container": "Количество виртуальных CPU для этого контейнера", "The optional headers to insert into the request before it is sent to the backend member.": "", "The password must not be the same as the previous": "Пароль не должен совпадать с предыдущим", "The password must not be the same as the previous two": "Пароль не должен совпадать с предыдущими двумя", "The password must not be the same as the previous {num}": "Пароль не должен совпадать с предыдущими {num}", "The port created here will be automatically deleted when detach. If you need a reusable port, please go to the Virtual Adapter page to create and attach the port to instance.": "Порт, созданный здесь, будет автоматически удален при отсоединении. Если вам нужен повторно используемый порт, перейдите на страницу Виртуального адаптера, чтобы создать порт и прикрепить его к инстансу.", "The private key content format is: with \"-----BEGIN RSA PRIVATE KEY-----\" as the beginning,\"-----END RSA PRIVATE KEY-----\" as the end, 64 characters per line, the last line does not exceed 64 characters, and there cannot be blank lines.": "Формат содержимого закрытого ключа: с \"-----BEGIN RSA PRIVATE KEY-----\" в начале, \"-----END RSA PRIVATE KEY-----\" в конце, 64 символа в каждой строке, последняя строка не должна превышать 64 символа, и не может быть пустых строк.", "The private key of the certificate, the extension of the private key is \"key\", you can directly enter the content of the private key file or upload a private key that conforms to the format document.": "Закрытый ключ сертификата, расширение закрытого ключа - \"key\", вы можете ввести содержимое файла закрытого ключа напрямую или загрузить закрытый ключ, соответствующий документу формата.", "The resource class of the scheduled node needs to correspond to the resource class name of the flavor used by the ironic instance (for example, the resource class name of the scheduling node is baremetal.with-GPU, and the custom resource class name of the flavor is CUSTOM_BAREMETAL_WITH_GPU=1).": "Ресурсный класс запланированного узла должен соответствовать имени ресурсного класса используемого образца (например, имя ресурсного класса запланированного узла - baremetal.with-GPU, а пользовательское имя ресурсного класса образца - CUSTOM_BAREMETAL_WITH_GPU=1).", "The resource has been deleted": "Ресурс был удален", "The root and os_admin are default users and cannot be created!": "Пользователи root и os_admin являются пользователями по умолчанию и не могут быть созданы!", "The root disk of the instance has snapshots": "", "The security group is similar to the firewall function and is used to set up network access control. ": "Группа безопасности аналогична функции брандмауэра и используется для настройки контроля доступа к сети.", "The security group is similar to the firewall function for setting up network access control, or you can go to the console and create a new security group. (Note: The security group you selected will work on all virtual LANs on the instances.)": "Группа безопасности аналогична функции брандмауэра для настройки контроля доступа к сети, или вы можете перейти в консоль и создать новую группу безопасности. (Примечание: Группа безопасности, которую вы выбрали, будет работать на всех виртуальных сетях инстансов.)", "The selected VPC/subnet does not have IPv6 enabled.": "Выбранная ВПС/ подсеть не поддерживает IPv6.", "The selected network has no subnet": "Выбранная сеть не имеет подсети", "The selected project is different from the project to which the network belongs. That is, the subnet to be created is not under the same project as the network. Please do not continue unless you are quite sure what you are doing.": "", "The session has expired, please log in again.": "Сессия истекла, пожалуйста, войдите в систему снова.", "The shelved offloaded instance only supports immediate deletion": "Спящий выгруженный инстанс поддерживает только мгновенное удаление", "The size of the external port range is required to be the same as the size of the internal port range": "Размер диапазона внешних портов должен быть таким же, как размер внутри", "The start source is a template used to create an instance. You can choose an image or a bootable volume.": "", "The starting number must be less than the ending number": "Начальное число должно быть меньше конечного числа", "The timeout for cluster creation in minutes.": "Тайм-аут создания кластера в минутах.", "The timeout period of waiting for the return of the health check request, the check timeout will be judged as a check failure": "Период ожидания ответа от запроса проверки состояния, превышение тайм-аута будет считаться ошибкой проверки состояния", "The total amount of data is { total }, and the interface can support downloading { totalMax } pieces of data. If you need to download all the data, please contact the administrator.": "Общий объем данных составляет { total }, и интерфейс поддерживает загрузку { totalMax } элементов данных. Если вам нужно загрузить все данные, обратитесь к администратору.", "The trait name of the flavor needs to correspond to the trait of the scheduling node; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all necessary traits (for example: the trait of the scheduling node has HW_CPU_X86_VMX trait, and the flavor adds HW_CPU_X86_VMX, it can be scheduled to this node for necessary traits).": "Имя черты образца должно соответствовать черте запланированного узла; внедряя необходимые черты в инстанс ironic, вычислительный сервис будет планировать инстанс только на узле с метками, необходимыми для работы (например, черта запланированного узла имеет черту HW_CPU_X86_VMX, и образ добавляет HW_CPU_X86_VMX, его можно планировать на этом узле для необходимых черт).", "The trait of the scheduled node needs to correspond to the trait of the flavor used by the ironic instance; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all the necessary traits (for example, the ironic instance which use the flavor that has HW_CPU_X86_VMX as a necessary trait, can be scheduled to the node which has the trait of HW_CPU_X86_VMX).": "Черта запланированного узла должна соответствовать черте образца, используемого инстансом ironic; внедряя необходимые черты в инстанс ironic, вычислительный сервис будет планировать инстанс только на физическом узле с необходимыми чертами (например, инстанс ironic, использующий образец, у которого есть черта HW_CPU_X86_VMX в качестве обязательной черты, может быть запланирован на узел с чертой HW_CPU_X86_VMX).", "The unit suffix must be one of the following: Kb(it), Kib(it), Mb(it), Mib(it), Gb(it), Gib(it), Tb(it), Tib(it), KB, KiB, MB, MiB, GB, GiB, TB, TiB. If the unit suffix is not provided, it is assumed to be KB.": "Суффикс единицы измерения должен быть одним из следующих: Kb(it), Kib(it), Mb(it), Mib(it), Gb(it), Gib(it), Tb(it), Tib(it), KB, KiB, MB, MiB, GB, GiB, TB, TiB. Если суффикс единицы измерения не указан, предполагается, что это KB.", "The user has been disabled, please contact the administrator": "Пользователь отключен, пожалуйста, свяжитесь с администратором", "The user needs to ensure that the input is a shell script that can run completely and normally.": "Пользователь должен убедиться, что ввод представляет собой оболочку, которая может выполняться полностью и нормально.", "The value of the upper limit of the range must be greater than the value of the lower limit of the range.": "Значение верхнего предела диапазона должно быть больше значения нижнего предела диапазона.", "The volume associated with the backup is not available, unable to restore.": "<PERSON>и<PERSON><PERSON>, связанный с резервной копией, недоступен, восстановление невозможно.", "The volume status can be reset to in-use only when the previous status is in-use.": "Статус диска можно сбросить в in-use только в том случае, если предыдущий статус - in-use.", "The volume type needs to be consistent with the volume type when the snapshot is created.": "Тип диска должен соответствовать типу диска при создании снимка.", "The volume type needs to set \"multiattach\" in the metadata to support shared volume attributes.": "Тип диска должен устанавливать \"multiattach\" в метаданных для поддержки общих атрибутов диска.", "The working directory for commands to run in": "Рабочий каталог для выполнения команд", "The zone name should end with \".\"": "", "The {action} instruction has been issued, instance: {name}. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.": "Выдана инструкция {action}, инстанс: {name}. \n Вы можете подождать несколько секунд, чтобы отследить изменения списка данных, или вручную обновить данные, чтобы получить окончательный результат отображения.", "The {action} instruction has been issued. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.": "Выдана инструкция {action}. \n Вы можете подождать несколько секунд, чтобы отследить изменения списка данных", "The {name} has already been used by other {resource}({content}), please change.": "", "The {name} {ports} have already been used, please change.": "", "There are resources that cannot {action} in the selected resources, such as:": "", "There are resources that cannot {action} in the selected resources.": "", "There are resources under the project and cannot be deleted.": "", "There is currently a image that needs to continue uploading. Please refresh page and upload the image. The new image cannot be used for the time being.": "В настоящее время существует существующий файл, который необходимо продолжить загрузку. Пожалуйста, обновите или вернитесь, чтобы загрузить его. Новое не может быть использовано в настоящее время.", "There is currently no file to paste.": "", "This operation creates a security group with default security group rules for the IPv4 and IPv6 ether types.": "", "This service will automatically query the configuration (CPU, memory, etc.) and MAC address of the physical machine, and the ironic-inspector service will automatically register this information in the node information.": "Этот сервис автоматически опрашивает конфигурацию (CPU, память и т. д.) и MAC-адрес физической машины, а служба ironic-inspector автоматически регистрирует эту информацию в данных о узле.", "This will delete all child objects of the load balancer.": "Это удалит все дочерние объекты балансировщика нагрузки.", "Threads Activity Trends": "Тенденции активности потоков", "Time Interval: ": "Интервал времени: ", "Time To Live": "Время жизни", "Time To Live in seconds.": "Время жизни в секундах.", "Time between running the check in seconds": "Интервал выполнения проверки в секундах", "Timeout(Minute)": "Тайм-аут (минуты)", "Timeout(s)": "Тайм-аут (секунды)", "Tips: without domain means \"Default\" domain.": "Советы: отсутствие домена означает домен «Default».", "To open": "Открыть", "Today CPU usage > 80% alert": "Сегодня предупреждение: использование CPU > 80%", "Today Memory usage > 80% alert": "Сегодня предупреждение: использование памяти > 80%", "Togo": "Того", "Tokelau": "Токелау", "Tonga": "Тонга", "Too many disks mounted on the instance will affect the read and write performance. It is recommended not to exceed 16 disks.": "Слишком много дисков, смонтированных на инстансе, повлияют на производительность чтения и записи. Рекомендуется не превышать 16 дисков.", "Topic": "Тема", "Topology": "Топология", "Total": "Всего", "Total Capacity": "Общая емкость", "Total Connections": "Всего соединений", "Total Consumers": "Всего потребителей", "Total Containers": "Всего контейнеров", "Total Exchanges": "Всего обменов", "Total IPs": "Всего IP-адресов", "Total Queues": "Всего очередей", "Total Ram": "Об<PERSON>ий объем памяти", "Total {total} items": "Всего {total} элементов", "Trait Properties": "Свойства признаков", "Traits": "Признаки", "Transfer ID": "Идентификатор передачи", "Transfer Name": "Имя передачи", "Transferred": "Передано", "Transform Protocol": "Протокол преобразования", "Trinidad and Tobago": "Тринидад и Тобаго", "True": "Истина", "Tunisia": "<PERSON>у<PERSON><PERSON><PERSON>", "Turkey": "Турция", "Turkmenistan": "Туркменистан", "Turks and Caicos Islands": "Теркс и Кайкос", "Tuvalu": "Тувалу", "Two-way authentication": "Двусторонняя аутентификация", "Type": "Тип", "UDP": "UDP", "UDPLite": "UDPLite", "UNHEALTHY": "НЕЗДОРОВЫЙ", "UNKNOWN": "НЕИЗВЕСТНО", "UOS": "UOS", "UPDATE COMPLETE": "ОБНОВЛЕНИЕ ЗАВЕРШЕНО", "UPDATE FAILED": "ОБНОВЛЕНИЕ НЕ УДАЛОСЬ", "UPDATE IN PROGRESS": "ОБНОВЛЕНИЕ В ПРОЦЕССЕ", "USB Info": "Информация о USB", "USB Parameters": "Параметры USB", "USB model, used when configuring instance flavor": "", "USER": "ПОЛЬЗОВАТЕЛЬ", "UStack": "UStack", "UStack Reports": "Отчеты UStack", "UStack Services": "Сервисы UStack", "UUID": "UUID", "Ubuntu": "Ubuntu", "Uccps": "Uccps", "Uganda": "Уганда", "Ukraine": "Украина", "Unable to create instance: batch creation is not supported when specifying IP.": "Невозможно создать инстанс: пакетное создание не поддерживается при указании IP-адреса.", "Unable to create instance: insufficient quota to create resources.": "Невозможно создать инстанс: недостаточно квот для создания ресурсов.", "Unable to create volume: insufficient quota to create resources.": "Невозможно создать диск: недостаточно квот для создания ресурсов.", "Unable to delete router \"{ name }\". External gateway is opened, please clear external gateway first.": "Невозможно удалить маршрутизатор \"{ name }\". Внешний шлюз открыт, сначала очистите внешний шлюз.", "Unable to get {name} detail.": "Невозможно получить информацию о {name}.", "Unable to get {name}.": "Невозможно получить {name}.", "Unable to get {title}, please go back to ": "Невозможно получить {title}, вернитесь на предыдущую страницу: ", "Unable to get {title}, please go to ": "Невозможно получить {title}, перейдите на страницу: ", "Unable to paste into the same folder.": "Невозможно вставить в ту же папку.", "Unable to render form": "Невозможно отобразить форму", "Unable to {action} {name}.": "Невозможно выполнить действие {action} для {name}.", "Unable to {action}, because : {reason}, instance: {name}.": "Невозможно выполнить действие {action}, потому что: {reason}, инстанс: {name}.", "Unable to {action}, instance: {name}.": "Невозможно выполнить действие {action}, инстанс: {name}.", "Unable to {action}.": "Невозможно выполнить действие {action}.", "Unable to {title}, please go back to ": "Невозможно получить {title}, вернитесь на предыдущую страницу: ", "Unattached": "Не присоединено", "Unavailable": "Недоступно", "Unbootable": "Незагрузочный", "Unbounded": "Без ограничений", "United Arab Emirates": "Объединенные Арабские Эмираты", "United Kingdom": "Соединенное Королевство", "United States": "Соединенные Штаты Америки", "Unknown": "Неизвестно", "Unless Stopped": "Кроме случаев остановки", "Unless you know clearly which AZ to create the volume in, you don not need to fill in here.": "Если вы точно не знаете, в какой доступной зоне создать диск, вам не нужно заполнять это поле.", "Unlimit": "Без ограничений", "Unlock": "Разблокировать", "Unlock Instance": "Разблокировать инстанс", "Unmanage Error": "Ошибка управления", "Unmanage Starting": "Начало процесса управления", "Unmanaged": "Неуправляемый", "Unpause": "Возобновить", "Unpause Container": "Возобновить контейнер", "Unpause Instance": "Возобновить инстанс", "Unrescuing": "Отмена спасения", "Unrestricted": "Неограниченный", "Unset": "Сбросить", "Unshelve": "Вернуть на полку", "Unshelve Instance": "Вернуть инстанс с полки", "Unshelving": "Возвращение с полки", "Unused": "Не используется", "Up": "Включено", "Update": "", "Update Access": "Обновить доступ", "Update At": "Обновлено в", "Update Cluster Template": "Обновить шаблон кластера", "Update Complete": "Обновление завершено", "Update Failed": "Ошибка обновления", "Update In Progress": "Идет обновление", "Update Record Set": "Обновить набор записей", "Update Segment": "", "Update Status": "Обновить статус", "Update Template": "Обновить шаблон", "Update User Password": "Обновить пароль пользователя", "Update user password": "Обновить пароль пользователя", "Updated": "", "Updated At": "Обновлено в", "Updating": "Обновление", "Updating Password": "Обновление пароля", "Upgrade Cluster": "Обновить кластер", "Upload File": "Загрузить файл", "Upload Type": "Тип загрузки", "Upload progress": "Прогресс загрузки", "Uploading": "Загрузка", "Uploads with mirrors are interrupted. The name of the mirror file is: {name}. Do you want to continue the image? Please select the mirror file in the button below and click the \"Continue\" button; Tips: If the wrong file is selected, it will not be interrupted. Clicking the \"Delete\" button will delete the task of uploading the image.": "Загрузка зеркального изображения была прервана. Имя зеркального файла: {name}. Вы хотите продолжить зеркальное отражение? Пожалуйста, выберите зеркальный файл на кнопке ниже и нажмите кнопку «Продолжить»; Советы: Если вы выберете неправильный файл, он не будет прерван и не будет продолжен. Нажмите кнопку «Удалить», чтобы удалить задачу загрузки зеркального изображения.", "Uruguay": "Уругвай", "Usage": "Использование", "Usage Type": "Тип использования", "Usb Controller": "Контроллер USB", "Use Type": "Тип использования", "Used": "Используется", "Used IPs": "Используемые IP-адреса", "Used by tunnel(s): {names}. ID(s): {ids}": "Используется туннелем(ями): {names}. Идентификатор(ы): {ids}", "Used to restrict whether the application credential may be used for the creation or destruction of other application credentials or trusts.": "Используется для ограничения возможности использования учетных данных приложения для создания или уничтожения других учетных данных или доверительных отношений приложения.", "User": "Пользователь", "User Account": "Учетная запись пользователя", "User Center": "Центр пользователя", "User Data": "Пользовательские данные", "User Detail": "Информация о пользователе", "User Edit": "Редактирование пользователя", "User Group": "Группа пользователей", "User Group Detail": "Информация о группе пользователей", "User Group ID/Name": "Идентификатор/Имя группы пользователей", "User Group Name": "Имя группы пользователей", "User Group Num": "Номер группы пользователей", "User Group Num: ": "Номер группы пользователей: ", "User Groups": "Группы пользователей", "User ID": "Идентификатор пользователя", "User ID/Name": "Идентификатор пользователя/Имя", "User Name": "Имя пользователя", "User Num": "Номер пользователя", "User Num: ": "Номер пользователя: ", "User name can not be duplicated": "Имя пользователя не может быть дублировано", "User need to change password": "Пользователю необходимо изменить пароль", "Username": "Имя пользователя", "Username or password is incorrect": "Имя пользователя или пароль неверны", "Users": "Пользователи", "Using cascading deletion, when the volume has snapshots, the associated snapshot will be automatically deleted first, and then the volume will be deleted, thereby improving the success rate of deleting the volume.": "Используя каскадное удаление, если у диска есть снимки, связанный снимок будет автоматически удален первым, а затем диск будет удален, что повысит успешность удаления диска.", "Using server groups, you can create cloud hosts on the same/different physical nodes as much as possible to meet the affinity/non-affinity requirements of business applications.": "Используя группы серверов, вы можете создавать облачные хосты на одних и тех же/разных физических узлах, насколько это возможно, чтобы удовлетворить требования бизнес-приложений к affinity/non-affinity.", "Uzbekistan": "Узбекистан", "VCPU (Core)": "VCPU (Ядер)", "VCPUs": "Виртуальные процессоры (VCPU)", "VDI - VirtualBox compatible image format": "VDI - Формат образа, совместимый с VirtualBox", "VGPU": "Виртуальные графические процессоры (VGPU)", "VGPU (Core)": "Виртуальный графический процессор (Ядер)", "VHD - VirtualPC compatible image format": "VHD - Формат образа, совместимый с VirtualPC", "VIF Details": "Подробности VIF", "VIF Type": "Тип VIF", "VIR Domain Event": "", "VMDK - Hyper-V compatible image format": "VMDK - Формат образа, совместимый с Hyper-V", "VNC": "VNC", "VNIC Type": "Ти<PERSON> VNIC", "VPN": "VPN", "VPN EndPoint Groups": "Группы конечных точек VPN", "VPN Gateways": "Шлюзы VPN", "VPN Service": "Служба VPN", "VPN Service ID": "Идентификатор службы VPN", "VPNs": "VPN", "VRRP": "VRRP", "Valid": "Действительный", "Value": "Значение", "Values": "Значения", "Vanuatu": "Вануату", "Vatican City State (Holy See)": "Государство Ватикан (Святой Престол)", "Vendor Interface": "Интерфейс производителя", "Venezuela": "Венесуэла", "Verifying": "Проверка", "Version": "Версия", "Vietnam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "View": "Просмотр", "View Detail": "Просмотреть детали", "View Full Log": "Посмотреть полный журнал", "View Rules": "Просмотр правил", "View virtual adapters": "Просмотр виртуальных адаптеров", "Virgin Islands (U.S.)": "Виргинские острова (США)", "Virtual Adapter": "Виртуальный адаптер", "Virtual Adapter ID": "Идентификатор виртуального адаптера", "Virtual LAN": "Виртуальная локальная сеть (VLAN)", "Virtual LANs": "Виртуальные локальные сети (VLANs)", "Virtual Resource Overview": "Обзор виртуальных ресурсов", "Virtual Resources Used": "Используемые виртуальные ресурсы", "Virtual adapter mainly used for binding instance and other operations, occupying the quota of the port.": "Виртуальный адаптер, в основном используется для привязки инстанса и других операций, занимая квоту порта.", "Visibility": "Видимость", "Visualization Compute Optimized Type with GPU": "Оптимизированный под визуализацию вычислительный тип с GPU", "Volume": "Диск", "Volume Backup": "Резервное копирование диска", "Volume Backup Capacity (GiB)": "Объем резервной копии диска (ГиБ)", "Volume Backup Detail": "Подробности резервного копирования диска", "Volume Backup Name": "Имя резервной копии диска", "Volume Backups": "Резервные копии дисков", "Volume Capacity (GiB)": "Объем диска (ГиБ)", "Volume Detail": "Подробнее о диске", "Volume Driver": "Драйвер диска", "Volume ID": "Идентификатор диска", "Volume ID/Name": "Идентификатор/Имя диска", "Volume Info": "Информация о диске", "Volume Name": "Имя диска", "Volume Size": "Размер диска", "Volume Snapshot": "Снимок диска", "Volume Snapshot Detail": "Подробности снимка диска", "Volume Snapshot Name": "Имя снимка диска", "Volume Snapshots": "Снимки дисков", "Volume Source": "Источник диска", "Volume Transfer": "Передача диска", "Volume Type": "Тип диска", "Volume Type Detail": "Подробности типа диска", "Volume Types": "Ти<PERSON><PERSON> <PERSON><PERSON>ов", "Volumes": "Дис<PERSON>и", "Wallis And Futuna Islands": "Уоллис и Футуна", "Warn": "Предупреждение", "Warning": "Предупреждение", "Weight": "<PERSON>е<PERSON>", "Weights": "Веса", "Welcome": "Добро пожаловать", "Western Sahara": "Западная Сахара", "When auto-expand/close is enabled, if there is no operation in the pop-up window, the pop-up window will be closed automatically after { seconds } seconds, and it will be automatically expanded when the displayed content changes.": "Когда включена функция автоматического раскрытия/закрытия, если во всплывающем окне нет действий, оно будет автоматически закрыто после { seconds } секунд, и оно автоматически развернется, когда изменится отображаемое содержимое.", "When the computing service starts the recycling instance interval, the instance will be stored in the recycling bin after deletion, and will be retained according to the corresponding time interval. You can choose to restore it within this period. After successful recovery, the status of the instance is running and related resources remain unchanged.": "Когда вычислительная служба запускает интервал восстановления инстанса, инстанс будет сохранен в корзине после удаления и будет сохранен в соответствии с соответствующим временным интервалом. Вы можете выбрать его восстановление в течение этого периода. После успешного восстановления статус инстанса будет , и связанные ресурсы останутся неизменными.", "When the volume is \"bootable\" and the status is \"available\", it can be used as a startup source to create an instance.": "Когда диск является «загрузочным» и статус «доступен», его можно использовать в качестве источника загрузки для создания инстанса.", "When you do online backup of the volume that has been bound, you need to pay attention to the following points:": "При выполнении онлайн-резервного копирования диска, который был привязан, обратите внимание на следующие моменты:", "When you restore a backup, you need to meet one of the following conditions:": "При восстановлении резервной копии вам необходимо выполнить одно из следующих условий:", "When your Yaml file is a fixed template, variable variables can be stored in an environment variable file to implement template deployment. The parameters in the environment variable file need to match the parameters defined in the template file.": "Когда ваш файл Yaml является фиксированным шаблоном, переменные переменные можно хранить в файле переменных среды для реализации развертывания шаблона. Параметры в файле переменных среды должны соответствовать параметрам, определенным в файле шаблона.", "Whether enable or not using the floating IP of cloud provider.": "Включать или нет использование плавающего IP-адреса облачного провайдера.", "Whether the Login Name can be used is up to the feasible configuration of cloud-init or cloudbase-init service in the image.": "Возможность использования имени пользователя зависит от выполнимой конфигурации службы cloud-init или cloudbase-init в образе.", "Whether the boot device should be set only for the next reboot, or persistently.": "Следует ли устанавливать устройство загрузки только для следующей перезагрузки или постоянно.", "Which Network Interface provider to use when plumbing the network connections for this Node": "поставщика сетевых интерфейсов использовать при настройке сетевых подключений для этого узла", "Windows": "", "Workdir": "", "Working Directory": "Рабочий каталог", "X86 Architecture": "Архитектура X86", "YAML File": "YAML-файл", "Yemen": "Йемен", "Yes": "Да", "Yes - Create a new system disk": "", "You are not allowed to delete policy \"{ name }\" used by firewalls: { firewalls }.": "", "You are not allowed to delete policy \"{ name }\".": "", "You are not allowed to delete router \"{ name }\".": "Вам запрещено удалять маршрутизатор \"{ name }\".", "You are not allowed to delete rule \"{ name }\" in use.": "", "You are not allowed to delete rule \"{ name }\".": "", "You are not allowed to delete snapshot \"{ name }\", which is used by creating volume \"{volumes}\".": "Вам запрещено удалять снимок \"{ name }\", который используется для создания диска \"{volumes}\".", "You are not allowed to delete snapshot \"{ name }\".": "Вам запрещено удалять снимок \"{ name }\".", "You are not allowed to jump to the console.": "Вам запрещено перейти в консоль.", "You are not allowed to { action } \"{ name }\".": "Вам запрещено { action } \"{ name }\".", "You are not allowed to { action } {name}.": "Вам запрещено { action } {name}.", "You are not allowed to {action}, instance: {name}.": "Вам запрещено {action}, инстанс: {name}.", "You are not allowed to {action}.": "Вам запрещено {action}.", "You can manually specify a physical node to create an instance.": "Вы можете вручную указать физический узел для создания инстанса.", "You don't have access to get {name}.": "У вас нет доступа к получению {name}.", "You may update the editable properties of the RBAC policy here.": "", "Yugoslavia": "Югославия", "Zambia": "Замбия", "Zimbabwe": "Зимбабве", "Zone": "", "Zone ID": "Идентификатор зоны", "Zone ID/Name": "", "Zone Name": "Имя зоны", "Zones Detail": "Подробности о зонах", "abandon stack": "отказаться от стека", "add access rule": "добавить правило доступа", "add network": "добавить сеть", "add router": "добавить маршрутизатор", "all": "все", "an optional string field to be used to store any vendor-specific information": "необязательное текстовое поле для хранения информации, специфичной для поставщика", "application credential": "полномочие приложения", "associate floating ip": "сопоставить плавающий IP", "attach interface": "прикрепить интерфейс", "authorized by group ": "авторизовано группой", "auto": "", "auto_priority": "", "availability zones": "зоны доступности", "available": "доступно", "bare metal node": "физический узел", "bare metal nodes": "физические узлы", "be copied": "быть скопированным", "be cut": "быть вырезанным", "be deleted": "быть удаленным", "be rebooted": "быть перезагруженным", "be recovered": "быть восстановленным", "be released": "быть освобожденным", "be soft rebooted": "быть мягко перезагруженным", "be started": "быть запущенным", "be stopped": "быть остановленным", "capsules": "капсулы", "certificate": "сертификат", "cidr": "CIDR", "cinder services": "cinder-сервисы", "clusters": "кластеры", "clustertemplates": "шаблоны кластера", "compute hosts": "хосты вычислений", "compute services": "сервисы вычислений", "configurations": "конфигурации", "confirm resize or migrate": "подтвердить изменение размера или миграцию", "connect subnet": "подключить подсеть", "container objects": "объекты контейнера", "containers": "контейнеры", "create DSCP marking rule": "создать правило маркировки DSCP", "create a new network/subnet": "создать новую сеть/подсеть", "create a new security group": "создать новую группу безопасности", "create allowed address pair": "создать разрешенную пару адресов", "create bandwidth limit rule": "создать правило ограничения пропускной способности", "create baremetal node": "создать физический узел", "create default pool": "создать пул по умолчанию", "create encryption": "создать шифрование", "create firewall policy": "", "create flavor": "создать конфигурацию", "create instance snapshot": "создать снимок инстанса", "create ipsec site connection": "создать IPsec-соединение между сайтами", "create network": "создать сеть", "create router": "создать маршрутизатор", "create share": "создать общий ресурс", "create share group": "создать группу общего ресурса", "create share group type": "создать тип группы общего доступа", "create share network": "создать общую сеть", "create share type": "создать тип общего ресурса", "create stack": "создать стек", "create volume": "создать диск", "create volume snapshot": "создать снимок диска", "create volume type": "создать тип диска", "create vpn": "создать VPN", "create vpn endpoint group": "создать группу конечных точек VPN", "create vpn ike policy": "создать политику ike VPN", "create vpn ipsec policy": "создать политику ipsec VPN", "data": "данные", "database backups": "резервные копии баз данных", "database instances": "инстансы баз данных", "delete": "удалить", "delete allowed address pair": "удалить разрешенную пару адресов", "delete application credential": "удалить учетные данные приложения", "delete bandwidth egress rules": "удаление правил исходящего трафика", "delete bandwidth ingress rules": "удаление правил входящего трафика", "delete certificate": "удалить сертификат", "delete container": "удалить контейнер", "delete default pool": "удалить пул по умолчанию", "delete domain": "удалить домен", "delete dscp marking rules": "удалить правила маркировки DSCP", "delete firewall": "", "delete flavor": "удалить конфигурацию", "delete group": "удалить группу", "delete host": "", "delete image": "удалить образ", "delete instance": "удалить инстанс", "delete instance snapshot": "удалить снимок инстанса", "delete ipsec site connection": "удалить IPsec-соединение между сайтами", "delete ironic instance": "удалить инстанс ironic", "delete keypair": "удалить пару ключей", "delete listener": "удалить listener", "delete load balancer": "удалить балансировщик нагрузки", "delete member": "удалить участника", "delete network": "удалить сеть", "delete policy": "", "delete port forwarding": "удалитылку портов", "delete project": "удалить проект", "delete qos policy": "удалить политику QoS", "delete role": "удалить роль", "delete router": "удалить маршрутизатор", "delete rule": "", "delete segments": "", "delete stack": "удалить стек", "delete static route": "удалить статический маршрут", "delete subnet": "удалить подсеть", "delete user": "удалить пользователя", "delete virtual adapter": "удалить виртуальный адаптер", "delete volume": "удалить диск", "delete volume backup": "удалить резервную копию диска", "delete volume snapshot": "удалить снимок диска", "delete vpn": "удалить VPN", "delete vpn IKE policy": "удалить политику IKE VPN", "delete vpn IPsec policy": "удалить политику IPsec VPN", "delete vpn endpoint groups": "удалить группы конечных точек VPN", "description": "описание", "detach instance": "отсоединить инстанс", "detach security group": "отсоединить группу безопасности", "disable cinder service": "отключить сервис cinder", "disable compute service": "отключить сервис вычислений", "disable neutron agent": "отключить агента neutron", "disassociate floating ip": "отсоединить плавающий IP-адрес", "disconnect subnet": "отключить подсеть", "dns zones": "DNS-зоны", "domain": "домен", "domains": "домены", "download image": "Скачать изображение", "e.g. 2001:Db8::/48": "например, 2001:Db8::/48", "edit baremetal node": "редактировать физический узел", "edit default pool": "редактировать пул по умолчанию", "edit health monitor": "редактировать монитор состояния", "edit image": "редактировать образ", "edit instance snapshot": "редактировать снимок инстанса", "edit member": "редактировать информацию участника", "edit system permission": "редактировать разрешение системы", "egress": "исходящий", "enable cinder service": "включить сервис cinder", "enable compute service": "включить сервис вычислений", "enable neutron agent": "включить агент neutron", "external port": "внешний порт", "external ports": "внешние порты", "extra specs": "дополнительные характеристики", "firewall": "", "firewall policies": "", "firewall rule": "", "firewall rules": "", "firewalls": "", "flavor": "конфигурация", "floating ip": "плавающий IP-адрес", "floating ips": "плавающие IP-адреса", "heat services": "сервисы heat", "host aggregates": "агрегированные хосты", "hosts": "хосты", "hypervisor": "гипервизор", "image": "образ", "images": "образы", "in": "в", "ingress": "входящий", "insert": "вставить", "insert rule": "", "instance": "инстанс", "instance snapshot": "снимок инстанса", "instance snapshots": "снимки инстанса", "instance: {name}.": "инстанс: {name}.", "instances": "инстансы", "internal port": "внутренний порт", "internal ports": "внутренние порты", "ipsec site connection": "соединение между сайтами IPsec", "jump to the console": "перейти к консоли", "keypair": "ключевая пара", "keypairs": "ключевые пары", "labels": "метки", "list page": "страница со списком", "listener": "listener", "listeners": "listeners", "live migrate": "живая миграция", "load balancer": "балансировщик нагрузки", "lock instance": "заблокировать инстанс", "manage ports": "", "manage qos spec": "управление спецификацией qos", "manage resource types": "управление типами ресурсов", "message": "сообщение", "message.reason": "причина сообщения", "metadata": "метаданные", "migrate": "миграция", "modify instance tags": "изменить метки инстанса", "modify project tags": "изменить метки проекта", "network": "сеть", "networks": "сети", "neutron agent": "агент Neutron", "neutron agents": "агенты Neutron", "ns1.example.com admin.example.com 2013022001 86400 7200 604800 300 <ul><li>The primary name server for the domain, which is ns1.example.com or the first name server in the vanity name server list.</li><li>The responsible party for the domain: admin.example.com.</li><li>A timestamp that changes whenever you update your domain.</li><li>The number of seconds before the zone should be refreshed.</li><li>The number of seconds before a failed refresh should be retried.</li><li>The upper limit in seconds before a zone is considered no longer authoritative.</li><li>The negative result TTL (for example, how long a resolver should consider a negative result for a subdomain to be valid before retrying).</li></ul>": "", "open external gateway": "открыть внешний шлюз", "out": "выход", "paste files to folder": "вставить файлы в папку", "pause instance": "приостановить инстанс", "phone": "телефон", "please select network": "пожалуйста, выберите сеть", "please select subnet": "пожалуйста, выберите подсеть", "policy": "", "port": "порт", "port forwarding": "перенаправление порта", "port forwardings": "перенаправления порта", "port groups": "группы портов", "ports": "порты", "project": "проект", "projects": "проекты", "qemu_guest_agent enabled": "вклю<PERSON>ен qemu_guest_agent", "qoS policy": "политика QoS", "qos specs": "спецификации QoS", "quota set to -1 means there is no quota limit on the current resource": "квота, установленная на -1, означает отсутствие лимита квоты на текущий ресурс", "read": "чтение", "reboot instance": "перезагрузить инстанс", "rebuild instance": "восстановить инстанс", "receive": "получить", "recordsets": "наборы записей", "recover instance": "восстановить инстанс", "recycle bins": "корзины для восстановления", "release fixed ip": "освободить фиксированный IP-адрес", "remove network": "удалить сеть", "remove router": "удалить маршрутизатор", "remove rule": "", "reserved_host": "", "resize": "изменить размер", "resume instance": "возобновить инстанс", "revert resize or migrate": "отменить изменение размера или миграцию", "rh_priority": "", "role": "роль", "roles": "роли", "router": "маршрутизатор", "routers": "маршрутизаторы", "security group": "группа безопасности", "security group rules": "правила группы безопасности", "security groups": "группы безопасности", "segments": "", "select an existing port": "выбрать существующий порт", "server group": "группа серверов", "server groups": "группы серверов", "services": "сервисы", "settings": "настройки", "share": "общий доступ", "share access rules": "правила доступа к общим ресурсам", "share group": "группа общего доступа", "share group type": "тип группы общего доступа", "share groups": "группы общего доступа", "share instance": "общий доступ к инстансу", "share instances": "общий доступ к инстансам", "share metadata": "метаданные общего доступа", "share network": "сеть общего доступа", "share server": "сервер общего доступа", "share servers": "серверы общего доступа", "share type": "тип общего доступа", "share types": "типы общего доступа", "shelve instance": "отложить инстанс", "smtp.example.com": "smtp.example.com", "soft reboot instance": "мягкая перезагрузка инстанса", "stack": "стек", "stack events": "события стека", "stack resources": "ресурсы стека", "stacks": "стеки", "start instance": "запустить инстанс", "static routers": "статические маршрутизаторы", "stop instance": "остановить инстанс", "storage backend": "хранилище данных", "subnet": "подсеть", "subnets": "подсети", "suspend instance": "приостановить инстанс", "the Republic of Abkhazia": "Республика Абхазия", "the folder is not empty": "папка не пуста", "the policy is in use": "политика используется", "the router has connected subnet": "маршрутизатор подключен к подсети", "the vpn gateway is in use": "шлюз VPN используется", "time / 24h": "время / 24 часа", "to delete": "удалить", "transmit": "передача", "unlock instance": "разблокировать инстанс", "unpause instance": "снять с паузы инстанс", "unshelve instance": "разархивировать инстанс", "update": "обновить", "update status": "обновить статус", "update template": "обновить шаблон", "used": "используется", "user": "пользователь", "user group": "группа пользователей", "user groups": "группы пользователей", "users": "пользователи", "vCPUs": "Виртуальные процессоры (vCPUs)", "vCPUs and ram are not used for bare metal scheduling": "vCPUs и объем ОЗУ не используются для планирования физических узлов", "volume": "диск", "volume backup": "резервное копирование диска", "volume backups": "резервные копии дисков", "volume capacity": "емкость диска", "volume snapshot": "снимок диска", "volume snapshots": "снимки дисков", "volume type": "тип диска", "volume type qos": "тип диска QoS", "volume type {type}": "тип диска {type}", "volume type {type} capacity": "емкость типа диска {type}", "volume types": "типы дисков", "volumes": "диски", "vpn IKE policy": "политика VPN IKE", "vpn IPsec policy": "политика VPN IPsec", "vpn endpoint groups": "группы конечных точек VPN", "vpn services": "VPN-сервисы", "write": "запись", "{ name } Format Error (e.g. *********** or ***********/24)": "", "{ name } Format Error (e.g. FE80:0:0:0:0:0:0:1 or FE80:0:0:0:0:0:0:1/10)": "", "{ size } GiB": "{ size } ГиБ", "{ size } KiB": "{ size } КиБ", "{ size } MiB": "{ size } <PERSON><PERSON>Б", "{ size } TiB": "{ size } ТиБ", "{ size } bytes": "{ size } байт", "{action} successfully, instance: {name}.": "{action} успешно, инстанс: {name}.", "{action} successfully.": "{action} успешно.", "{action} {name} successfully.": "{action} {name} успешно.", "{hours} hours {leftMinutes} minutes {leftSeconds} seconds": "{hours} часов {leftMinutes} минут {leftSeconds} секунд", "{interval, plural, =1 {one day} other {# days} } later delete": "{interval, plural, =1 {через один день} other {через # дней} } удалить", "{interval, plural, =1 {one hour} other {# hours} } later delete": "{interval, plural, =1 {через один час} other {через # часов} } удалить", "{interval, plural, =1 {one minute} other {# minutes} } later delete": "{interval, plural, =1 {через одну минуту} other {через # минут} } удалить", "{interval, plural, =1 {one week} other {# weeks} } later delete": "{interval, plural, =1 {через одну неделю} other {через # недель} } удалить", "{minutes} minutes {leftSeconds} seconds": "{minutes} минут {leftSeconds} секунд", "{name} type": "{name} тип", "{name} type capacity": "{name} использовано", "{name} type capacity (GiB)": "{name} использовано (ГиБ)", "{name} type snapshots": "{name} с<PERSON>и<PERSON><PERSON>ов", "{name} {id} could not be found.": "{name} {id} не найден.", "{number} {resource}": "{number} {resource}", "{pageSize} items/page": "{pageSize} элементов/страницу", "{seconds} seconds": "{seconds} секунд"}