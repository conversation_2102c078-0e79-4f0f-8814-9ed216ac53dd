{"3600": "3600", " You can go to the console to ": "Konsola gitmek için", "\"Shared\" volume can be mounted on multiple instances": "\"Paylaşılan\" disk, birden çok sanal makineye bağlanabilir.", "\"v=spf1 ipv4=********* include:examplesender.email +all\" <ul><li><b>v=spf1:</b> Tells the server that this contains an SPF record. Every SPF record must begin with this string.</li> <li><b>Guest List:</b> Then comes the “guest list” portion of the SPF record or the list of authorized IP addresses. In this example, the SPF record is telling the server that ipv4=********* is authorized to send emails on behalf of the domain.</li> <li><b>include:examplesender.net:</b> is an example of the include tag, which tells the server what third-party organizations are authorized to send emails on behalf of the domain. This tag signals that the content of the SPF record for the included domain (examplesender.net) should be checked and the IP addresses it contains should also be considered authorized. Multiple domains can be included within an SPF record but this tag will only work for valid domains.</li><li><b>-all:</b> Tells, the server that addresses not listed in the SPF record are not authorized to send emails and should be rejected.</li></ul>": "", "'ip' rule represents IPv4 or IPv6 address, 'cert' rule represents TLS certificate, 'user' rule represents username or usergroup, 'cephx' rule represents ceph auth ID.": "'ip' kuralı IPv4 ve IPv6 adresini temsil eder, 'cert' kuralı TLS sertifikasını temsil eder, 'user' kuralı kullanıcı adını ve kullanıcı grubunu temsil eder, 'cephx' kuralı ceph kimlik ID'sini temsil eder. ", "-1 means no connection limit": "-1, ba<PERSON><PERSON><PERSON> sınırı olmadığı anlamına gelir.", ".": ".", "0 iodef mailto:<EMAIL> <ul><li><b>0:</b> is flag. An unsigned integer between 0-255.</li> <li><b>iodef:</b> An ASCII string that represents the identifier of the property represented by the record.<br />Available Tags: \"issue\", \"issuewild\", \"iodef\"</li><li><b>mailto:<EMAIL>:</b> The value associated with the tag.</li></ul>": "", "1. The backup can only capture the data that has been written to the volume at the beginning of the backup task, excluding the data in the cache at that time.": "1. <PERSON><PERSON><PERSON><PERSON> testini<PERSON> b<PERSON>, ya<PERSON><PERSON><PERSON><PERSON> diskte toplanan veriler yedeklenebilir ve önbellekte depolanan veriler hariç tutulur.", "1. The name of the custom resource class property should start with CUSTOM_, can only contain uppercase letters A ~ Z, numbers 0 ~ 9 or underscores, and the length should not exceed 255 characters (for example: CUSTOM_BAREMETAL_SMALL).": "1. Kullanıcı kaynak sınıfı özelliğinin adı CUSTOM_ ile başlamalı, yalnızda A'dan Z'ye kadar olan büyük harfleri, 0'dan 9'a kadar olan rakamları veya alt çizgi (_) içermeli ve ismin uzunluğu 255 karakteri geçmemelidir (Örneğin: CUSTOM_BAREMETAL_SMALL).", "1. The name of the trait should start with CUSTOM_, can only contain uppercase letters A ~ Z, numbers 0 ~ 9 or underscores, and the length should not exceed 255 characters (for example: CUSTOM_TRAIT1).": "1. <PERSON><PERSON> niteliğin adı CUSTOM_ ile b<PERSON>ı, yaln<PERSON>zda A'dan Z'ye kadar olan büyük harfleri, 0'dan 9'a kadar olan rakamları veya alt çizgi (_) içermeli ve ismin uzunluğu 255 karakteri geçmemelidir (Örneğin: CUSTOM_TRAIT1).", "1. The volume associated with the backup is available.": "1. Ye<PERSON>lemeyle ilişkili disk kullanılabilir.", "1. You can create {resources} using ports or port ranges.": "1. <PERSON><PERSON> adaptörlerini veya ağ adaptör aralıklarını kullanarak {resources} oluşturabilirsiniz.", "10 0 5060 server1.example.com. <ul><li>\"10\" is the priority of the record. The lower the value, the higher the priority.</li><li>0 is the weight of the record. This is the weight of which this record has a chance to be used when there are multiple matching SRV records of the same priority.</li><li>5060 is the port of the record. This specifies the port on which the application or service is running.</li> <li>server1.example.com is the target of the record. This specifies the domain of the application or service the record is for. SRV records must specify a target which is either an A record or AAAA record, and may not use CNAME records.</li></ul>": "", "10 mail.example.com <ul><li><b>10:</b> Priority</li> <li><b>mail.example.com:</b> Value</li></ul>": "", "10s": "10 Saniye", "1D": "1 Gün", "1H": "1 Saat", "1min": "1 Dk", "2. In the same protocol, you cannot create multiple {resources} for the same source port or source port range.": "2. Aynı protokol içinde, aynı kaynak ağ adaptörü veya aynı ağ adaptörü aralığı için birden fazla {resources} oluşturamazsınız.", "2. The trait of the scheduled node needs to correspond to the trait of the flavor used by the ironic instance; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all the necessary traits (for example, the ironic instance which use the flavor that has CUSTOM_TRAIT1 as a necessary trait, can be scheduled to the node which has the trait of CUSTOM_TRAIT1).": "2. <PERSON><PERSON><PERSON> dü<PERSON>ü<PERSON><PERSON><PERSON>, İronik sanal makinesinde kullanılan özellikle uyumlu olmalıdır; gerekli özellikler İronic sanal makinesine ekelenerek hesaplama hizmeti sanal makinesi tüm gerekli özelliklere sahip bare metal düğümüne planlayacaktır. <PERSON><PERSON><PERSON><PERSON>, CUSTOM_TRAIT1 özelliğini gerekli bir özellik olarak içeren bir şablon kullanan İronik sanal makinesi, CUSTOM_TRAIT1 özelliğine sahip düğüme planlanabilir.", "2. The volume associated with the backup has been mounted, and the instance is shut down.": "2. <PERSON><PERSON><PERSON><PERSON>le ilişkili disk bağlandı ve sanal makine kapatılıyor.", "2. To ensure the integrity of the data, it is recommended that you suspend the write operation of all files when creating a backup.": "2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>çin, bir ye<PERSON><PERSON>e oluştururken tüm dosyaların yazma işlemini askıya almanız önerilir.", "2. You can customize the resource class name of the flavor, but it needs to correspond to the resource class of the scheduled node (for example, the resource class name of the scheduling node is baremetal.with-GPU, and the custom resource class name of the flavor is CUSTOM_BAREMETAL_WITH_GPU=1).": "2. Bir şablonun kaynak sınıfı adını özelleştirebilirsiniz ancak bu şablon kaynak sınıfı adı, zamanlanan düğümün kaynak sınıfına karşılık gelmesi gerekmektedir (<PERSON><PERSON><PERSON><PERSON>, zamanlama düğümünün kaynak sınıf adı 'baremetal.with-GPU' ise şablonun özel kayank sınıf adı 'CUSTOM_BAREMETAL_WITH_GPU=1' olmalıdır).", "3. When using a port range to create a port mapping, the size of the external port range is required to be the same as the size of the internal port range. For example, the external port range is 80:90 and the internal port range is 8080:8090.": "3. Bir ağ adaptörü eşleştirmesi oluşturmak için bir ağ adaptörü aralığı kullanırken, ha<PERSON><PERSON> ağ adaptmrü aralığı boyutu, dahiliağ adaptörü boyutu ile aynı olması gerekir. <PERSON><PERSON><PERSON><PERSON>, harici ağ adapötrü aralığı 80:90 ise dahiliağ adaptörü aralığı 8080:8090 olmalıdır.", "4 2 123456789abcdef67890123456789abcdef67890123456789abcdef123456789 <ul> <li><b>4 is Algorithm:</b> Algorithm (0: reserved; 1: RSA; 2: DSA, 3: ECDSA; 4: Ed25519; 6:Ed448)</li> <li><b>2 is Type:</b> Algorithm used to hash the public key (0: reserved; 1: SHA-1; 2: SHA-256)</li> <li><b>Last parameter is Fingerprint:</b> Hexadecimal representation of the hash result, as text</li> </ul>": "", "4. When you use a port range to create {resources}, multiple {resources} will be created in batches. ": " {resources} oluşturmak için bir ağ adaptörü aralığı kullandığınızda, toplu işlemlerde birden fazla {resources} oluşturulur.", "5min": "5 Dk", "8 to 16 characters, at least one uppercase letter, one lowercase letter, one number.": "8 ile 16 karakter, en az bir b<PERSON><PERSON><PERSON><PERSON> harf, en az bir kü<PERSON><PERSON>k harf, en az bir rakam.", "8 to 32 characters, at least one uppercase letter, one lowercase letter, one number and one special character.": "", "<username> or <username>@<domain>": "<kullanıcı adı> veya <kullanıcı adı>@<etki alanı>", "A command that will be sent to the container": "Konteyner'a gönderilecek bir komut", "A container with the same name already exists": "<PERSON><PERSON><PERSON> isme sahip bir konteyner bulunmaktadır.", "A dynamic scheduling algorithm that estimates the server load based on the number of currently active connections. The system allocates new connection requests to the server with the least number of current connections. Commonly used for long connection services, such as database connections and other services.": "Dinamik zamanlama algor<PERSON>, sun<PERSON><PERSON> yükünü o anda açık olan bağlantı sayısından tahmin eder. Sistem, o anda en az sayıda bağlantıya sahip sunucuya yeni bağlantılar tahsis eder. Veritabanı bağlantıları gibi uzun ömürlü bağlantılara sahip hizmetler için kullanılır.", "A host aggregate can be associated with at most one AZ. Once the association is established, the AZ cannot be disassociated.": "Bir ana bil<PERSON><PERSON>, en fazla bir AZ ile ilişkilendirilebilir. İlişki kurulduk<PERSON>, AZ'nin il<PERSON>ş<PERSON> kesi<PERSON>.", "A public container will allow anyone to use the objects in your container through a public URL.": "<PERSON>ir genel konteyner, her<PERSON><PERSON> konteyner içindeki nesneleri genel bir URL aracılığıyla kullanılabilir hale getirir.", "A rule specified before insertion or after insertion a rule. If both are not specified, the new rule is inserted as the first rule of the policy.": "", "A snapshot is an image which preserves the disk state of a running instance, which can be used to start a new instance.": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>şan bir sanal makinenin disk durumunu koruyan veya yeni bir sanal makinenin başlatılmasına izin veren bir imajdır.", "A template is a YAML file that contains configuration information, please enter the correct format.": "Taslak, yapılandırma bilgilerini içeren bir YAML dosyasıdır. Lütfen doğru biçimde girin.", "A template is a YAML file that contains configuration information.": "Taslak, yapılandırma bilgilerini içeren bir YAML dosyasıdır.", "ADMINISTRATOR": "YÖNETİCİ", "ADOPT COMPLETE": "ALINMA TAMAMLANDI", "AH": "AH", "AKI - Amazon kernel image format": "AKI - Amazon çekirdek imaj <PERSON>i", "ALLOW": "", "AMI - Amazon server image format": "AMI - Amazon sunucu imaj <PERSON>i", "ANY": "HERHANGİ BİR", "API Address": "API Adresi", "ARI - Amazon ramdisk image format": "ARI - Amazon ramdisk imaj biçimi", "ARM Architecture": "ARM <PERSON>", "Abandon Stack": "Yığını Terk Et", "Abandoning this stack will preserve the resources deployed by the stack.": "Bu yığını terk etmek, yığın tarafından dağıtılan kaynakların korunmasını sağlayacaktır.", "Abort Upload": "Yüklemeyi İptal Et", "Accept Volume Transfer": "Disk Transferini Kabul Et", "Access Control": "<PERSON><PERSON><PERSON><PERSON>", "Access Key": "<PERSON><PERSON><PERSON><PERSON>", "Access Level": "<PERSON><PERSON><PERSON><PERSON>", "Access Rules": "<PERSON><PERSON><PERSON><PERSON>", "Access Rules Status": "<PERSON><PERSON><PERSON><PERSON> Durumu", "Access To": "<PERSON><PERSON><PERSON><PERSON>", "Access Type": "<PERSON><PERSON><PERSON><PERSON>", "Access Type Setting": "<PERSON><PERSON><PERSON><PERSON>", "Action": "<PERSON><PERSON><PERSON>", "Action Logs": "<PERSON><PERSON><PERSON>", "Active": "Aktif", "Active Status": "Akt<PERSON>", "Add": "<PERSON><PERSON>", "Add Access Rule": "<PERSON><PERSON><PERSON><PERSON>", "Add Custom Metadata": "<PERSON><PERSON>", "Add Data Disks": "<PERSON><PERSON>", "Add Environment Variable": "<PERSON><PERSON>keni <PERSON>", "Add Exposed Ports": "Dışa Açık Ağ Adaptörleri Ekle", "Add External Members": "<PERSON><PERSON>", "Add Extra Info": "<PERSON><PERSON>", "Add Extra Spec": "<PERSON><PERSON>", "Add Host": "", "Add IP": "IP Ekle", "Add Label": "Etiket Ekle", "Add Member": "<PERSON><PERSON>", "Add Metadata": "<PERSON><PERSON>", "Add NUMA Node": "NUMA Düğümü Ekle", "Add Network": "<PERSON><PERSON>", "Add Policy": "", "Add Property": "<PERSON><PERSON><PERSON>", "Add Router": "Yönlendirici Ekle", "Add Virtual LAN": "Sanal LAN Ekle", "Add hosts to the aggregate or remove hosts from it. Hosts can be in multiple aggregates.": "Ana bilgisayarları bir kümeye ekleyin veya oradan çıkarın. Ana bilgisayarlar birden çok kümede bulunabilir.", "Add network": "<PERSON><PERSON>", "Add scheduler hints": "Planlayıcı İpucu Ekle", "Additional Labels": "<PERSON><PERSON>", "Additional routes announced to the instance, one entry per line(e.g. *************/24,***********)": "Her satıra bir giriş olacak şekilde bir sanal makineye ek yollar bildirilmesi (örneğin *************/24,***********)", "Additional routes announced to the instance, one entry per line(e.g. {ip})": "Her satıra bir giriş olacak şekilde bir sanal makineye ek yollar bildirilmesi (örneğin {ip})", "Address": "<PERSON><PERSON>", "Address Record": "<PERSON><PERSON>", "Addresses": "<PERSON><PERSON><PERSON>", "Admin State": "Yönetici Durum Ayarı", "Admin State Up": "Yönetici Durumu Açık", "Admin Status": "Yönetici Durumu", "Administrator": "Yönetici", "Adopt Complete": "<PERSON><PERSON><PERSON>", "Adopt Failed": "Alım Başarısız", "Adopt In Progress": "<PERSON><PERSON><PERSON>", "Advanced": "Gelişmiş", "Advanced Options": "Gelişmiş Seçenekler", "Advanced Params": "Gelişmiş Parametreler", "Affiliated Domain": "Bağlı Alan", "Affiliated Domain ID/Name": "", "Affinity": "Afinite", "Affinity (mandatory):": "Afinite (zorunlu):", "Affinity (not mandatory):": "Afinite (opsiyonel):", "Afghanistan": "Afganistan", "After attaching interface, you may need to login the instance to update the network interface configuration and restart the network service.": "Arayüzü bağ<PERSON><PERSON><PERSON><PERSON> sonra, ağ arayüzü yapılandırmasını güncellemek ve ağ hizmetini yeniden başlatmak için sanal makineye giriş yapmanız gerekebilir.", "After disable the compute service, the new instance will not schedule to the compute node.": "Hesaplama hizmetini devre dışı bıraktıktan sonra yeni sanal makine hesaplama düğümüne zamanlanmayacaktır.", "After shelving, the instance will be shut down, resources will be released, and the snapshot will be saved to Glance. This will take about a few minutes, please be patient. You also can choose to unshelve to restore the instance.": "Rafa kaldırma işleminden sonra, sanal makine kapatılacak, kaynaklar serbest bırakılacak ve anlık görüntü Glance'a kaydedilecektir. Bu işlem birkaç dakika sürebilir, lütfen sabırlı olun. <PERSON><PERSON><PERSON>, sanal makineyi geri yüklemek için raflardan çıkar seçebilirsiniz.", "After the share is expanded, the share cannot be reduced.": "Paylaşım genişletildikten sonra, pay<PERSON>ş<PERSON>m küçültülemez.", "After the volume is expanded, the volume cannot be reduced.": "Disk genişletildikten sonra, disk küçültülemez.", "Agent": "Agent", "Agree to force shutdown": "<PERSON><PERSON><PERSON> kapatma kabul ediliyor", "Albania": "Arnavutluk", "Algeria": "<PERSON><PERSON><PERSON><PERSON>", "All": "<PERSON><PERSON><PERSON>", "All Flavors": "<PERSON><PERSON><PERSON>", "All ICMP": "Tüm ICMP", "All Images": "<PERSON><PERSON><PERSON>", "All Networks": "<PERSON><PERSON><PERSON>", "All Port": "<PERSON><PERSON>m Ağ <PERSON>", "All Proto": "<PERSON><PERSON><PERSON>", "All QoS Policies": "<PERSON><PERSON>m QoS İlkeleri", "All TCP": "Tüm TCP", "All UDP": "Tüm UDP", "All data downloaded.": "<PERSON><PERSON><PERSON> veriler in<PERSON>.", "All network segments are indicated by \"*\", not \"0.0.0.0/0\"": "<PERSON><PERSON><PERSON> ağ segment<PERSON> \"0.0.0.0/0\" ile de<PERSON><PERSON>, \"*\" ile gösterilir", "Allocate IP": "IP Ayır", "Allocation Pools": "Dağı<PERSON><PERSON><PERSON>", "Allowed Address Pairs": "İzin Verilen Adres Çiftleri", "Allowed Host": "İzin Verilen Ana Bil<PERSON>", "Always": "<PERSON> Zaman", "American Samoa": "Amerikan Samoası", "An object with the same name already exists": "Aynı ada sahip bir nesne zaten var.", "Andorra": "Andorra", "Angola": "Angola", "Anguilla": "<PERSON><PERSON><PERSON>", "Anti-Affinity": "Anti-Afinite", "Anti-affinity (mandatory):": "Anti-Afinite (zorunlu):", "Anti-affinity (not mandatory):": "Anti-Afinite (opsiyonel):", "Antigua and Barbuda": "Antigua ve Barbuda", "Any": "Herhangi Bir", "Any(Random)": "<PERSON><PERSON><PERSON>(Rastgele)", "Application Credentials": "Uygulama <PERSON> Bilgileri", "Application Template": "Uygulama Taslağı", "Apply Latency(ms)": "Gecik<PERSON>yi <PERSON>(ms)", "Applying": "Uygulanıyor", "Arch": "<PERSON><PERSON><PERSON>", "Architecture": "<PERSON><PERSON><PERSON>", "Are you sure set the project { project } as the default project? User login is automatically logged into the default project.": "", "Are you sure to cancel transfer volume { name }? ": "Disk { name } transferini iptal etmek istediğinize emin misiniz?", "Are you sure to delete instance { name }? ": "{ name } sanal makinesini silmek istediğinize emin misiniz?", "Are you sure to delete volume { name }? ": "{ name } diskini silmek istediğinize emin misin<PERSON>?", "Are you sure to download data?": "Verileri indirmek istediğinize emin misiniz?", "Are you sure to forbidden domain { name }? Forbidden the domain will have negative effect, and users associated with the domain will not be able to log in if they are only assigned to the domain": "{ name } alan adını yasaklamak istediğinize emin misiniz? Alan adını yasakla<PERSON>k, o<PERSON>uz bir etkiye sahip olacaktır ve alan adına atanmış kullanıcılar sadece bu alan adına atanmışlarsa oturum açamayacaklardır.", "Are you sure to forbidden project { name }? Forbidden the project will have negative effect, and users associated with the project will not be able to log in if they are only assigned to the project": "{ name } projesini yasakla<PERSON>k istediğinize emin misiniz? <PERSON><PERSON>yi yasaklamak, o<PERSON>uz bir etkiye sahip olacaktır ve projeye atanmış kullanıcılar sadece bu projeye atanmışlarsa giriş yapamayacaklardır.", "Are you sure to forbidden user { name }? Forbidden the user will not allow login in ": "{ name } kullanıcısını yasaklamak istediğinize emin misiniz? Kullanıcıyı yasaklamak, kullanıcının oturum açmasına izin vermeyecektir.", "Are you sure to jump directly to the console? The console will open in a new page later.": "Do<PERSON>rudan konsola geçmek istediğinize emin misiniz? Konsol daha sonra yeni bir sayfada açılacaktır.", "Are you sure to remove the default project?": "", "Are you sure to shelve instance { name }? ": "{ name } sanal makinesini rafa kaldırmak istediğinize emin misiniz?", "Are you sure to { action } {name}?": "{ action } { name } ile devam etmek istediğinize emin misiniz?", "Are you sure to {action} (Host: {name})?": "", "Are you sure to {action} (Segment: {name})?": "", "Are you sure to {action} (instance: {name})?": "{action} i<PERSON><PERSON><PERSON> (sanal makine: { name }) gerçekleştirmek istediğinize emin misiniz?", "Are you sure to {action}?": "{action} i<PERSON><PERSON><PERSON> yapmak istediğinize emin misiniz?", "Are you sure to {action}? (Record Set: {name} - {id})": "{action} i<PERSON><PERSON>ini yapmak istediğinizden emin misiniz? (Kayıt Seti: {name} - {id})", "Are you sure to {action}? (Zone: {name})": "{action} i<PERSON><PERSON><PERSON> yapmak istediğinizden emin misiniz? (Bölge: {name})", "Argentina": "<PERSON><PERSON><PERSON><PERSON>", "Armenia": "Ermenistan", "Aruba": "Aruba", "Associate": "İlişkilendir", "Associate Floating IP": "Değişken IP İlişkilendir", "Associate IP": "IP İlişkilendir", "Associate Network": "<PERSON>ğ İ<PERSON>ş<PERSON>lendir", "Associated Ports": "", "Associated QoS Spec ID": "İlişkili QoS Spesifikasyon Kimliği", "Associated QoS Spec ID/Name": "İlişkili QoS Spesifikasyon Kimliği/Adı", "Associated Resource": "İlişkilendirilmiş Kaynak", "Associated Resource Types": "İlişkilendirilmiş Kaynak Türleri", "Associated Resources": "İlişkilendirilmiş Kaynaklar", "Associations": "İlişkilendirmeler", "Attach": "<PERSON><PERSON>", "Attach Instance": "<PERSON><PERSON>", "Attach Interface": "<PERSON><PERSON><PERSON><PERSON>", "Attach Network": "<PERSON><PERSON><PERSON>", "Attach Security Group": "Güvenlik Grubu Ekle", "Attach USB": "USB Ekle", "Attach Volume": "<PERSON><PERSON>", "Attach volume": "<PERSON><PERSON>", "Attached Device": "<PERSON><PERSON><PERSON>", "Attached To": "<PERSON><PERSON>", "Attaching": "Ekleniyor", "Attachments Info": "Eklentiler Bilgisi", "Attributes": "<PERSON><PERSON><PERSON><PERSON>", "Audited": "", "Australia": "Avustralya", "Austria": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Auth Algorithm": "Kimlik Doğrulama Algoritması", "Auth Key": "Kimlik Doğrulama Anahtarı", "Auto": "Otomatik", "Auto Healing": "Otomatik İyileştirme", "Auto Inspect": "Otomatik Kontrol", "Auto Scaling": "Otomatik Ölçeklendirme", "Auto allocate mac address": "MAC adresini otomatik ayır", "Auto scaling feature will be enabled": "Otomatik ölçeklendirme özelliği etkinleştirilecek", "Automatically Assigned Address": "Otomatik Olarak Atanan Ad<PERSON>", "Automatically repair unhealhty nodes": "Otomatik olarak iyi olmayan dü<PERSON>i onar", "Availability Zone": "Kullanılabilirlik Bölgesi", "Availability Zone Hints": "Kullanılabilirlik Bölgesi İpuçları", "Availability Zone Info": "Kullanılabilirlik Bölgesi Bilgisi", "Availability Zone Name": "Kullanılabilirlik Bölgesi Adı", "Availability Zones": "Kullanılabilirlik Bölgeleri", "Availability zone refers to a physical area where power and network are independent of each other in the same area. In the same region, the availability zone and the availability zone can communicate with each other in the intranet, and the available zones can achieve fault isolation.": "Kullanılabilirlik bö<PERSON>si, aynı alanda gücün ve ağın birbirinden bağımsız olduğu fiziksel bir alanı ifade eder. <PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>, kullanılabilirlik bölgeleri birbirleriyle intranette iletişim kurabilir ve kullanılabilir bölgeler arasında hata izolasyonu sağlayabilir.", "Available": "Mevcut", "Available Zone": "Mevcut Bölge", "Average PGs per OSD": "OSD başına ortalama PG", "Awaiting Transfer": "Transfer Bekliyor", "Azerbaijan": "Azerbaycan", "BLOCK I/O(B)": "BLOK G/Ç(B)", "Back": "<PERSON><PERSON>", "Back End": "En sona git", "Back to Home": "<PERSON><PERSON>", "Back to login page": "<PERSON><PERSON><PERSON> g<PERSON>ön", "Backend": "Arkayüz ", "Backend Name": "Arkayüz Adı", "Backing Up": "Yedekleniyor", "Backup": "<PERSON><PERSON><PERSON>", "Backup Detail": "Yedekleme <PERSON>", "Backup File": "<PERSON><PERSON>", "Backup File Location": "<PERSON><PERSON>", "Backup Mode": "<PERSON><PERSON><PERSON><PERSON>", "Backups": "<PERSON><PERSON><PERSON>", "Backups & Snapshots": "Yedekler ve Anlık Görü<PERSON>üler", "Bad Gateway (code: 502) ": "Hatalı Geçiş (kod: 502)", "Bahamas": "<PERSON><PERSON><PERSON><PERSON>", "Bahrain": "<PERSON><PERSON><PERSON><PERSON>", "BandWidth Limit Egress": "Bant Genişliği Sınırlama Çıkış", "BandWidth Limit Ingress": "Bant Genişliği Sınırlama Giriş", "Bandwidth limit": "Bant Genişliği Sınırlaması", "Bangladesh": "<PERSON><PERSON><PERSON>", "Barbados": "Barbados", "Bare Metal": "Bare Metal", "Bare Metal Enroll": "Bare Metal Kaydı", "Bare Metal Node Detail": "Bare Metal Düğüm Detayı", "Bare Metal Nodes": "Bare Metal Düğümleri", "BareMetal Parameters": "Bare Metal Parametreleri", "Base Config": "Temel <PERSON>ılandırma", "Base Info": "<PERSON><PERSON>", "Basic Parameters": "Temel Parametreler", "Batch Allocate": "Toplu Ayır", "Before deleting the project, it is recommended to clean up the resources under the project.": "", "Belarus": "<PERSON><PERSON>", "Belgium": "Belçika", "Belize": "Belize", "Benin": "Benin", "Bermuda": "Bermuda", "Bhutan": "<PERSON><PERSON>", "Big Data": "Büyük Veri", "Bind Device": "Cihazı Bağla", "Bind Device Type": "Cihaz Türünü Bağla", "Bind Resource": "Kaynağı Bağla", "Bind Resource Name": "Kaynak Adını Bağla", "Binding": "Bağlama", "Binding Groups": "Grupları Bağlama", "Binding Instance": "<PERSON><PERSON>", "Binding Profile": "Profili <PERSON>", "Binding Users": "Kullanıcıları Bağlama", "Blank Volume": "Boş Disk", "Block Device Mapping": "Blok - Aygıt Eşlemesi", "Block Migrate": "B<PERSON><PERSON>u <PERSON>", "Block Storage Services": "<PERSON><PERSON><PERSON><PERSON>metler<PERSON>", "Blocked": "Engellenmiş", "Bolivia": "<PERSON><PERSON><PERSON><PERSON>", "Boot Device": "Önyükleme Aygıtı", "Boot From Volume": "", "Boot Interface": "Önyükleme Arayüzü", "Boot Mode of BIOS": "BIOS'un Önyükleme Modu", "Bootable": "Önyüklenebilir", "Bootable Volume": "Önyüklenebilir Disk", "Bosnia and Herzegovina": "Bosna-Hersek", "Both of Frontend and Backend": "<PERSON><PERSON>ayü<PERSON>", "Botswana": "Botsvana", "Brazil": "<PERSON><PERSON><PERSON><PERSON>", "British Indian Ocean Territory": "Britanya Hint Okyanusu Toprakları", "Brunei Darussalam": "Brunei Sultanlığı", "Build": "Oluştur", "Building": "Oluşturuluyor", "Bulgaria": "Bulgaristan", "Burkina Faso": "Burkina Faso", "Burst limit": "Ek Sınır", "Burundi": "Burundi", "By default, for security reasons, application credentials are forbidden from being used for creating or destructing additional application credentials or keystone trusts. If your application credential needs to be able to perform these actions, check unrestricted.": "Varsayılan olarak, güvenlik nedeniyle uygulama kimlik bilgilerinin ek uygulama kimlik bilgileri veya kilit taşı güvenleri oluşturmak veya yok etmek için kullanılması yasaktır. Uygulama kimlik bilgilerinizin bu eylemleri gerçekleştirebilmesi gerekiyorsa sınırsız seçeneğini işaretleyin.", "CA Certificate": "CA Sertifikası", "CA Certificates": "CA Sertifikaları", "CHECK COMPLETE": "KONTROL TAMAMLANDI", "CIDR": "CIDR", "CIDR Format Error(e.g. ***********/24, 2001:DB8::/48)": "CIDR Format Hatası(örn. ***********/24, 2001:DB8::/48)", "CIFS": "CIFS", "CMD": "CMD", "COE": "COE", "COE Version": "COE Sürümü", "CPU": "CPU", "CPU %": "CPU %", "CPU (Core)": "CPU (Çekirdek)", "CPU Arch": "CPU Mimarisi", "CPU Cores": "CPU Çekirdekleri", "CPU Policy": "CPU İlkeleri", "CPU Thread Policy": "CPU İş Parçacığı İlkeleri", "CPU Usage(%)": "CPU Kullanımı(%)", "CPU Usages (Core)": "CPU Kullanımı (Çekirdek)", "CPU value is { cpu }, NUMA CPU value is { totalCpu }, need to be equal. ": "CPU değeri { cpu } ve NUMA CPU değeri { totalCpu } eşit olmalıdır.", "CPU(Core)": "CPU(Çekirdek)", "CREATE COMPLETE": "OLUŞTURMA TAMAMLANDI", "CREATE FAILED": "OLUŞTURMA BAŞARISIZ", "CREATE IN PROGRESS": "OLUŞTURMA SÜRECİNDE", "Cache Service": "Önbellek Servisi", "Cameroon": "Kamerun", "Can add { number } {name}": "{ number } {name} e<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Canada": "Ka<PERSON><PERSON>", "Cancel": "İptal", "Cancel Download": "İndirmeyi İptal Et", "Cancel Select": "Seçimi İptal Et", "Cancel Transfer": "Transferi İptal Et", "Cancel download successfully.": "İndirme başarıyla iptal edildi.", "Cancel upload successfully.": "<PERSON><PERSON><PERSON>me başarıyla iptal edildi.", "Canonical Name Record": "Kanonik Ad Kaydı", "Capacity & Type": "Kapasite & Tür", "Capacity (GiB)": "Kapasite (GiB)", "Cape Verde": "<PERSON><PERSON><PERSON>", "Capsule Detail": "Kapsül Detayı", "Capsule Type": "Kaps<PERSON>l Türü", "Capsules": "<PERSON><PERSON><PERSON><PERSON>", "Cascading deletion": "<PERSON><PERSON><PERSON><PERSON> silme", "Cast Rules To Read Only": "Salt Okunur Kurallar Belirle", "Category": "<PERSON><PERSON><PERSON>", "Cayman Islands": "Cayman Adaları", "CentOS": "CentOS", "Central African Republic": "Orta Afrika Cumhuriyeti", "CephFS": "CephFS", "Cephx": "Cephx", "Cert": "Ser<PERSON><PERSON><PERSON>", "Certificate Authority Authorization Record": "Sertifika Yetkilisi Yetkilendirme Kaydı", "Certificate Content": "Sertifika İçeriği", "Certificate Detail": "Sertifika Detayı", "Certificate Name": "Sertifika Adı", "Certificate Type": "Sertifika <PERSON>ü<PERSON>", "Certificates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Chad": "Çad", "Change Password": "Parolayı Değiştir", "Change Type": "<PERSON><PERSON><PERSON><PERSON>", "Change password": "Parolayı değiştir", "Change type": "<PERSON><PERSON><PERSON><PERSON>", "Changed Node Count": "Değiştirilen Düğüm Sayısı", "Channel": "<PERSON><PERSON>", "Chassis ID": "<PERSON><PERSON>", "Check Can Live Migrate Destination": "Canlı Taşınabilir Hedefi Kontrol Et", "Check Can Live Migrate Source": "Canlı Taşınabilir Kaynağı Kontrol Et", "Check Complete": "<PERSON><PERSON><PERSON>", "Check Failed": "Ko<PERSON>rol <PERSON>ı<PERSON>", "Check In Progress": "<PERSON><PERSON><PERSON>", "Checksum": "<PERSON><PERSON><PERSON>", "Chile": "Şili", "China": "<PERSON><PERSON>", "Choose a Network Driver": "Bir Ağ Sürücüsü Seçin", "Choose a host to live migrate instance to. If not selected, the scheduler will auto select target host.": "Canlı taşınabilir sanal makineyi taşımak için bir ana bilgisayar seçin. Seçilmediyse, zamanlayıcı otomatik olarak hedef ana bilgisayar seçecektir.", "Choose a host to migrate instance to. If not selected, the scheduler will auto select target host.": "Sanal makineyi taşımak için bir ana bilgisayar seçin. Seçilmediyse, zamanlayıcı otomatik olarak hedef ana bilgisayar seçecektir.", "Choosing a QoS policy can limit bandwidth and DSCP": "Bir QoS ilkesi seçmek bant genişliğini ve DSCP'yi sı<PERSON>ı<PERSON>ırabilir", "Christmas Island": "Christmas Adası", "Cidr": "CIDR", "Cinder Service": "<PERSON><PERSON>", "Cipher": "Şifreleme", "Clean Failed": "Temizleme Başarısız", "Clean Wait": "<PERSON><PERSON><PERSON><PERSON>", "Cleaning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Clear Gateway": "<PERSON><PERSON> G<PERSON>ç<PERSON>", "Clear selected": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "Click To View": "Görüntülemek için Tıklayın", "Click here for filters.": "Filtreler için buraya tıklayın.", "Click to Upload": "Yüklemek için Tıkla", "Click to show detail": "Detayları göstermek için tıklayın", "Clone Volume": "<PERSON><PERSON>", "Clone volume": "Diski klonla", "Close": "Ka<PERSON><PERSON>", "Close External Gateway": "<PERSON><PERSON><PERSON> Geçidini <PERSON>", "Close all notifications.": "<PERSON><PERSON><PERSON> bildiri<PERSON> kapat.", "Close external gateway": "<PERSON><PERSON>i ağ geçidini kapat", "Cloud": "Bulut", "Cloud Container Engine": "Bulut Konteyner Motoru", "Cluster Detail": "<PERSON><PERSON><PERSON>", "Cluster Distro": "<PERSON><PERSON><PERSON>ım<PERSON>", "Cluster Info": "<PERSON><PERSON><PERSON>", "Cluster Management": "<PERSON><PERSON><PERSON>", "Cluster Name": "<PERSON><PERSON><PERSON>", "Cluster Network": "<PERSON><PERSON><PERSON>", "Cluster Template": "<PERSON><PERSON><PERSON>", "Cluster Template Detail": "<PERSON><PERSON>me Taslak Detayı", "Cluster Template Name": "<PERSON><PERSON><PERSON> Taslak Adı", "Cluster Templates": "<PERSON><PERSON><PERSON>", "Cluster Type": "<PERSON><PERSON><PERSON>", "Clusters": "<PERSON><PERSON><PERSON><PERSON>", "Clusters Management": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cocos (Keeling) Islands": "Cocos (Keeling) Adaları", "Code": "Kod", "Cold Migrate": "<PERSON><PERSON><PERSON>", "Colombia": "Kolombiya", "Command": "<PERSON><PERSON><PERSON>", "Command to run to check health": "Durumunu kontrol etmek için çalıştırılacak komut", "Command was successfully executed at container {name}.": "<PERSON><PERSON><PERSON> başarı<PERSON> {name} konteynerında çalıştırıldı.", "Commas ‘,’ are not allowed to be in a tag name in order to simplify requests that specify lists of tags": "Etiket listesi belirleyen istekleri basitleştirmek için etiket adında virgül ‘,’ kullanılamaz.", "Commit Latency(ms)": "<PERSON><PERSON>(ms)", "Common Server": "Ortak Sunucu", "Comoros": "<PERSON><PERSON><PERSON>", "Compute": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Compute Hosts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Compute Live Migration": "Canlı Taşınabilir Hesaplama", "Compute Live Resize Instance": "Canlı Boyutlandırma Sanal Makine Hesapla", "Compute Node status": "Hesaplama Düğü<PERSON>", "Compute Optimized": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Compute Optimized Info": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Compute Optimized Type": "<PERSON><PERSON><PERSON><PERSON><PERSON>ti<PERSON>", "Compute Optimized Type with GPU": "GPU ile Hesaplama Optimizasyonlu Tür", "Compute Pause Instance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Compute Reboot Instance": "Hesaplama Sanal Makinesini Yeniden <PERSON>lat", "Compute Resume Instance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Compute Service": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Compute Services": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Compute Start Instance": "Hesaplama Sanal Makinesini <PERSON>", "Compute Stop Instance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Compute Suspend Instance": "Hesaplama Sanal Makinesini Askıya Al", "Compute Unpause Instance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Conductor Live Migrate Instance": "İşlem Yöneticisi Canlı Taşınabilir Sanal Makinesi", "Conductor Live Resize Instance": "İşlem Yöneticisi Canlı Boyutlandırma Sanal Makinesini", "Conductor Migrate Server": "İşlem Yöneticisi Sunucu <PERSON>", "Config Overview": "Yapılandırma Genel Bakış", "Configuration": "Yapılandırma", "Configuration Detail": "Yapılandırma Detayı", "Configuration Group": "Yapılandırma Grubu", "Configuration Group ID/Name": "Yapılandırma Grubu ID/Adı", "Configuration Groups": "Yapılandırma Grupları", "Configuration Update": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Configured Disk (GiB)": "Yapılandırılmış Disk (GiB)", "Configured Memory (GiB)": "Yapılandırılmış Bellek (GiB)", "Confirm": "<PERSON><PERSON><PERSON>", "Confirm Config": "Yapılandırmayı Onayla", "Confirm Password": "Parolayı Onayla", "Confirm Resize or Migrate": "Boyutlandırmayı veya Taşımayı Onayla", "Confirm Shared Key": "Paylaşılan Anahtarı Onayla", "Confirming Resize or Migrate": "Boyutlandırmayı veya Taşımayı Onaylama", "Connect Subnet": "Alt Ağı Bağla", "Connect router": "Yönlendirici Bağla", "Connected Threads": "Bağlı İş Parçacıkları", "Connection Examples": "Bağlantı Sanal makineleri", "Connection Information": "Bağlantı Bilgisi", "Connection Limit": "Bağlantı Sınırı", "Consecutive failures needed to report unhealthy": "İyi olmayan raporlama için ardışık başarısızlık sayısı", "Console": "Ko<PERSON><PERSON>", "Console Interface": "Konsol Arayüzü", "Console Log": "Konsol Günlüğü", "Consumer": "Tüketici", "Container": "Konteyner", "Container Creating": "Konteyner <PERSON>r", "Container Deleting": "<PERSON><PERSON><PERSON><PERSON>", "Container Detail": "Konteyner <PERSON>", "Container Format": "Konteyner B<PERSON>i", "Container Killing": "Konteyner Sonlandırılıyor", "Container Name": "Konteyner <PERSON>", "Container Pausing": "<PERSON><PERSON><PERSON><PERSON>", "Container Rebooting": "Konteyner Yeniden Başlatılıyor", "Container Rebuilding": "Konteyner Yeniden Oluşturuluyor", "Container Restarting": "Konteyner Yeniden Başlatılıyor", "Container Starting": "Konteyner Başlatılıyor", "Container Status": "<PERSON><PERSON><PERSON><PERSON>", "Container Stopping": "<PERSON><PERSON><PERSON><PERSON>", "Container Unpausing": "<PERSON><PERSON><PERSON><PERSON>", "Container Version": "Konteyner <PERSON>", "Containers": "Konteynerler", "Containers CPU": "Konteynerler CPU", "Containers Disk (GiB)": "Konteynerler Disk (GiB)", "Containers Info": "Ko<PERSON>yne<PERSON><PERSON>", "Containers Management": "<PERSON><PERSON><PERSON><PERSON>", "Containers Memory (MiB)": "Ko<PERSON><PERSON><PERSON><PERSON> (MiB)", "Content": "İçerik", "Content Type": "İçerik Türü", "Continue": "<PERSON><PERSON>", "Continue Upload": "Yüklemeye Devam Et", "Control Attribute": "", "Control Attributes": "", "Control Location": "<PERSON><PERSON><PERSON>", "Cook Islands": "<PERSON>", "Copy": "Kopyala", "Copy File": "Dosyayı Kopyala", "CoreOS": "CoreOS", "Costa Rica": "<PERSON><PERSON>", "Cote D'Ivoire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Count": "<PERSON><PERSON>", "Crashed": "Çöktü", "Create": "Oluştur", "Create Allowed Address Pair": "İzin Verilen Adres Çifti Oluştur", "Create Application Credentials": "Uygulama Kimlik Bilgileri Oluştur", "Create Backup": "<PERSON><PERSON>", "Create Bandwidth Limit Rule": "Bant Genişliği Sınır Kuralı Oluştur", "Create Bare Metal Node": "Bare Metal Düğümü Oluştur", "Create Capsule": "Kapsül Oluştur", "Create Certificate": "Sertifika Oluştur", "Create Cluster": "<PERSON><PERSON><PERSON>", "Create Cluster Template": "<PERSON><PERSON><PERSON> Taslağı Oluştur", "Create Complete": "Oluşturma Tamamlandı", "Create Configurations": "Yapılandırmalar Oluştur", "Create Container": "Konteyner <PERSON>", "Create DSCP Marking Rule": "DSCP İşaretleme Kuralı Oluştur", "Create Database": "Veritabanı Oluştur", "Create Database Backup": "Veritabanı Yedeklemesi Oluştur", "Create Database Instance": "Veritabanı Sanal Makinesi Oluştur", "Create Default Pool": "Varsayılan Havuz Oluştur", "Create Domain": "Etki Alanı Oluştur", "Create Encryption": "Şifreleme Oluştur", "Create Extra Spec": "Ek Özellik Oluştur", "Create Failed": "Oluşturma Başarısız", "Create Firewall": "", "Create Firewall Policy": "", "Create Flavor": "Şablon Oluştur", "Create Folder": "Klasör <PERSON>", "Create Host Aggregate": "<PERSON>", "Create IPsec Site Connection": "IPsec Site Bağlantısı Oluştur", "Create Image": "<PERSON><PERSON>j <PERSON>", "Create In Progress": "Oluşturma Devam Ediyor", "Create Instance": "<PERSON><PERSON> Ma<PERSON>", "Create Instance Snapshot": "Sanal Makine Anlık Görüntü Oluştur", "Create Ironic Instance": "İronik Sanal Makine Oluştur", "Create Keypair": "<PERSON><PERSON><PERSON>", "Create Listener": "Dinleyici Oluştur", "Create Loadbalancer": "Yük Dengeleyici Oluştur", "Create Network": "<PERSON>ğ <PERSON>", "Create New Network": "Yeni Ağ Oluştur", "Create Node": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Create Policy": "", "Create Port": "Ağ Adaptörü Oluştur", "Create Port Forwarding": "Ağ Adaptörü Yönlendirme Oluştur", "Create Port Group": "Ağ Adaptörü Grubu Oluştur", "Create Project": "<PERSON><PERSON>", "Create QoS Policy": "QoS İlkesi <PERSON>", "Create QoS Spec": "QoS Belirlemesi Oluştur", "Create RBAC Policy": "", "Create Record Set": "Kayıt Kümesi Oluştur", "Create Role": "Rol Oluştur", "Create Router": "Yönlendirici Oluştur", "Create Rule": "Kural Oluştur", "Create Security Group": "Güvenlik Grubu Oluştur", "Create Segment": "", "Create Server Group": "Sunucu Grubu <PERSON>", "Create Share": "Paylaşım Oluştur", "Create Share Group": "Paylaşım Grubu Oluştur", "Create Share Group Type": "Paylaşım Grubu Türü Oluştur", "Create Share Metadata": "Paylaşım Meta Verisi Oluştur", "Create Share Network": "Paylaşım Ağı Oluştur", "Create Share Type": "Paylaşım Türü Oluştur", "Create Snapshot": "Anlık Görüntü Oluştur", "Create Stack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Create Static Route": "Statik Rota Oluştur", "Create Subnet": "Alt Ağ Oluştur", "Create Time": "Oluşturma Zamanı", "Create Transfer": "Transfer Oluştur", "Create Type": "<PERSON><PERSON><PERSON>", "Create User": "Kullanıcı Oluştur", "Create User Group": "Kullanıcı Grubu Oluştur", "Create VPN": "VPN Oluştur", "Create VPN Endpoint Group": "VPN Uç Nokta Grubu Oluştur", "Create VPN IKE Policy": "VPN IKE İlkesi Oluştur", "Create VPN IPsec Policy": "VPN IPsec İlkesi Oluştur", "Create Virtual Adapter": "<PERSON><PERSON> Adaptör <PERSON>", "Create Volume": "Disk Oluştur", "Create Volume Backup": "Disk Yedeklemesi Oluştur", "Create Volume Snapshot": "Disk Anlık Görüntüsü Oluştur", "Create Volume Type": "Disk Türü Oluştur", "Create Zone": "<PERSON><PERSON><PERSON>", "Create a full backup, the system will automatically create a new backup chain, the full backup name is the backup chain name; Create an incremental backup, the system will automatically create an incremental backup under the newly created backup chain.": "Tam bir yedek<PERSON>e oluşturun, sistem otomatik olarak yeni bir yedekleme zinciri oluşturacaktır, tam yedeklemenin adı yedekleme zinciri adıdır; Artan bir yedek<PERSON>e oluşturun, sistem otomatik olarak yeni oluşturulan yedekleme zinciri altında artan bir yedekleme oluşturacaktır.", "Create firewall": "", "Create host aggregate": "<PERSON> bil<PERSON><PERSON>", "Create image": "İ<PERSON>j <PERSON>", "Create instance": "<PERSON><PERSON>", "Create ironic instance": "İronik sanal makine oluştur", "Create new AZ": "Yeni AZ oluştur", "Create rule": "Kural oluştur", "Create security group": "Güvenlik grubu oluştur", "Create server group": "<PERSON><PERSON><PERSON> g<PERSON>", "Create static route": "Statik rota oluştur", "Create volume": "Disk oluştur", "Create volume backup": "Disk yedeklemesi oluştur", "Created": "<PERSON><PERSON>ş<PERSON><PERSON><PERSON>", "Created At": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Created Time": "Oluşturulma Zamanı", "Created Volumes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Creating": "Oluşturuluyor", "Creating From Snapshot": "Anlık Görüntüden Oluşturuluyor", "Creation Timeout (Minutes)": "Oluşturma Zaman Aşımı (Dakika)", "Credential Type": "Kimlik Bilgisi Türü", "Croatia (local name: Hrvatska)": "Hırvatistan (yerel ad: Hrvatska)", "Cuba": "Küba", "Current Availability Zones": "Mevcut Kullanılabilirlik Bölgeleri", "Current Compute Host": "<PERSON><PERSON><PERSON>", "Current Connections": "Mevcut Bağlantılar", "Current Disk (GiB)": "Mevcut Disk (GiB)", "Current Flavor": "<PERSON><PERSON><PERSON>", "Current Host": "<PERSON><PERSON><PERSON>", "Current Interface": "Mevcut <PERSON>", "Current Master Node Count": "Mevcut Ana <PERSON>ü<PERSON>ü<PERSON>", "Current Node Count": "Mevcut Düğüm <PERSON>", "Current Password": "<PERSON><PERSON><PERSON>", "Current Path: ": "Mevcut Yol: ", "Current Project": "<PERSON><PERSON><PERSON>", "Current Project Images": "Mevcut Proje <PERSON>ı", "Current Project Networks": "Mevcut Proje Ağları", "Current Project QoS Policies": "Mevcut Proje QoS İlkeleri", "Current QoS policy name": "Mevcut QoS ilkesi adı", "Current Rules": "", "Current Status": "Mevcut Durum", "Current Storage Backend": "Mevcut Depol<PERSON> Arkayüzü", "Current data downloaded.": "Mevcut veriler indirildi.", "Custom": "<PERSON><PERSON>", "Custom Headers": "", "Custom ICMP Rule": "Özel ICMP Kuralı", "Custom Metadata": "<PERSON><PERSON>", "Custom Properties Info": "<PERSON>zel Özellik Bilgisi", "Custom TCP Rule": "Özel TCP Kuralı", "Custom Trait": "<PERSON><PERSON>", "Custom UDP Rule": "Özel UDP Kuralı", "Cut": "<PERSON><PERSON><PERSON>", "Cut File": "Dosyayı Kes", "Cyprus": "Kıbrıs", "Czech Republic": "Çek Cumhuriyeti", "DC/OS": "DC/OS", "DCCP": "DCCP", "DEGRADED: One or more of the entity’s components are in ERROR": "BOZULDU: Bir veya daha fazla varlık bileşeninde HATA var.", "DELETE COMPLETE": "SİLME TAMAMLANDI", "DELETE FAILED": "SİLME BAŞARISIZ", "DELETE_IN PROGRESS": "SİLME DEVAM EDİYOR", "DENY": "", "DHCP": "DHCP", "DHCP Agent": "DHCP Ajanı", "DHCP Agents": "DHCP Ajanları", "DISK IOPS": "DİSK IOPS", "DISK Usage(%)": "DİSK Kullanımı(%)", "DNS": "DNS", "DNS Assignment": "DNS Atama", "DNS Name": "DNS Adı", "DNS Nameservers": "DNS İsim Sunucuları", "DNS Reverse": "Ters DNS", "DNS Zones": "DNS Bölgeleri", "DNS Zones Detail": "DNS Bölgeleri Detayları", "DPD Action": "DPD Eylemi", "DPD Interval (sec)": "DPD Zaman Aralığı (sn)", "DPD actions controls the use of Dead Peer Detection Protocol.": "DPD Eylemi, ÖLü Nokta Tespiti (DPD) protokülünün kullanımını kontrol eder.", "DPD timeout (sec)": "DPD zaman aşımı (sn)", "DRAINING: The member is not accepting new connections": "BOŞALTMA: <PERSON><PERSON>, yeni bağlantıları kabul etmiyor.", "DSCP Marking": "DSCP İşaretleme", "Danger": "<PERSON><PERSON><PERSON>", "Data Disk": "<PERSON><PERSON>", "Data Disks": "<PERSON><PERSON>", "Data Protection": "<PERSON><PERSON>", "Data Source Type": "Veri Kay<PERSON>", "Database": "Veri Tabanı", "Database Backup Detail": "Veri Tabanı Yedekleme Detayı", "Database Disk (GiB)": "Veri Tabanı Disk (GiB)", "Database Flavor": "Veri Tabanı Şablonu", "Database Instance": "Veri Tabanı Sanal Makinesi", "Database Instance Detail": "Veri Tabanı Sanal Makine Detayları", "Database Instance Name": "Veri Tabanı Sanal Makine Adı", "Database Instance Status": "Veri Tabanı Sanal Makine Durumu", "Database Instances": "Veri Tabanı Sanal Makineleri", "Database Name": "Veri Tabanı Adı", "Database Port": "Veri Tabanı Ağ Adaptörü", "Database Service": "Veri Tabanı Hizmeti", "Databases": "Veri <PERSON>ları", "Datastore": "<PERSON><PERSON>", "Datastore Type": "<PERSON><PERSON>", "Datastore Version": "<PERSON><PERSON>", "Deactivated": "<PERSON><PERSON><PERSON>", "Debian": "Debian", "Dedicated": "Ayrılmış", "Default Policy": "Varsayılan İlke", "Default Project": "", "Default Project ID/Name": "", "Default is slaac, for details, see https://docs.openstack.org/neutron/latest/admin/config-ipv6.html": "<PERSON><PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON><PERSON>tı<PERSON>. Detaylar için https://docs.openstack.org/neutron/latest/admin/config-ipv6.html adresine bakın.", "Defaults": "Varsayılanlar", "Defines the admin state of the health monitor.": "Durum denetleyicinin yönetici durumunu tanımlar.", "Defines the admin state of the listener.": "Dinleyicinin yönetici durumunu tanı<PERSON>lar.", "Defines the admin state of the pool.": "Havuzun yönetici durumunu tanımlar.", "Defines the admin state of the port.": "<PERSON><PERSON>ünün yönetici durumunu tanımlar.", "Degraded": "Bozuldu", "Delay Interval(s)": "Gecikme <PERSON>lığı (saniye)", "Delete": "Sil", "Delete Allowed Address Pair": "İzin Verilen Adres Çiftini Sil", "Delete Application Credential": "Uygulama Kimlik Bilgisini <PERSON>", "Delete Bandwidth Egress Rules": "Bant Genişliği Çıkış Kurallarını Sil", "Delete Bandwidth Ingress Rules": "Bant Genişliği Giriş Kurallarını Sil", "Delete Capsule": "Kapsülü Sil", "Delete Certificate": "Sertifikayı Sil", "Delete Cluster": "<PERSON><PERSON><PERSON>", "Delete Cluster Template": "<PERSON><PERSON><PERSON>slağını Sil", "Delete Complete": "<PERSON><PERSON><PERSON>", "Delete Configuration": "Yapılandırmayı Sil", "Delete Container": "<PERSON><PERSON><PERSON><PERSON>", "Delete DSCP Marking Rules": "DSCP İşaretleme Kurallarını Sil", "Delete Database": "Veritabanını Sil", "Delete Database Backup": "Veritabanı Yedeklemesini Sil", "Delete Default Pool": "Varsayılan Havuzu Sil", "Delete Domain": "Etki Alanını Sil", "Delete Encryption": "Şifrelemeyi Sil", "Delete Extra Specs": "Ek Özellikleri Sil", "Delete Failed": "<PERSON><PERSON><PERSON>", "Delete File": "Dosyayı Sil", "Delete Firewall": "", "Delete Flavor": "Şablonu Sil", "Delete Folder": "Klasörü Sil", "Delete Group": "Grubu <PERSON>", "Delete Host Aggregate": "<PERSON>", "Delete IPsec Site Connection": "IPsec Site Bağlantısını Sil", "Delete Image": "İmajı Sil", "Delete In Progress": "<PERSON><PERSON><PERSON>", "Delete Instance": "<PERSON><PERSON>", "Delete Instance Snapshot": "Sanal Makine Anlık Görüntüsünü Sil", "Delete Keypair": "<PERSON><PERSON><PERSON>", "Delete Listener": "<PERSON><PERSON><PERSON><PERSON>", "Delete Load Balancer": "Yük Dengeleyiciyi Sil", "Delete Member": "Üyeyi Sil", "Delete Metadata": "<PERSON><PERSON>", "Delete Network": "Ağı Sil", "Delete Node": "Düğümü Sil", "Delete Policy": "", "Delete Port": "<PERSON>ğ <PERSON>ptörünü Sil", "Delete Port Forwarding": "Ağ Adaptörünü Yönlendirmesini Sil", "Delete Port Group": "Ağ Adaptörünü Grubunu Sil", "Delete Project": "<PERSON><PERSON><PERSON>", "Delete QoS Policy": "QoS İlkesini Sil", "Delete QoS Spec": "QoS Belirlemesini Sil", "Delete RBAC Policy": "", "Delete Record Set": "<PERSON><PERSON><PERSON>", "Delete Role": "<PERSON><PERSON><PERSON>", "Delete Router": "Yönlendiriciyi Sil", "Delete Rule": "Kuralı Sil", "Delete Security Group": "Güvenlik Grubunu Sil", "Delete Server Group": "<PERSON><PERSON><PERSON>", "Delete Share": "Paylaşımı Sil", "Delete Share Access Rule": "Paylaşım Erişim <PERSON>l", "Delete Share Group": "Paylaşım Grubunu Sil", "Delete Share Group Type": "Paylaşım Grubu Türünü Sil", "Delete Share Metadata": "Paylaşım Meta Verisini Sil", "Delete Share Network": "Paylaşım Ağını Sil", "Delete Share Server": "Paylaşım Sunuc<PERSON>u Sil", "Delete Share Type": "Paylaşım Türünü Sil", "Delete Static Route": "Statik Rota Sil", "Delete Subnet": "Alt Ağı Sil", "Delete User": "Kullanıcıyı Sil", "Delete VPN": "VPN'i Sil", "Delete VPN EndPoint Groups": "VPN Uç Nokta Gruplarını Sil", "Delete VPN IKE Policy": "VPN IKE İlkesini Sil", "Delete VPN IPsec Policy": "VPN IPsec İlkesini Sil", "Delete Virtual Adapter": "Sanal Adaptörü <PERSON>", "Delete Volume": "Diski Sil", "Delete Volume Backup": "Disk Yedeklemesini Sil", "Delete Volume Snapshot": "Disk Anlık Görüntüsünü Sil", "Delete Volume Type": "Disk Türünü Sil", "Delete Volume on Instance Delete": "", "Delete Zone": "<PERSON><PERSON><PERSON><PERSON>", "Delete metadata": "", "Deleted": "<PERSON><PERSON><PERSON>", "Deleted At": "<PERSON><PERSON><PERSON>", "Deleted with the instance": "<PERSON><PERSON> ma<PERSON><PERSON> birl<PERSON> silindi", "Deleting": "Silini<PERSON><PERSON>", "Deleting this stack will delete all resources deployed by the stack.": "Bu yığı<PERSON><PERSON> silmek, yığın tarafından dağıtılan tüm kaynakları silecektir.", "Democratic Republic of the Congo": "Kongo Demokratik Cumhuriyeti", "Denmark": "Danimark<PERSON>", "Denying": "Reddedilme", "Deploy Failed": "Dağıtım Başarısız", "Deploy Wait": "Dağıtım Bekliyor", "Deploying": "Dağ<PERSON><PERSON><PERSON><PERSON>or", "Deployment Parameters": "Dağıtım Parametreleri", "Description": "<PERSON><PERSON>ı<PERSON><PERSON>", "Dest Folder": "<PERSON><PERSON><PERSON>", "Destination": "<PERSON><PERSON><PERSON>", "Destination CIDR": "Hedef CIDR", "Destination IP": "", "Destination IP Address/Subnet": "", "Destination Port": "", "Destination Port/Port Range": "Hedef Ağ Adaptörü / Ağ Adaptör Aralığı", "Detach": "Ayrıştır", "Detach Instance": "Sanal Makineyi Ayrıştır", "Detach Interface": "Arayüzü Ayrıştır", "Detach Network": "Ağı Ayrıştır", "Detach Security Group": "Güvenlik Grubunu Ayrıştır", "Detach Volume": "Diski Ayrıştır", "Detach interface": "Arayüzü Ayrıştır", "Detaching": "Ayrıştırılıyor", "Detail": "Detay", "Detail Info": "<PERSON><PERSON> Bilgi", "Details": "Detaylar", "Details *": "Detaylar *", "Details about the PTR record.": "PTR kaydıyla ilgili detaylar.", "Device": "", "Device ID": "<PERSON><PERSON><PERSON>", "Device ID/Name": "", "Device Owner": "<PERSON><PERSON><PERSON>", "Devicemapper": "Cihaz Haritalayıcı", "Direct": "<PERSON><PERSON><PERSON><PERSON>", "Direction": "<PERSON><PERSON><PERSON>", "Disable": "Devre Dışı Bırak", "Disable Cinder Service": "<PERSON>inder Servisini Devre Dışı Bırak", "Disable Compute Host": "Hesaplama Ana Bilgisayarını Devre Dışı Bırak", "Disable Compute Service": "Hesaplama Servisini Devre Dışı Bırak", "Disable Gateway": "Ağ Geçidini Devre Dışı Bırak", "Disable Neutron Agent": "Neutron Ajanını Devre Dışı Bırak", "Disable SNAT": "SNAT Devre Dışı Bırak", "Disable TLS": "TLS'yi Devre Dışı Bırak", "Disable compute host": "Hesaplama ana bilgisayarını devre dışı bırak", "Disabled": "Devre Dışı", "Disabling port security will turn off the security group policy protection and anti-spoofing protection on the port. General applicable scenarios: NFV or operation and maintenance Debug.": "Ağ adaptörü güvenliğinin devre dışı bırakılması, ağ adaptöründeki güvenlik grubu ilkesi korumasını ve sahtecilik korumasını kapatacaktır. Genel geçerli senaryolar: NFV veya çalıştırma ve bakım hata ayıklama.", "Disabling the project will have a negative impact. If the users associated with the project are only assigned to the project, they will not be able to log in": "Projeyi devre dışı bırakmanın olumsuz etkisi olacaktır. Projeye bağlı kullanıcılar sadece projeye atanmışsa giriş yapamayacaklardır.", "Disassociate": "İlişkisini Kes", "Disassociate Floating IP": "Değişken IP İlişkisini Kes", "Disassociate Floating Ip": "Değişken IP İlişkisini Kes", "Disconnect Subnet": "Alt Ağı Bağlantısını Kes", "Discovery URL": "<PERSON><PERSON><PERSON>", "Disk": "Disk", "Disk (GiB)": "Disk (GiB)", "Disk Format": "Disk Formatı", "Disk Info": "Disk Bilgisi", "Disk Tag": "Disk Etiketi", "Disk allocation (GiB)": "Disk tahsisi (GiB)", "Disk size is limited by the min disk of flavor, image, etc.": "Disk boyutu; <PERSON><PERSON><PERSON><PERSON>, imaj<PERSON>n vb.'nin minimum diski tarafından sınırlanır.", "Djibouti": "Cibuti", "Do Build And Run Instance": "Sanal Makine Oluştur ve Çalıştır", "Do HH:mm": "Saat:<PERSON><PERSON><PERSON> yap", "Do not reset the normally mounted volume to the \"available\"、\"maintenance\" or \"error\" status. The reset state does not remove the volume from the instance. If you need to remove the volume from the instance, please go to the console of the corresponding project and use the \"detach\" operation.": "Normal olarak bağlanan diski \"kullanılabilir\", \"bakım\" veya \"hata\" durumunda sıfırlamayın. Sıfırlama durumu diski sanal makineden kaldırmaz. Diski sanal makineden kaldırmanız gereki<PERSON>, lütfen ilgili projenin konsoluna gidin ve \"ayrıştırma\" işlemini kullanın.", "Do not set with a backend": "Arkayüz ile ayarlamayın", "Docker": "<PERSON>er", "Docker Hub": "<PERSON><PERSON>", "Docker Storage Driver": "<PERSON><PERSON> <PERSON><PERSON>", "Docker Swarm": "Docker Swarm", "Docker Swarm Mode": "<PERSON><PERSON> Swarm Modu", "Docker Volume Size (GiB)": "<PERSON><PERSON> (GiB)", "Domain": "<PERSON>", "Domain Detail": "<PERSON>", "Domain ID": "", "Domain ID/Name": "Alan <PERSON>ı ID/Adı", "Domain Manager": "<PERSON>", "Domain Name": "<PERSON>", "Domain name ending in.": "", "Domains": "<PERSON>", "Dominica": "Dominika", "Down": "Aşağı", "Download": "<PERSON><PERSON><PERSON>", "Download File": "Dosyayı İndir", "Download Image": "<PERSON><PERSON><PERSON>", "Download all data": "<PERSON><PERSON><PERSON> veril<PERSON> indir", "Download canceled!": "İndirme iptal edildi!", "Download current data": "Geçerli verileri indir", "Download progress": "<PERSON><PERSON><PERSON><PERSON>", "Downloading": "İndiriliyor", "Draining": "Boşaltılıyor", "Driver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Driver Handles Share Servers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Paylaşım Sunucularını Yönetir", "Driver Info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>il<PERSON>", "Driver Interface": "Sürücü Arayüzü", "Duplicate tag name: {tag}": "Yinelenen etiket adı: {tag}", "EGP": "EGP", "ENTRYPOINT": "GİRİŞ NOKTASI", "ESP": "ESP", "Each instance belongs to at least one security group, which needs to be specified when it is created. Instances in the same security group can communicate with each other on the network, and instances in different security groups are disconnected from the internal network by default.": "Her sanal makine en az bir güvenlik grubuna aittir ve oluşturulduğunda belirtilmelidir. Aynı güvenlik grubundaki sanal makineler birbirleriyle iletişim kurabilir ve farklı güvenlik gruplarındaki sanal makineler varsayılan olarak dahiliağdan bağlantıları kesilir.", "Each new connection request is assigned to the next server in order, and all requests are finally divided equally among all servers. Commonly used for short connection services, such as HTTP services.": "Her yeni bağlantı isteği sırayla bir sonraki sunucuya atanır ve tüm istekler sonunda tüm sunucular arasında eşit olarak bölünür. HTTP gibi kısa bağlantı hizmetleri için yaygın olarak kullanılır.", "Each server can have up to 50 tags": "Her sunucu en fazla 50 etikete sahip olabilir", "East Timor": "Doğu Timor", "Ecuador": "Ekvador", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Edit Bandwidth Egress Limit Rule": "Bant Genişliği Çıkış Limit Kuralını Düzenle", "Edit Bandwidth Ingress Limit Rule": "Bant Genişliği Giriş Limit Kuralını Düzenle", "Edit Bare Metal Node": "Bare Metal Düğümünü Düzenle", "Edit Consumer": "Tüketiciyi Düzenle", "Edit Container": "<PERSON><PERSON><PERSON><PERSON>", "Edit DSCP Marking Rule": "DSCP İşaretleme Kuralını Düzenle", "Edit Default Pool": "Varsayılan Havuzu <PERSON>le", "Edit Domain": "<PERSON>", "Edit Domain Permission": "Alan Adı İzni Düzenle", "Edit Extra Spec": "Ek Özellikleri Düzenle", "Edit Flavor": "Şablonu <PERSON>", "Edit Health Monitor": "<PERSON><PERSON><PERSON>", "Edit Host Aggregate": "<PERSON>", "Edit IPsec Site Connection": "IPsec Site Bağlantısını Düzenle", "Edit Image": "İmajı Düzenle", "Edit Instance": "<PERSON><PERSON>", "Edit Instance Snapshot": "Sanal Makine Anlık Görüntüsünü Düzenle", "Edit Listener": "<PERSON><PERSON><PERSON><PERSON>", "Edit Load Balancer": "Yük Dengeleyiciyi Düzenle", "Edit Member": "Üyeyi Düzenle", "Edit Metadata": "<PERSON><PERSON>", "Edit Port": "<PERSON>ğ <PERSON>ptörünü Düzenle", "Edit Port Forwarding": "Ağ Adaptörü Yönlendirmesini Düzenle", "Edit Port Group": "Ağ Adaptörünü Grubunu <PERSON>", "Edit Project": "<PERSON><PERSON>", "Edit QoS Policy": "QoS İlkesini Düzenle", "Edit Quota": "<PERSON>", "Edit Role": "<PERSON><PERSON><PERSON>", "Edit Router": "Yönlendiriciyi <PERSON>le", "Edit Rule": "Kuralı Düzenle", "Edit Share Metadata": "Paylaşım Meta Veriyi Düzenle", "Edit Subnet": "Alt Ağı Düzenle", "Edit System Permission": "Sistem İzni Düzenle", "Edit User": "Kullanıcıyı Düzenle", "Edit User Group": "Kullanıcı Grubunu <PERSON>le", "Edit VPN": "VPN'i Düzenle", "Edit VPN EndPoint Groups": "VPN Uç Noktası Gruplarını Düzenle", "Edit VPN IKE Policy": "VPN IKE İlkesini Düzenle", "Edit VPN IPsec Policy": "VPN IPsec İlkesini Düzenle", "Edit Volume Backup": "Disk Yedeğini Düzenle", "Edit host aggregate": "<PERSON>", "Edit metadata": "<PERSON>a veriyi d<PERSON>", "Edit quota": "<PERSON> düzenle", "Edit rule": "", "Editing only changes the content of the file, not the file name.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, yaln<PERSON>zca dosyanın içeriğini değiştirir. Dosya adını değiştirmez.", "Effective Mode": "<PERSON><PERSON><PERSON>", "Effective mode after configuration changes": "Ya<PERSON>ı<PERSON>ırma <PERSON>şikliklerinden sonra etkin mod", "Egress": "Çıkış", "Egress Policy": "", "Egress Policy ID": "", "Egress Policy Name": "", "Egypt": "Mısır", "Eject": "<PERSON><PERSON><PERSON><PERSON>", "El Salvador": "El Salvador", "Email": "E-posta", "Email Address": "E-posta Adresi", "Email for the zone. Used in SOA records for the zone.": "", "Enable": "Etkinleştir", "Enable Admin State": "Yönetici Durumunu <PERSON>", "Enable Compute Host": "Hesa<PERSON>lama Ana Bilgisayarını Etkinleştir", "Enable Compute Service": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Enable DHCP": "DHCP'y<PERSON>", "Enable Domain": "<PERSON>tkinleştir", "Enable Floating IP": "Değişken IP'yi <PERSON>r", "Enable Health Check": "Durum Kontrolünü <PERSON>kinleştir", "Enable Health Monitor": "Durum Denetleyicisini Etkinleştir", "Enable Load Balancer": "Yük Dengeleyiciyi Etkinleştir", "Enable Neutron Agent": "Neutron Ajanını Etkinleştir", "Enable Project": "<PERSON><PERSON><PERSON>", "Enable QoS Policy": "QoS İlkesini Etkinleştir", "Enable Registry": "<PERSON><PERSON>t Defterini Etkinleştir", "Enable SNAT": "SNAT Etkinleştir", "Enable Service": "Hizmeti Etkinleştir", "Enable User": "Kullanıcıyı Etkinleştir", "Enable auto heal": "Otomatik Onarımı Etkinleştir", "Enable auto remove": "Otomatik Kaldırmayı Etkinleştir", "Enable compute host": "Hesaplama ana bilgisayarını etkinleştir", "Enable interactive mode": "Etkileşimli Modu Etkinleştir", "Enabled": "Etkinleş<PERSON><PERSON>", "Enabled Load Balancer for Master Nodes": "<PERSON> Düğümler İçin Yük Dengeleyici Etkin", "Enabled Network": "Etkinleştirilmiş Ağ", "Encapsulation Mode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Encrypted": "Şifrelenmiş", "Encryption": "Şifreleme", "Encryption Algorithm": "Şifreleme Algoritması", "Encryption Info": "Şifreleme Bilgisi", "End Time": "Bitiş Zamanı", "Endpoint Counts": "Uç Nokta Sayısı", "Endpoints": "<PERSON><PERSON>", "Engine ID": "Motor Kimliği", "Enroll": "<PERSON><PERSON><PERSON>", "Enter Maintenance Mode": "Bakım Moduna Gir", "Enter an integer value between 1 and 65535.": "1 ile 65535 arasında bir tamsayı değeri girin.", "Enter query conditions to filter": "Filtreleme için sorgu koşullarını girin", "Entered: {length, plural, =1 {one character} other {# characters} }(maximum {maxCount} characters)": "Girilen: {length, plural, =1 {one character} other {# characters} }(maximum {maxCount} characters)", "Environment": "Ç<PERSON><PERSON>", "Environment Variable": "<PERSON><PERSON><PERSON>", "Environment Variables": "<PERSON><PERSON><PERSON>", "Ephemeral Disk (GiB)": "Ephemeral", "Equatorial Guinea": "<PERSON><PERSON><PERSON><PERSON>", "Eritrea": "Eritre", "Error": "<PERSON><PERSON>", "Error Deleting": "<PERSON><PERSON><PERSON>", "Error Extending": "Genişletme Hatası", "Error Restoring": "<PERSON><PERSON>", "Estonia": "Estonya", "Ether Type": "<PERSON><PERSON><PERSON>ü<PERSON>", "Ethiopia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Event": "", "Event Time": "<PERSON><PERSON>", "Evictions": "Çıkarılmalar", "Execute Command": "Komutu Çalıştır", "Execution Result": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Existing Volume": "Mevcut Disk", "Exit Policy": "Çıkış Politikası", "Exp: ": "", "Expand": "Genişlet", "Expand Advanced Options": "Gelişmiş Seçenekleri Genişlet", "Expired Time": "<PERSON><PERSON><PERSON><PERSON> Doldu", "Expires At": "<PERSON><PERSON><PERSON><PERSON>", "Export Location": "Dışa Aktarma Konumu", "Export Locations": "Dışa Aktarma Konumları", "Exposed Ports": "Açık Ağ Adaptörleri", "Extend Root Volume": "Kök Diski Genişlet", "Extend Share": "Paylaşımı Genişlet", "Extend Volume": "Diski Genişlet", "Extend volume": "Diski genişlet", "Extending": "Genişletiliyor", "Extending Error": "Genişletme Hatası", "External": "<PERSON><PERSON>", "External Fixed IP": "Harici Sabit IP", "External Fixed IPs": "Harici <PERSON>'ler", "External Gateway": "Harici Ağ Geçidi", "External IP": "Harici IP", "External IP(V4)": "Harici IP(V4)", "External IP(V6)": "Harici IP(V6)", "External Network": "Harici A<PERSON>", "External Network ID/Name": "Harici ağ kimliği/adı", "External Network Info": "Harici Ağ Bilgisi", "External Networks": "<PERSON><PERSON>", "External Port": "Harici Ağ Adaptörü", "External Port/Port Range": "Harici Ağ Adaptörü/Ağ Adaptör Aralığı", "Extra Infos": "<PERSON><PERSON>", "Extra Specs": "<PERSON><PERSON>", "FAKE": "SAHTE", "FLAT": "FLAT", "Fail Rollback": "Başarısız Geri Alma", "Failed": "Başarısız", "Failover Segment": "", "Falkland Islands (Malvinas)": "Falkland Adaları (Malvinas)", "Faroe Islands": "Faroe <PERSON>", "Fault": "<PERSON><PERSON><PERSON><PERSON>", "Fedora": "<PERSON><PERSON>", "Fiji": "Fiji", "File": "<PERSON><PERSON><PERSON>", "File System Used Space": "<PERSON><PERSON><PERSON>", "File URL": "Dosya URL'si", "Filename": "<PERSON><PERSON><PERSON>", "Files: {names}": "Dosyalar: {names}", "Fill In The Parameters": "Parametrel<PERSON>", "Fingerprint": "Parmak İzi", "Finish Resize": "Yeniden Boyutlandırmayı Tamamla", "Finland": "Finlandiya", "Firewall": "", "Firewall Detail": "", "Firewall Policies": "", "Firewall Policy": "", "Firewall Port": "", "Firewall Rule": "", "Firewall Rules": "", "Firewalls": "", "Fixed IP": "Sabit IP", "Fixed IP Address": "Sabit IP Adresi", "Fixed IPs": "Sabit IP'ler", "Fixed Network": "Sabit Ağ", "Fixed Subnet": "Sabit Alt Ağ", "Flavor": "Şablon", "Flavor Detail": "Şablon Detayı", "Flavor Info": "Şablon Bilgisi", "Flavor Name": "Şablon Adı", "Flavor families, used to configure the instance flavor classification": "", "Flavor of Master Nodes": "<PERSON>", "Flavor of Nodes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Flavors": "Şablonlar", "Floating IP": "Değişken IP", "Floating IP Address": "Değişken IP Adresi", "Floating IP Enabled": "Değişken IP Etkinleştirildi", "Floating IPs": "Değişken IP'ler", "Floating Ip": "Değişken Ip", "Floating Ip Address": "Değişken IP Adresi", "Floating Ip Detail": "Değişken IP Detayı", "Floating ip has already been associate, Please check Force release": "Değişken IP zaten ilişkilendirilmiş, Lütfen zorla serbest bırakmayı kontrol edin", "Folder Detail": "Klasör <PERSON>", "Folder Name": "Klasör <PERSON>", "For GPU type, you need to install GPU drivers in the instance operating system.": "GPU türü iç<PERSON>, sanal makine işletim sisteminde GPU sürücülerini yüklemeniz gerekmektedir.", "For GRE networks, valid segmentation IDs are 1 to 4294967295": "GRE ağları için geçerli segmentasyon kimlikleri 1 ile 4294967295 arasındadır.", "For VLAN networks, valid segmentation IDs are 1 to 4094": "VLAN ağları için geçerli segmentasyon kimlikleri 1 ile 4094 arasındadır.", "For VXLAN networks, valid segmentation IDs are 1 to 16777215": "VXLAN ağları için geçerli segmentasyon kimlikleri 1 ile 16777215 arasındadır.", "Forbidden": "Yasaklanmış", "Forbidden Domain": "Yasaklanmış Etki Alanı", "Forbidden Project": "Yasaklanmış Proje", "Forbidden User": "Yasaklanmış Kullanıcı", "Forbidden the domain will have a negative impact, all project and user in domain will be forbidden": "Etki alanın yasaklanması olumsuz etkiye neden olur, alan i<PERSON>indeki tüm projeler ve kullanıcılar yasaklanır.", "Force Delete": "<PERSON><PERSON><PERSON>", "Force Delete Container": "<PERSON><PERSON><PERSON>", "Force Delete Share Instance": "Zorla Paylaşılan Sanal Makine Sil", "Force release": "Zorla Serbest Bırak", "Force shutdown must be checked!": "", "Forced Down": "<PERSON><PERSON><PERSON>ılmış", "Forced Shutdown": "<PERSON><PERSON><PERSON>", "Forced shutdown may result in data loss or file system damage. You can also take the initiative to shut down and perform operations.": "Zorla kapatma veri kaybına veya dosya sistemi hasarına neden olabilir. Aynı zamanda kapatmayı başlatıp işlemleri gerçekleştirebilirsiniz.", "Forgot your password?": "Parolanızı mı unuttunuz?", "Format": "<PERSON><PERSON><PERSON><PERSON>", "Forward Slash ‘/’ is not allowed to be in a tag name": "İleri eğik çizgi ‘/’ bir etiket adında kullanılam<PERSON>.", "France": "<PERSON><PERSON><PERSON>", "Free": "Ücretsiz", "FreeBSD": "FreeBSD", "French Guiana": "Fransız Guyanası", "French Polynesia": "Fransız <PERSON>", "Frequent login failure will cause the account to be temporarily locked, please operate after 5 minutes": "Sık sık giriş başarısızlığı hesabın geçici olarak kilitlenmesine neden olur, lütfen 5 dakika sonra tekrar deneyin.", "From port": "<PERSON><PERSON>", "Front End": "<PERSON><PERSON>", "Frontend": "Önyüz", "Full": "Do<PERSON>", "Full Backup": "<PERSON>", "GPU Count": "GPU Sayısı", "GPU Info": "GPU Bilgisi", "GPU Model": "GPU Modeli", "GPU Parameters": "GPU Parametreleri", "GPU Type": "GPU Türü", "GPU model, used when configuring Compute Optimized Type with GPU": "", "GPU pass-through will load GPU devices directly to the instance for use. VGPU is a GPU virtualization solution. GPU resources will be segmented and distributed to multiple instances for shared use.": "GPU geçişi, GPU cihazlarını doğrudan sanal makine için kullanıma yükler. VGPU, bir GPU sanallaştırma çözümüdür. GPU kaynakları, paylaşılan kullanım için birden çok sanal makine için bölümlendirilir ve dağıtılır.", "GRE": "GRE", "Gabon": "Gabon", "Gambia": "Gambiya", "Gateway": "<PERSON><PERSON>", "Gateway IP": "Ağ Geçidi IP", "Gateway Time-out (code: 504) ": "<PERSON><PERSON> Geçidi Zaman Aşımı (kod: 504)", "Gateway ip {gateway_ip} conflicts with allocation pool {pool}": "Ağ geçidi IP {gateway_ip}, dağ<PERSON><PERSON><PERSON>m havuzu {pool} ile çakışıyor", "General Purpose": "Genel Amaçlı", "Generated Time": "", "Georgia": "Gürcistan", "Germany": "Almanya", "Get OpenRC file": "OpenRC dosyasını al", "Get Token": "Token Al", "Get {name} detail error.": "{name} a<PERSON>ı<PERSON>ı hatası alındı.", "Get {name} error.": "{name} hat<PERSON>ı alındı.", "Ghana": "Gana", "Gibraltar": "Cebelitarık", "Given IP": "Verilen IP", "Glance": "Glance", "Glance Image": "Glance İmajı", "Global Setting": "<PERSON><PERSON>", "GlusterFS": "GlusterFS", "Grant Databases Access": "Veritabanı Erişimini <PERSON>", "Greece": "Yunanistan", "Greenland": "Grönland", "Grenada": "Grenada", "Guadeloupe": "Guadeloupe", "Guam": "Guam", "Guatemala": "Guatemala", "Guinea": "Gine", "Guinea Bissau": "Gine-Bissau", "Guyana": "Guyana", "HDFS": "HDFS", "HEALTHY": "SAĞLIKLI", "HTTP Proxy": "HTTP Proxy", "HTTP Version not supported (code: 505) ": "HTTP Sürümü Desteklenmiyor (kod: 505}", "HTTPS Proxy": "HTTPS Proxy", "Haiti": "Haiti", "Hard Reboot": "<PERSON><PERSON><PERSON>", "Hard Rebooting": "Zorla Yeniden Başlatılıyor", "Hash": "<PERSON>rma", "Health Check CMD": "Durum Kontrol CMD", "Health Check Interval": "Sağlık Kontrol Aralığı", "Health Check Retries": "Sağlık Kontrol Tekrarları", "Health Check Timeout": "Sağlık Kontrol Zaman Aşımı", "Health Checking Log": "Sağlık Kontrol Günlüğü", "Health Inspection": "Sağlık Denetimi", "Health Monitor": "<PERSON><PERSON>", "Health Monitor Delay": "Durum Denetleyici Gecikme", "Health Monitor Detail": "Durum Denetleyici Detayı", "Health Monitor Max Retries": "Durum Denetleyici Maksimum Deneme", "Health Monitor Name": "Durum Denetleyici Adı", "Health Monitor Timeout": "Durum Denetleyici Zaman Aşımı", "Health Monitor Type": "Durum Denetleyici Türü", "Health Status": "Sağlık Durumu", "HealthMonitor Type": "Durum Denetleyici Türü", "Healthy": "İyi", "Heartbeat Timestamp": "Ka<PERSON>p Atış Zaman Damgası", "Hello, {name}": "<PERSON><PERSON><PERSON><PERSON>, {name}", "Heterogeneous Computing": "Farklı Tipli Hesaplama", "Hidden": "GİZLİ", "Hide Advanced Options": "Gelişmiş Seçenekleri Gizle", "Hide Default Firewalls": "", "Hide Default Policies": "", "Hide Default Rules": "", "High Clock Speed": "Yüksek Saat Hızı", "Home": "<PERSON>", "Home page": "<PERSON>", "Honduras": "Honduras", "Hong Kong": "Hong Kong", "Host": "<PERSON>", "Host Aggregate": "<PERSON>", "Host Aggregates": "<PERSON>", "Host Average Network IO": "Ana Bilgisayar Ortalama Ağ Giriş/Çıkışı", "Host CPU Usage": "Ana Bilgisayar CPU Kullanımı", "Host Detail": "<PERSON>ilgis<PERSON>", "Host Disk Average IOPS": "Ana Bilgisayar Disk Ortalama IOPS", "Host Memory Usage": "Ana Bilgisayar Bellek Kullanımı", "Host Name": "", "Host Routes": "<PERSON>", "Host Routes Format Error(e.g. *************/24,***********)": "<PERSON>dirmeleri Biç<PERSON> (örn. *************/24,***********)", "Host Routes Format Error(e.g. ::0a38:01fe/24,::0a38:01fe)": "<PERSON>ilgis<PERSON>lendirmeleri Biçim <PERSON> (örn. ::0a38:01fe/24,::0a38:01fe)", "Hostname": "<PERSON>il<PERSON>", "Hosts": "<PERSON>", "Hosts Detail": "<PERSON>", "Hungary": "<PERSON><PERSON><PERSON>", "Hypervisor Detail": "Hypervisor Detayı", "Hypervisors": "Hypervisor'lar", "ICMP": "", "ICMP Code": "ICMP Kodu", "ICMP Type": "ICMP Türü", "ICMP Type/ICMP Code": "ICMP Türü/ICMP Kodu", "ID": "<PERSON><PERSON>", "ID/Floating IP": "Kimlik/Değişken IP", "ID/Name": "Kimlik/Ad", "IGMP": "IGMP", "IKE Policies": "IKE <PERSON>", "IKE Policy": "IKE <PERSON>lkesi", "IKE Version": "IKE Sürümü", "IP": "IP", "IP Address": "IP Adresi", "IP Distribution Mode": "IP Dağıtım Modu", "IP Protocol": "IP Protokolü", "IP Usage": "IP Kullanımı", "IP Version": "IP Sürümü", "IP address allocation polls, one enter per line(e.g. ***********,***********00)": "IP adresi <PERSON>, her satırda bir giriş yapın (örn. ***********,***********00)", "IP address allocation polls, one enter per line(e.g. {ip})": "IP adresi <PERSON>, her satırda bir giriş yapın (örn. {ip})", "IPMI Address": "IPMI Adresi", "IPMI Bridge": "IPMI Köprüsü", "IPMI Password": "IPMI Şifresi", "IPMI Port": "IPMI Ağ Adaptörü", "IPMI Privilege Level": "IPMI Ayrıcalık Seviyesi", "IPMI Protocol Version": "IPMI Protokol Sürümü", "IPMI Username": "IPMI Kullanıcı Adı", "IPMITool": "IPMITool", "IPXE": "IPXE", "IPsec Policies": "IPsec İlkeleri", "IPsec Policy": "IPsec İlkesi", "IPsec Site Connection": "IPsec Site Bağlantısı", "IPsec Site Connections": "IPsec Site Bağlantıları", "IPsec site connection Detail": "IPsec site bağlantısı Detayı", "IPv4": "IPv4", "IPv4 Address": "IPv4 Adresi", "IPv6": "IPv6", "IPv6 Address": "IPv6 Adresi", "IPv6 Address Record": "IPv6 Adres Kaydı", "IPv6-Encap": "IPv6-Encap", "IPv6-Frag": "IPv6-Frag", "IPv6-ICMP": "IPv6-ICMP", "IPv6-NoNxt": "IPv6-NoNxt", "IPv6-Opts": "IPv6-Opts", "IPv6-Route": "IPv6-Route", "ISO - Optical disc image format": "ISO - Optik disk görüntü biçimi", "Iceland": "İzlanda", "Id": "", "Identifier of the physical port on the switch to which node’s port is connected to": "Düğümün bağlı olduğu anahtarın fiziksel ağ adaptörünün tanımlayıcısı", "Identity": "<PERSON><PERSON>", "If \"Enable\" fails to roll back, the resource will be deleted after the creation fails; if \"Disable\" fails to roll back, the resource will be retained after the creation fails.": "\"Etkin\" iş<PERSON>i geri alınamaz<PERSON>, kaynak oluşturma başarısız olduktan sonra kaynak silinecektir; \"Etkin Değil\" işlemi geri alınamazsa, kaynak oluşturma başarısız olduktan sonra kaynak korunacaktır.", "If OS is Linux, system will reset root password, if OS is Windows, system will reset Administrator password.": "Eğer işletim sistemi Linux ise sistem, kök parolasını sıfırlayacaktır. Eğer işletim sistemi Windows ise sistem, yönetici parolasını sıfırlayacaktır.", "If an instance is using this flavor, deleting it will cause the instance's flavor data to be missing. Are you sure to delete {name}?": "<PERSON><PERSON><PERSON> bir sanal makine bu şablonu kullanıyorsa, şablonu silmek sanal makinenin şablon verilerinin kaybolmasına neden olacaktır. {name}'i silmek istediğinize emin misiniz?", "If checked, the network will be enable.": "<PERSON><PERSON><PERSON> is<PERSON>, ağ etkinleştirilecektir.", "If exposed port is specified, this parameter will be ignored.": "Açık ağ adaptörü belirtilirse, bu parametre göz ardı edilecektir.", "If it is an SNI type certificate, a domain name needs to be specified": "Eğer bir SNI tipi sertifika ise, bir alan adı belirtilmelidir.", "If it’s not set, the value of this in the template will be used.": "<PERSON><PERSON><PERSON>, taslaktaki değeri kullanılacaktır.", "If no gateway is specified, the first IP address will be defaulted.": "<PERSON>ğer bir ağ geçidi belirtilmezse, ilk IP adresi varsayılan olarak atanır.", "If not provided, the roles assigned to the application credential will be the same as the roles in the current token.": "<PERSON><PERSON><PERSON> belirtilmezse uygulama kimlik bilgilerine atanmış roller, mevcut tokendeki rollerle aynı olacaktır.", "If nova-compute on the host is disabled, it will be forbidden to be selected as the target host.": "<PERSON><PERSON><PERSON> ana bilgisayardaki nova-compute devre dı<PERSON><PERSON> bı<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hedef ana bilgis<PERSON>r olarak seçilmesi yasaklanacaktır.", "If set then all tenants will be able to see this share.": "<PERSON><PERSON><PERSON>, tüm tenantlar bu paylaşımı görebilecektir.", "If the capacity of the disk is large, the type modify operation may take several hours. Please be cautious.": "Disk kapasitesi büyükse, tip değiştirme işlemi birkaç saat sürebilir. Lütfen dikkatli olun.", "If the listener has an SNI certificate installed, it cannot be removed. Please delete the listener or replace the SNI certificate": "Dinleyiciye SNI sertifikası yüklüyse, kaldırılamaz. Dinleyiciyi silin veya SNI sertifikasını değiştirin.", "If the root disk has a snapshot, it will affect the deletion of the original disk during reconstruction or the recovery of the instance snapshot.": "", "If the value is set to 0, it means unlimited": "Değer 0 olarak ayarlanırsa, sınırsız anlamına gelir.", "If the volume associated with the snapshot has changed the volume type, please modify this option manually; if the volume associated with the snapshot keeps the volume type unchanged, please ignore this option. (no need to change).": "Anlık görüntü ile ilişkilendirilen diskteki disk türü değiştirildiyse, lütfen bu seçeneği manuel olar<PERSON>ğiştirin; eğer anl<PERSON>k görüntü ile ilişkilendirilen diskteki disk türü değişmediyse, lütfen bu seçeneği yok sayın (değiştirmeniz gerekmez).", "If this parameter is selected, resumable uploads are supported, but the total upload time may be increased by a small amount. Images smaller than 200M are not recommended.": "Bu parametre seçilirse, devam ettirilebilir yüklemeler desteklenir, ancak toplam yükleme süresi küçük bir miktar artırılabilir. 200M'den küçük görüntüler önerilmez.", "If this parameter is specified, Zun will create a security group with a set of rules to open the ports that should be exposed, and associate the security group to the container.": "<PERSON>ğ<PERSON> bu parametre belirtilirse, <PERSON><PERSON> belirtilen ağ adaptörlerini açmak için bir kural kümesi ile bir güvenlik grubu oluşturacak ve güvenlik grubunu konteynerle ilişkilendirecektir.", "If you are not authorized to access any project, or if the project you are involved in has been deleted or disabled, contact the platform administrator to reassign the project": "Herhang<PERSON> bir projeye eri<PERSON>im yet<PERSON>z yoksa ya da dahil olduğunuz proje silinmiş veya devre dışı bırakılmışsa, projeye yeniden atanmak için platform yöneticisine başvurun.", "If you are not sure which authentication method to use, please contact your administrator.": "Kullanılacak kimlik doğrulama yöntemi hakkında emin <PERSON>, lütfen yöneticinizle iletişime geçin.", "If you choose a port which subnet is different from the subnet of LB, please ensure connectivity between the two.": "YD (Yük Dengeleyici)'nin alt ağından farklı bir alt ağa sahip bir ağ adaptörü seçerseniz, iki alt ağ arasında bağlantı sağlamayı unutmayın.", "If you do not fill in parameters such as cpus, memory_mb, local_gb, cpu_arch, etc., you can automatically inject the configuration and Mac address of the physical machine by performing the \"Auto Inspect\" operation.": "CPUs, bellek miktarı, yerel disk boyutu, CPU mimarisi gibi parametreleri doldurmadıysanız, \"Otomatik İnceleme\" işlemini gerçekleştirerek fiziksel makinenin yapılandırmasını ve MAC adresini otomatik olarak ekleyebilirsiniz.", "If you still want to keep the disk data, it is recommended that you create a backup for the disk before deleting.": "", "Illegal JSON scheme": "Geçersiz JSON şeması", "Image": "<PERSON><PERSON><PERSON>", "Image & OS": "İmaj & İşletim Sistemi", "Image Backup": "<PERSON><PERSON><PERSON>", "Image Detail": "İmaj <PERSON>", "Image Driver": "<PERSON><PERSON><PERSON>", "Image Info": "<PERSON><PERSON><PERSON>", "Image Name": "<PERSON><PERSON><PERSON>", "Image Pending Upload": "<PERSON><PERSON><PERSON>", "Image Pulling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Image Size": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Image Snapshot Pending": "İmaj Anlık Görüntüsü Beklemede", "Image Uploading": "<PERSON><PERSON><PERSON>", "Images": "İmajlar", "Immediate effect": "<PERSON><PERSON><PERSON> etki", "Immediately delete": "<PERSON><PERSON> sil", "Implied Roles": "<PERSON><PERSON><PERSON><PERSON>", "Import Keypair": "<PERSON><PERSON><PERSON>ini İçe Aktar", "Import Metadata": "Meta Verileri İçe Aktar", "Import metadata": "Meta Verileri İçe Aktar", "Importing": "İçe Aktarılıyor", "In Cluster": "<PERSON><PERSON><PERSON>", "In Use": "Kullanımda", "In general, administrator for Windows, root for Linux, please fill by image uploading.": "<PERSON><PERSON><PERSON> ,Windows için yönetici - Linux için root, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yükleme sırasında doldurulmalıdır.", "In order to avoid data loss, the instance will shut down and interrupt your business. Please confirm carefully.": "Veri kaybını önlemek için sanal makine kapatılacak ve işiniz kesintiye uğrayacak. Lütfen dikkatli bir şekilde <PERSON>aylayın.", "In the last 30 days": "Son 30 günde", "In the last 7 days": "Son 7 günde", "In the last hour": "Son bir saatte", "In-use": "Kullanımda", "Inactive": "<PERSON><PERSON><PERSON>", "Increment Backup": "<PERSON><PERSON><PERSON>", "Incremental": "Artış", "Incremental Backup": "<PERSON><PERSON><PERSON>", "India": "Hindistan", "Indicates whether this VPN can only respond to connections or both respond to and initiate connections.": "Bu VPN'in yalnızca bağlantılara yanıt verip veremeyeceğini veya hem yanıt verip hem de bağlantı başlatabileceğini belirtir.", "Indonesia": "Endonezya", "Infinity": "Sonsuz", "Info": "<PERSON><PERSON><PERSON>", "Ingress": "<PERSON><PERSON><PERSON>", "Ingress Policy": "", "Ingress Policy ID": "", "Ingress Policy Name": "", "Init Complete": "Başlatma Tamamlandı", "Init Failed": "Başlatma Başarısız", "Init In Progress": "<PERSON><PERSON><PERSON><PERSON>", "Initial Admin User": "Başlangıç Yönetici Kullanıcısı", "Initial Databases": "Başlangıç Veritabanları", "Initial Volume Size": "Başlangıç Disk Boyutu", "Initialize Databases": "Veritabanlarını Başlat", "Initiator Mode": "Başlatıcı Modu", "Input destination port or port range (example: 80 or 80:160)": "<PERSON><PERSON><PERSON> ağ adaptörünü veya ağ adaptörü aralığını girin (örneğin: 80 veya 80:160)", "Input external port or port range (example: 80 or 80:160)": "Harici ağ adaptörünü veya ağ adaptörü aralığını girin (örneğin: 80 veya 80:160)", "Input internal port or port range (example: 80 or 80:160)": "Dahili ağ adaptörünü veya ağ adaptörü aralığını girin (örneğin: 80 veya 80:160)", "Input source port or port range (example: 80 or 80:160)": "Kaynak ağ adaptörünü veya ağ adaptörü aralığını girin (örneğin: 80 veya 80:160)", "Insecure Registry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Insert": "", "Insert After": "", "Insert Before": "", "Insert Rule": "", "Inspect Failed": "Denetim Başarısız", "Inspecting": "Denetleniyor", "Instance": "<PERSON><PERSON> makine", "Instance \"{ name }\" has already been locked.": "<PERSON><PERSON> makine \"{ name }\" zaten kilitlenmiş durumda.", "Instance \"{ name }\" is ironic, can not soft reboot it.": "<PERSON><PERSON> makine \"{ name }\" ironik, on<PERSON>la yumuşak yeniden ba<PERSON><PERSON>ma ya<PERSON>ı<PERSON>.", "Instance \"{ name }\" is locked, can not delete it.": "<PERSON>al makine \"{ name }\" kilitli, siline<PERSON>z.", "Instance \"{ name }\" is locked, can not pause it.": "<PERSON>al makine \"{ name }\" kili<PERSON><PERSON>, duraklatılamaz.", "Instance \"{ name }\" is locked, can not reboot it.": "<PERSON><PERSON> makine \"{ name }\" k<PERSON><PERSON><PERSON>, ye<PERSON><PERSON> ba<PERSON>ı<PERSON>.", "Instance \"{ name }\" is locked, can not resume it.": "<PERSON><PERSON> makine \"{ name }\" kili<PERSON><PERSON>, devam et<PERSON>.", "Instance \"{ name }\" is locked, can not soft reboot it.": "<PERSON><PERSON> makine \"{ name }\" k<PERSON><PERSON><PERSON>, on<PERSON>la yumu<PERSON>ak yeniden ba<PERSON><PERSON>ma ya<PERSON>ı<PERSON>.", "Instance \"{ name }\" is locked, can not start it.": "<PERSON><PERSON> makine \"{ name }\" k<PERSON><PERSON><PERSON>, ba<PERSON>latılamaz.", "Instance \"{ name }\" is locked, can not stop it.": "<PERSON>al makine \"{ name }\" kili<PERSON>i, durdurulamaz.", "Instance \"{ name }\" is locked, can not suspend it.": "<PERSON><PERSON> makine \"{ name }\" k<PERSON><PERSON><PERSON>, <PERSON>ıya al<PERSON>.", "Instance \"{ name }\" is locked, can not unpause it.": "<PERSON><PERSON> makine \"{ name }\" k<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>.", "Instance \"{ name }\" is not locked, can not unlock it.": "", "Instance \"{ name }\" status is not active, can not soft reboot it.": "", "Instance \"{ name }\" status is not in active or shutoff, can not reboot it.": "", "Instance \"{ name }\" status is not in active or suspended, can not stop it.": "", "Instance \"{ name }\" status is not in active, can not pause it.": "", "Instance \"{ name }\" status is not in active, can not suspend it.": "", "Instance \"{ name }\" status is not in paused, can not unpause it.": "", "Instance \"{ name }\" status is not in suspended, can not resume it.": "", "Instance \"{ name }\" status is not shutoff, can not start it.": "", "Instance Addr": "<PERSON><PERSON>", "Instance Architecture": "<PERSON><PERSON>", "Instance Console Log": "<PERSON><PERSON> Makine Konsol Günlüğü", "Instance Detail": "<PERSON><PERSON> Makine <PERSON>", "Instance ID": "<PERSON><PERSON>", "Instance IP": "Sanal Makine IP'si", "Instance Info": "<PERSON><PERSON>", "Instance Port": "", "Instance Related": "<PERSON><PERSON> Ma<PERSON>", "Instance Snapshot": "Sanal Makine Anlık Görüntü", "Instance Snapshot Detail": "Sanal Makine Anlık Görüntü Ayrıntısı", "Instance Snapshot Name": "Sanal Makine Anlık Görüntü Adı", "Instance Snapshots": "Sanal Makine Anlık Görüntüleri", "Instance Status": "<PERSON><PERSON>", "Instance UUID": "", "Instance-HA": "", "Instances": "<PERSON><PERSON>", "Instances \"{ name }\" are locked, can not delete them.": "<PERSON><PERSON> makineler \"{ name }\" kili<PERSON><PERSON>, si<PERSON><PERSON><PERSON>r.", "Insufficient {name} quota to create resources (left { quota }, input { input }).": "Kaynakları oluşturmak için <PERSON> {name} kotası yok (kalan { quota }, girdi { input }).", "Interface Info": "Arayüz <PERSON>", "Interface Name:": "Arayüz Adı:", "Interface for vendor-specific functionality on this node": "Bu düğümdeki satıcıya özgü işlev için a<PERSON>üz", "Interface used for attaching and detaching volumes on this node": "Bu düğümdeki diskleri eklemek ve çıkarmak için kullanılan arayüz", "Interface used for configuring RAID on this node": "Bu düğümde RAID yapılandırmak için kullanılan arayüz", "Interfaces": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Internal Ip Address": "Dahili IP Adresi", "Internal Network Bandwidth (Gbps)": "", "Internal Port": "Dahili Ağ Adaptörü", "Internal Port/Port Range": "Dahili Ağ Adaptörü/Ağ Adaptörü Aralığı", "Internal Server Error (code: 500) ": "<PERSON><PERSON><PERSON> (kod: 500)", "Invalid": "Geçersiz", "Invalid CIDR.": "Geçersiz CIDR.", "Invalid IP Address": "Geçersiz IP Adresi", "Invalid IP Address and Port": "Geçersiz IP Adresi ve Ağ Adaptörü", "Invalid Mac Address. Please Use \":\" as separator.": "Geçersiz MAC Adresi. Lütfen \":\" ayrıcı olarak kullanın.", "Invalid Tag Value: {tag}": "Geçersiz Etiket Değeri: {tag}", "Invalid combination": "Geçersiz k<PERSON>yon", "Invalid: ": "Geçersiz: ", "Invalid: Allocation Pools Format Error(e.g. ***********,***********00) and start ip should be less than end ip": "Geçersiz: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Havuzları Biçim <PERSON>(örn. ***********, ***********00) ve başlangıç IP'si, bitiş IP'sinden küçük olmalıdır.", "Invalid: Allocation Pools Format Error(e.g. fd00:dead:beef:58::9,fd00:dead:beef:58::13) and start ip should be less than end ip": "Geçersiz: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Havuzları Biçim <PERSON>(örn. fd00:dead:beef:58::9, fd00:dead:beef:58::13) ve başlangıç IP'si, bitiş IP'sinden küçük olmalıdır.", "Invalid: CIDR Format Error(e.g. **********/24)": "Geçersiz: CIDR Biçim <PERSON>(örn. **********/24)", "Invalid: DNS Format Error(e.g. 1001:1001::)": "Geçersiz: DNS <PERSON>i<PERSON><PERSON>(örn. 1001:1001::)", "Invalid: DNS Format Error(e.g. ***************)": "Geçersiz: DNS <PERSON><PERSON><PERSON><PERSON>(örn. ***************)", "Invalid: Domain name cannot be duplicated": "", "Invalid: Password must be the same with confirm password.": "Geçersiz: <PERSON><PERSON><PERSON>, onay parolasıyla aynı olmalıdır.", "Invalid: Please input a valid ip": "Geçersiz: Geçerli bir IP adresi giriniz", "Invalid: Please input a valid ipv4": "Geçersiz: Geçerli bir IPv4 adresi giriniz", "Invalid: Please input a valid ipv6.": "Geçersiz: Geçerli bir IPv6 adresi giriniz.", "Invalid: Project name can not be chinese": "Geçersiz: <PERSON><PERSON> o<PERSON>", "Invalid: Project names in the domain can not be repeated": "", "Invalid: Quota value(s) cannot be less than the current usage value(s): { used } used.": "Geçersiz: <PERSON><PERSON><PERSON>(ler), mevcut kullan<PERSON><PERSON> de<PERSON>(ler)den küçük olamaz: { used } kullanıldı.", "Invalid: User Group names in the domain can not be repeated": "", "Invalid: User names in the domain can not be repeated": "", "Ip Address": "IP Adresi", "Iran (Islamic Republic of)": "İran (İslam Cumhuriyeti)", "Iraq": "<PERSON><PERSON>", "Ireland": "İrlanda", "Ironic Instance": "<PERSON><PERSON><PERSON>", "Ironic Instance Name": "İronik Sanal Makine Adı", "Is Current Project": "Mevcut Proje mi", "Is Public": "Herkese Açık mı", "Is admin only": "<PERSON><PERSON>e yö<PERSON> mi", "Is associate to floating ip: ": "Yüzen IP'ye bağlı mı: ", "Is external network port": "<PERSON>ci ağın ağ adaptörü mü", "Isolate": "İzole et", "Isolate(No multithreading)": "İzole et (Çoklu iş parçacığı yok)", "Israel": "İsrail", "It is IPv6 type.": "IPv6 türüdür.", "It is recommended that the { instanceType } instance simultaneously set large page memory to large. { instanceType } instances also require faster memory addressing capabilities.": "{ instanceType } sanal makineleri aynı anda büyük sayfa belleğini büyük olarak ayarlanması önerilir. { instanceType } sanal makineleri ayrıca daha hızlı bellek adresleme yetenekleri gerektirir.", "It is recommended that you perform this cloning operation on a disk without any reading/writing": "Bu klonlama iş<PERSON>/yazma olmadan bir disk üzerinde gerçekleştirmeniz önerilir", "It is recommended that you use the private network address 10.0.0.0/8, **********/12, ***********/16": "10.0.0.0/8, **********/12, ***********/16 özel ağ adreslerini kullanmanız önerilir", "It is recommended that { instanceType } instance simultaneously set NUMA affinity policy for PCIE device to force or priority matching. This configuration can further improve PCIE computing performance.": "{ instanceType } sanal makinesinin aynı anda PCIE cihazı için NUMA Afinite ilkesini zorlama veya öncelik eşleştirme olarak ayarlamanız önerilir. Bu yapılandırma PCIE hesaplama performansını daha da artırabilir.", "It is recommended to install and use this agent. The instance created with this image can be used to modify the password (qemu_guest_agent needs to be installed when creating the image).": "Bu a<PERSON>ı kurmanız ve kullanmanız önerilir. Bu imajla oluşturulan sanal makine, parolayı değiştirmek için kullanılabilir (imaj oluşturulurken qemu_guest_agent k<PERSON><PERSON> olmalıdır).", "It is recommended to refer to the following description format, otherwise it may not be effective": "", "It is recommended to set CPU binding strategy as binding on { instanceType } instance. This configuration further improves the performance of the instance CPU.": "CPU bağlama stratejisini { instanceType } sanal makinede bağlama olarak ayarlamanız önerilir. <PERSON><PERSON> yap<PERSON>ırma, sanal makinenin CPU performansını daha da iyileştirir.", "It is recommended to set the CPU thread binding policy as thread binding in { instanceType } instance, which can further improve the CPU performance of instance.": "CPU iş parçacığı bağlama ilkesini { instanceType } sanal makinesinde iş parçacığı bağlama olarak ayarlamanız önerilir, bu da sanal makinenin CPU performansını daha da artırabilir.", "It is suggested to use the marked AZ directly, too much AZ will lead to the fragmentation of available resources": "Belirtilen AZ'yi do<PERSON> k<PERSON>z ö<PERSON>ilir, faz<PERSON>ıda AZ, mevcut kaynakların parçalanmasına neden olacaktır", "It is unreachable for all floating ips.": "Tüm değişken IP'ler için ul<PERSON>şılamaz.", "It is unreachable for this floating ip.": "Bu değişken IP için ulaşılamaz.", "Italy": "İtalya", "Items in Cache": "Önbellekteki Öğeler", "Jamaica": "<PERSON><PERSON><PERSON>", "Japan": "Jaonya", "Jordan": "Ürdün", "Jump to Console": "Ko<PERSON>la Git", "Kampuchea": "Demokratik Kampuçya", "Kazakhstan": "Kazakistan", "Kenya": "Kenya", "Kernel ID": "<PERSON><PERSON><PERSON><PERSON>", "Kernel Image": "Çekirdek <PERSON>jı", "Kernel Version": "Çekirdek Sürümü", "Key": "<PERSON><PERSON><PERSON>", "Key Pair": "<PERSON><PERSON><PERSON>", "Key Pairs": "<PERSON><PERSON><PERSON>", "Key Size (bits)": "<PERSON><PERSON><PERSON> (bit)", "Keypair": "<PERSON><PERSON><PERSON>", "Keypair Detail": "<PERSON><PERSON><PERSON>", "Keypair Info": "<PERSON><PERSON><PERSON>", "Keystone Credentials": "Keystone Kimlik Bilgileri", "Keystone token is expired.": "Keystone token sü<PERSON>i doldu.", "Kill": "Sonlandır", "Kill Container": "<PERSON><PERSON><PERSON><PERSON>", "Kill Signal": "<PERSON><PERSON><PERSON>", "Killed": "Sonlandırıldı", "Kubernetes": "Kubernetes", "Kuwait": "<PERSON><PERSON><PERSON>", "Kyrgyzstan": "Kırgızistan", "LB Algorithm": "YD (Yük Dengeleyici) Algoritması", "LEAST_CONNECTIONS": "EN_AZ_BAGLANTI", "Labels": "<PERSON><PERSON><PERSON><PERSON>", "Lao People's Democratic Republic": "Laos Demokratik Halk Cumhuriyeti", "Large": "Büyük", "Large Screen": "Büyük Ekran", "Large(Optimal performance)": "Büyük (En iyi performans)", "Last 2 Weeks": "Son 2 Hafta", "Last 24H Status": "Son 24 Saat Durumu", "Last 7 Days": "Son 7 Gün", "Last Day": "<PERSON>", "Last Hour": "<PERSON>", "Last Updated": "<PERSON>", "Last week alarm trend": "Geçen hafta alarm trendi", "Latvia": "Letonya", "Leave Maintenance Mode": "Bakım Modundan Çık", "Lebanon": "<PERSON><PERSON><PERSON><PERSON>", "Left": "Sol", "Lesotho": "Lesotho", "Liberia": "<PERSON><PERSON><PERSON>", "Libyan Arab Jamahiriya": "Libya", "Liechtenstein": "<PERSON><PERSON><PERSON>ş<PERSON><PERSON>", "Lifetime": "Hayat Döngüsü", "Lifetime Value": "Hayat Döngüsü Değeri", "Listener": "<PERSON><PERSON><PERSON>", "Listener Connection Limit": "Dinleyici Bağlantı Sınırı", "Listener Description": "Dinleyici Açıklaması", "Listener Detail": "Dinleyici Detayı", "Listener Name": "Dinleyici Adı", "Listener Number": "Dinleyici Numarası", "Listener Protocol": "Dinleyici Protokolü", "Listener Protocol Port": "Dinleyici Protokolü Ağ Adaptörü", "Listeners": "<PERSON><PERSON><PERSON><PERSON>", "Lithuania": "", "Live Migrate": "Canlı Taşıma", "Live Migration At Destination": "Hedefte Canlı Taşıma", "Load Balancer": "<PERSON><PERSON><PERSON>", "Load Balancer Detail": "Yük Dengeleyici Detayı", "Load Balancer Name": "Yük Dengeleyici Adı", "Load Balancers": "<PERSON><PERSON><PERSON>", "Load Template from a file": "Dosyadan <PERSON>", "Load from local files": "<PERSON><PERSON>", "LoadBalancers Instances": "Yük Dengeleyiciler Sanal <PERSON>", "Local": "<PERSON><PERSON>", "Local Endpoint Group": "<PERSON><PERSON> Nokta Grubu", "Local Endpoint Group ID": "<PERSON><PERSON> Nokta Grubu <PERSON>", "Local Link Connection": "Yerel Bağlantı Bağlantısı", "Local Network": "<PERSON><PERSON>", "Local SSD": "<PERSON><PERSON>", "Local Subnet": "<PERSON>rel <PERSON>", "Locality": "", "Lock": "<PERSON><PERSON><PERSON>", "Lock Instance": "<PERSON><PERSON>", "Lock Status": "<PERSON><PERSON>", "Lock instance will lock the operations that have a direct impact on the operation of the instance, such as: shutdown, restart, delete, the mounting and unmounting of volume, etc. It does not involve the capacity expansion and change type of volume.": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>sk bağlama ve Disk bağlamama gibi işlemleri kilitler. Disk kapasitesini genişletmeyi ve disk türünü değiştirmeyi içermez. ", "Locked": "<PERSON><PERSON><PERSON>", "Log": "Günlük", "Log Length": "Günlük Uzunluğu", "Log in": "<PERSON><PERSON><PERSON> yap", "Login Name": "<PERSON><PERSON><PERSON>", "Login Password": "<PERSON><PERSON><PERSON>", "Login Type": "<PERSON><PERSON><PERSON>", "Logs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Luxembourg": "Lüksemburg", "MAC Address": "MAC Adresi", "MAC Learning State": "MAC Öğrenme Durumu", "MAPRFS": "MAPRFS", "MEM %": "Bellek %", "MEM LIMIT (MiB)": "Bellek Sınırı (MiB)", "MEM USAGE (MiB)": "Bellek Kullanımı (MiB)", "MTU": "MTU", "Mac Address": "<PERSON>", "MacVTap": "MacVTap", "Macau": "<PERSON><PERSON><PERSON>", "Madagascar": "Madakasgar", "Mail Exchange Record": "Posta Değişim <PERSON>", "Maintained": "Bakımlı", "Maintenance": "Bakım", "Malawi": "Malavi", "Malaysia": "<PERSON><PERSON><PERSON>", "Maldives": "<PERSON><PERSON><PERSON><PERSON>", "Mali": "Mali", "Malta": "Malta", "Manage Access": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Manage Access Rule": "<PERSON><PERSON><PERSON><PERSON> Yönet", "Manage Error": "<PERSON><PERSON>", "Manage Host": "Ana Bilgisayarı Yönet", "Manage Metadata": "<PERSON><PERSON> Veriler<PERSON>", "Manage Ports": "", "Manage QoS Spec": "QoS Belirtisini Yönet", "Manage Resource Types": "<PERSON><PERSON><PERSON>", "Manage Security Group": "Güvenlik Grubunu <PERSON>", "Manage Starting": "Başlatmayı Yönet", "Manage State": "<PERSON><PERSON><PERSON>", "Manage User": "Kullanıcıyı Yönet", "Manage User Group": "Kullanıcı Grubunu Yönet", "Manage host": "Ana bilgisayarı yönet", "Manage user": "Kullanıcıyı yönet", "Manage user group": "Kullanıcı grubunu yönet", "Manageable": "Yönetilebilir", "Management": "Y<PERSON><PERSON><PERSON>", "Management Reason": "<PERSON><PERSON><PERSON><PERSON>", "Mandatory for secondary zones. The servers to slave from to get DNS information.": "", "Manu": "El ile", "Manual input": "<PERSON> giri<PERSON>", "Manually Assigned Address": "<PERSON> Atanan <PERSON>", "Manually Specify": "Elle Belirt", "Marshall Islands": "<PERSON>", "Martinique": "<PERSON><PERSON>", "Master Node Addresses": "<PERSON>", "Master Node Flavor": "<PERSON>", "Master Node LB Enabled": "<PERSON> Y<PERSON> (Yük Dengeleyici) Etkin", "Masters": "Ana", "Mauritania": "Moritanya", "Mauritius": "<PERSON><PERSON><PERSON>", "Max Avail": "<PERSON><PERSON><PERSON><PERSON>bilirlik", "Max BandWidth": "<PERSON><PERSON><PERSON>um Bant Genişliği", "Max Burst": "<PERSON><PERSON><PERSON><PERSON>", "Max Retries": "<PERSON><PERSON><PERSON><PERSON>", "Max Retry": "<PERSON><PERSON><PERSON><PERSON>", "Max connect": "<PERSON><PERSON><PERSON><PERSON> Bağlantı", "Maximum interval time for each health check response": "Her sağlık kontrolü yanıtı için maksimum aralık süresi", "Maximum time to allow one check to run in seconds": "Bir denetimin ç<PERSON>şmasına izin vermek için maksimum süre (saniye)", "Mayotte": "Mayotte", "Mem": "Bellek", "Member Count": "Üye Sayısı", "Member Detail": "Üye Detayı", "Member Num": "Üye Sayısı", "Members": "<PERSON><PERSON><PERSON>", "Members of Each Group": "Her Grubun Üyeleri", "Members of Each Server Group": "<PERSON> <PERSON><PERSON>", "Memory": "Bellek", "Memory (GiB)": "Bellek (GiB)", "Memory (MiB)": "Bellek (MiB)", "Memory Optimized": "Bellek Optimizasyonlu", "Memory Page": "Bellek Sayfası", "Memory Page Size": "Bellek Sayfa Boyutu", "Memory Usage": "Bellek Kullanımı", "Memory Usage(%)": "Bellek Kullanımı(%)", "Memory Usages (GiB)": "Bellek Kullanımları (GiB)", "Mesos": "Mesos", "Message": "<PERSON><PERSON>", "Message Details": "<PERSON><PERSON>", "Message Queue Service": "<PERSON><PERSON>", "Metadata": "<PERSON><PERSON>", "Metadata Definitions": "Meta Veri Tanımları", "Metadata Detail": "Meta Veri Detayı", "Mexico": "<PERSON><PERSON><PERSON>", "Micronesia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Migrate": "Taşı", "Migrate Volume": "Diski Taşı", "Migrate volume": "Diski taşı", "Migrating": "Taşınıyor", "Migrating To": "Buraya Taşınıyor", "Min Memory": "Minimum Bellek", "Min Memory (GiB)": "Minimum Bellek (GiB)", "Min System Disk": "Minimum Sistem Diski", "Min System Disk (GiB)": "Minimum Sistem Diski (GiB)", "Min size": "Minimum Boyut", "Min. Disk": "<PERSON><PERSON>", "Min. RAM": "Min. <PERSON>", "Minimum value is 68 for IPv4, and 1280 for IPv6.": "IPv4 için minimum değer 68 ve IPv6 için minimum değer 1280.", "Miscellaneous": "Çeşitli", "Missing IP Address": "Eksik IP Adresş", "Missing Port": "Eksik Ağ Adaptörü", "Missing Subnet": "Eksik Alt Ağ", "Missing Weight": "Eksik Ağırlık", "Modification Times": "Değiştirme Süreleri", "Modify Instance Tags": "<PERSON><PERSON> Makine Etiketlerini <PERSON>ğiştir", "Modify Project Tags": "<PERSON>je Etiketlerini Değiştir", "Modify QoS": "QoS Değiştir", "Moldova": "Moldova", "Monaco": "<PERSON><PERSON>", "Mongolia": "Moğolistan", "Monitor Center": "İzleyici Merkezi", "Monitor Overview": "İzleyici Görünümü", "Montenegro": "Karadağ", "Montserrat": "Montserrat", "More": "<PERSON><PERSON>", "More Actions": "<PERSON><PERSON>", "More than one label is required, such as: \"example.org.\"": "", "Morocco": "Fas", "Mount ISO": "ISO Bağla", "Mount snapshot support": "Anlık görüntü desteği bağlama", "Mozambique": "Mozambik", "Multiple filter tags are separated by enter": "Birden çok filtre etiketi enter tuşu ile ayrılır", "My Role": "<PERSON><PERSON>", "MySQL Actions": "MySQL Eylemleri", "Myanmar": "Myanmar", "N/A": "Uygulanamaz", "NET I/O(B)": "NET G/Ç(B)", "NFS": "NFS", "NOOP": "NOOP", "NUMA Node": "NUMA Düğümü", "NUMA Node Count": "NUMA Düğüm Sayısı", "NUMA Nodes": "NUMA Düğümleri", "Name": "İsim", "Name Server": "<PERSON><PERSON><PERSON>", "Name can not be duplicated": "İsim çoğaltılamaz.", "Name or ID og the container image": "Konteyner <PERSON>'nın İsmi veya ID'si", "Namespace": "Namespace", "Namibia": "Namibya", "Nauru": "Nauru", "Nepal": "Nepal", "Netherlands": "Hollanda", "Netherlands Antilles": "<PERSON><PERSON>", "Network": "Ağ", "Network Attaching": "<PERSON><PERSON><PERSON>", "Network Config": "<PERSON><PERSON>ı<PERSON>", "Network Detaching": "<PERSON><PERSON><PERSON>", "Network Detail": "<PERSON><PERSON>", "Network Driver": "<PERSON><PERSON>", "Network Dropped Packets": "Ağda Düşen <PERSON>", "Network Errors": "<PERSON><PERSON>ı", "Network ID": "<PERSON><PERSON>", "Network ID/Name": "", "Network Info": "<PERSON><PERSON>", "Network Interface": "Ağ Arayüzü", "Network Line": "<PERSON><PERSON>", "Network Name": "<PERSON><PERSON> Adı", "Network Service": "<PERSON><PERSON>", "Network Setting": "<PERSON><PERSON>", "Network Traffic": "<PERSON><PERSON>ra<PERSON>ği", "Network Type": "<PERSON><PERSON>", "Network topology page": "Ağ Topolojisi Sayfası", "Networking": "Ağ Bağlantısı", "Networking *": "Ağ Bağlantısı *", "Networks": "<PERSON><PERSON><PERSON>", "Neutron Agent Detail": "Neutron Ajan <PERSON>", "Neutron Agents": "Neutron Ajanları", "Neutron Net": "Neutron Ağı", "Neutron Service": "Neutron Hizmeti", "Neutron Subnet": "Neutron Alt Ağı", "New": "<PERSON><PERSON>", "New Availability Zone": "Yeni <PERSON>llanılabilirlik Bölgesi", "New Caledonia": "<PERSON><PERSON>", "New Status": "<PERSON><PERSON>", "New Tag": "Yeni Etiket", "New Volume": "<PERSON><PERSON>", "New Zealand": "<PERSON><PERSON>", "Next": "<PERSON><PERSON><PERSON>", "Next Hop": "<PERSON><PERSON><PERSON>", "Nicaragua": "Nikaragua", "Niger": "<PERSON><PERSON>", "Nigeria": "<PERSON><PERSON><PERSON>", "No": "Hay<PERSON><PERSON>", "No - Do not create a new system disk": "", "No Console": "Konsol Yok", "No Logs...": "Günlük Yok...", "No Monitor": "İzleyici Yok", "No Outputs": "Çıktı Yok", "No Proxy": "Proxy Yok", "No Raid": "Raid Yok", "No State": "<PERSON><PERSON>", "No Task": "<PERSON><PERSON><PERSON><PERSON>", "No Vender": "Satıcı Yok", "No default pool set": "", "Node": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Node Addresses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Node Driver": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Node Flavor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Node ID/Name": "Düğüm ID/Adı", "Node Info": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Node Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Node Spec": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Nodes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Nodes To Remove": "Kaldırılacak Düğümler", "Norfolk Island": "Norfolk Adası", "Normal": "Normal", "North Korea": "<PERSON><PERSON><PERSON>", "Northern Mariana Islands": "<PERSON><PERSON><PERSON>", "Norway": "Norveç", "Not Implemented (code: 501) ": "Uygulanmadı (kod: 501) ", "Not Open": "<PERSON><PERSON><PERSON>", "Not dealt with for the time being": "Şimdilik ele alınmadı", "Not deleted with the instance": "<PERSON>al makine ile silinmedi", "Not locked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Not select": "Seçilmedi", "Not yet bound": "<PERSON><PERSON><PERSON><PERSON> bağlanmadı", "Not yet selected": "<PERSON><PERSON><PERSON><PERSON>", "Note that when using a share type with the driver_handles_share_servers extra spec as False, you should not provide a share network.": "driver_handles_share_servers ek özelliği Yanlış olarak kullanılan paylaşım türü kullanıldığında, paylaşılan ağ sağlanmamalıdır.", "Note: Are you sure you need to modify the volume type?": "Not: Disk türünü değiştirmek istediğinize emin misiniz?", "Note: Please consider the container name carefully since it couldn't be changed after created.": "Not: Oluşturulduktan sonra değiştirilemeyeceği için lütfen konteyner adını dikkatli bir şekilde dü<PERSON>ü<PERSON>ün.", "Note: The security group you use will act on all virtual adapters of the instance.": "Not: Kullandığınız güvenlik grubu, sanal makinenin tüm sanal adaptörler üzerinde işlem yapacaktır.", "Notification Detail": "", "Notifications": "", "Nova Service": "Nova Hizmeti", "Number of GPU": "GPU Sayısı", "Number of IPs used by all projects": "Tüm projeler tarafından kullanılan IP sayısı", "Number of Master Nodes": "<PERSON>", "Number of Nodes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Number of Ports": "Ağ Adaptörleri Sayısı", "Number of Usb Controller": "USB Denetleyici Sayısı", "OK": "<PERSON><PERSON>", "OS": "İşlet<PERSON>mi", "OS Admin": "İşletim Sistemi Yöneticisi", "OS Disk": "İşletim Sistemi Diski", "OS Type": "İşletim Sistemi Türü", "OS Version": "İşletim Sistemi Sürümü", "OSDs": "OSD'ler", "OSPF": "OSPF", "Object": "", "Object Count": "Nesne Sayısı", "Object Count ": "Nesne Sayısı ", "Object ID": "", "Object ID/Name": "", "Object Name": "", "Object Storage": "<PERSON><PERSON><PERSON>", "Object Type": "", "Off": "<PERSON><PERSON><PERSON>", "Offline": "Çevrimdışı", "Oman": "<PERSON><PERSON>", "On": "Açık", "On Maintenance": "", "On failure": "<PERSON><PERSON>", "One entry per line(e.g. ***************)": "Her satıra bir giriş (örn. ***************)", "One entry per line(e.g. {ip})": "Her satıra bir giriş (örn. {ip})", "One-way authentication": "Tek yönlü kimlik doğrulama", "Online": "Çevrimiçi", "Online Resize": "Çevrimiçi Boyut Değiştirme", "Only a MAC address or an OpenFlow based datapath_id of the switch are accepted in this field": "Sad<PERSON>e anah<PERSON>ın bir MAC adresi veya OpenFlow tabanlı bir veriyolu_id'si bu alanda kabul edilir", "Only libvirt driver is supported.": "Yalnızca libvirt sürücüleri desteklenir.", "Only subnets that are already connected to the router can be selected.": "Yalnızca zaten yönlendiriciye bağlı olan alt ağlar seçilebilir.", "Open External Gateway": "Harici Ağ Geçidi Aç", "OpenID Connect": "OpenID Bağlantısı", "Operating Status": "İşletim Durumu", "Operating System": "İşlet<PERSON>mi", "Operation Center": "Operation Center", "Operation Name": "İşlem Adı", "Operation Time": "İşlem Saati", "Optimized Parameters": "Optimize Edilmiş Parametreler", "Optional list": "İsteğe bağlı liste", "Options": "Seçenekler", "Orchestration": "Orkestrasyon", "Orchestration Services": "Orkestrasyon Hizmetleri", "Orchestration information": "Orkestrasyon bilgisi", "Origin File Name": "Kaynak Dosya Adı", "Original Password": "Orijinal Şifre", "Other Protocol": "<PERSON><PERSON><PERSON>", "Other Service": "<PERSON><PERSON><PERSON>", "Other Services": "<PERSON><PERSON><PERSON>", "Others": "Di<PERSON><PERSON><PERSON><PERSON>", "Out Cluster": "<PERSON><PERSON><PERSON>", "Out of Sync": "Senkronizasyon Dışı", "Outputs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Overlapping allocation pools: {pools}": "Çak<PERSON>şan dağıtım havuzları: {pools}", "Overlay": "Overlay", "Overlay2": "Overlay2", "Overview": "Genel Bakış", "Owned Network": "Sahipli Ağ", "Owned Network ID": "Sahipli Ağ <PERSON>ği", "Owned Network ID/Name": "Sahip olunan Ağ Kimliği/Adı", "Owned Project": "<PERSON><PERSON><PERSON>", "Owned Subnet": "Sahipli Alt Ağ", "Owner": "Sahip", "Ownership of a volume can be transferred from one project to another. The transfer process of the volume needs to perform the transfer operation in the original owner's project, and complete the \"accept\" operation in the receiver's project.": "Bir diskin sahipliği bir projeden başka bir projeye aktarılabilir. Diskin transfer işlemi, diskin orijinal sahibinin projesinde transfer işleminin yapılması ve alıcının projesinde \"kabul et\" işleminin tamamlanmasını gerekmektedir.", "PEM encoding": "PEM kodlaması", "PFS": "PFS", "PG Count": "PG Sayısı", "PGM": "PGM", "PING": "PING", "PTR Domain Name": "PTR Alan <PERSON>", "PXE": "PXE", "PXE Enabled": "PXE <PERSON>", "Pakistan": "Pakistan", "Palau": "<PERSON><PERSON>", "Palestine": "<PERSON><PERSON><PERSON>", "Panama": "Panama", "Papua New Guinea": "Papua Yeni Gine", "Paraguay": "Paraguay", "Parameter": "Parametre", "Params Setting": "Parametre Ayarı", "Password": "Şifre", "Password Type": "<PERSON><PERSON><PERSON>", "Password changed successfully, please log in again.": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>, lütfen tekrar giriş ya<PERSON>ı<PERSON>.", "Password must be the same with confirm password.": "Ş<PERSON>re, doğrulama şifresiyle aynı olmalıdır.", "Paste": "Yapıştır", "Paste File": "<PERSON><PERSON><PERSON>", "Path": "Yol", "Pause": "<PERSON><PERSON><PERSON>", "Pause Container": "<PERSON><PERSON><PERSON><PERSON>", "Pause Instance": "<PERSON><PERSON>", "Paused": "Duraklatıldı", "Pausing": "Duraklatılıyor", "Payload": "", "Peer": "Çift", "Peer Address": "Çift Adresi", "Peer Cidrs": "Çift Cidrs", "Peer Endpoint Group": "Çift Uç Nokta Grubu", "Peer Endpoint Group ID": "Çift Uç Nokta Grubu Kimliği", "Peer Gateway Public Address": "Çift Ağ Geçidi Genel Adresi", "Peer ID": "Çift Kimliği", "Peer Network": "Çift Ağı", "Peer Network Segment": "Çift Ağ Segmenti", "Peer gateway public address for the IPsec site connection": "IPsec site bağlantısı için çift ağ geçidi genel adresi", "Pending": "Beklemede", "Pending Create": "Oluşturulmaya Bekliyor", "Pending Delete": "Silinmeye Bekliyor", "Pending Update": "Güncellenmeye Bekliyor", "Perform a consistent hash operation on the source IP address of the request to obtain a specific value. At the same time, the back-end server is numbered, and the request is distributed to the server with the corresponding number according to the calculation result. This can enable load distribution of visits from different source IPs, and at the same time enable requests from the same client IP to always be dispatched to a specific server. This method is suitable for load balancing TCP protocol without cookie function.": "İsteğin kaynak IP adresi üzerinde tutarlı bir kesişim işlemi yaparak belirli bir değeri elde edin. <PERSON><PERSON>ı zamanda, arkayüz sunucular numaralandırılır ve istek, hesaplama sonucuna göre ilgili numaraya sahip sunucuya dağıtılır. Bu, farklı kaynak IP'lerden yapılan ziyaretlerin yük dağıtımını mümkün kılar ve aynı zamanda aynı istemci IP'lerinden gelen isteklerin her zaman belirli bir sunucuya gönderilmesini sağlar. Bu yöntem, çerez işlevi olmadan yük dengeleme TCP protokolü için uygundur.", "Permanent": "Kalıcı", "Persistent": "<PERSON><PERSON><PERSON><PERSON>", "Peru": "Peru", "Phase1 Negotiation Mode": "Faz1 Müzakere Modu", "Philippines": "<PERSON><PERSON><PERSON><PERSON>", "Phone": "Telefon", "Physical CPU Usage": "Fiziksel CPU Kullanımı", "Physical Network": "Fiziksel Ağ", "Physical Node": "Fiziksel Düğüm", "Physical Nodes": "Fiziksel Düğümler", "Physical Storage Usage": "Fiziksel Depolama Kullanımı", "Pitcairn": "Pitcairn", "Platform Info": "Platform Bilgisi", "Please confirm your password!": "Lütfen şifrenizi onaylayın!", "Please enter JSON in the correct format!": "Lütfen JSON'u doğru formatta girin!", "Please enter URL!": "Lütfen URL girin!", "Please enter a correct certificate content, format is refer to the left tip!": "Lütfen doğru bir sertifika içeriği girin, biç<PERSON> için sol ipuçlarına bakınız!", "Please enter a correct domain, format is refer to the left tip!": "Lütfen doğru bir alan adı girin, biç<PERSON> için sol ipuçlarına bakınız!", "Please enter a correct private key, format is refer to the left tip!": "Lütfen doğru bir özel anahtar girin, biç<PERSON> için sol ipuçlarına bakınız!", "Please enter a file link starting with \"http://\" or \"https://\"!": "Lütfen \"http://\" veya \"https://\" ile başlayan bir dosya bağlantısı girin!", "Please enter a memory page size, such as: 1024, 1024MiB": "Lütfen bellek sayfa boy<PERSON>u girin, örneğin: 1024, 1024MiB", "Please enter a valid ASCII code": "Lütfen geçerli bir ASCII kodu girin", "Please enter a valid Email Address!": "Lütfen geçerli bir e-posta adresi girin!", "Please enter a valid IPv4 value.": "Lütfen geçerli bir IPv4 değeri girin.", "Please enter a valid IPv6 value.": "Lütfen geçerli bir IPv6 değeri girin.", "Please enter a valid Phone Number": "Lütfen geçerli bir telefon numarası girin", "Please enter complete key value!": "Lütfen tam bir anahtar de<PERSON> girin!", "Please enter right format custom trait!": "Lütfen doğru biçimde özel nitelik girin!", "Please enter right format key value!": "Lütfen doğru biçimde anahtar değeri girin!", "Please enter right format memory page value!": "Lütfen doğru biçimde bellek sayfa değeri girin!", "Please enter right format trait!": "Lütfen doğru biçimde özellik girin!", "Please enter the correct id": "Lütfen doğru ID girin", "Please enter the server id to be reduced, and separate different id with \",\"": "Azaltılacak sunucu ID'sini girin ve farklı ID'leri \",\" ile ayırın", "Please fill in the peer network segment and subnet mask of CIDR format, the written subnets should be under the same router, one per line.": "CIDR biçimindeki eş ağ segmenti ve alt ağ maskesini doldurun, yazılı alt ağlar aynı yönlendirici altında satır başına bir adet olmalıdır.", "Please input": "Lütfen girin", "Please input <username> or <username>@<domain name>!": "Lütfen <kullanıcı adı> veya <kullanıcı adı>@<etki alanı adı> girin!", "Please input ICMP code(0-255)": "Lütfen ICMP kodu girin (0-255)", "Please input ICMP type(0-255)": "Lütfen ICMP tür<PERSON> girin (0-255)", "Please input IPv4 or IPv6 cidr": "Lütfen IPv4 veya IPv6 cidr girin", "Please input IPv4 or IPv6 cidr, (e.g. ***********/24, 2001:DB8::/48)": "Lütfen IPv4 veya IPv6 cidr girin, (örn. ***********/24, 2001:DB8::/48)", "Please input a number": "", "Please input a parameter": "", "Please input a valid ip!": "Lütfen geçerli bir IP adresi girin!", "Please input a value": "", "Please input at least 2 characters.": "Lütfen en az 2 karakter girin.", "Please input at least one record": "", "Please input auth key": "Lütfen doğrulama anahtarı girin", "Please input cipher": "Lütfen şifreleme girin", "Please input cluster name": "Lütfen küme adı girin", "Please input cluster template name": "Lütfen küme taslak adı girin", "Please input complete data": "Lütfen eksiksiz veri girin", "Please input container name": "Lütfen konteyner adı girin", "Please input file name": "Lütfen dosya adı girin", "Please input image": "Lütfen imaj girin", "Please input ip address": "", "Please input ipv4": "Lütfen ipv4 girin", "Please input ipv6": "Lütfen ipv6 girin", "Please input key": "Lütfen anahtar girin", "Please input key and value": "Lütfen anahtar ve değer girin", "Please input key size": "<PERSON>ü<PERSON><PERSON> anahtar boyutu girin", "Please input metadata": "Lütfen meta verileri girin", "Please input name": "<PERSON><PERSON><PERSON><PERSON> isim verin", "Please input or load Template from a file": "Lütfen taslağı dosyadan yükleyin veya girin", "Please input port and protocol": "Lütfen bağlantı noktası ve protokol girin", "Please input prefix": "Lütfen önek girin", "Please input protocol number if it absent in select list.": "<PERSON><PERSON><PERSON> seçim <PERSON> yoksa, lütfen protokol numarasını girin.", "Please input provider": "Lütfen sağlayıcı girin", "Please input snapshot name": "Lütfen anlık görüntü adı girin", "Please input the correct format:  <username> or <username>@<domain name>.": "Lütfen doğru biçimi girin: <kullanıcı adı> veya <kullanıcı adı>@<etki alanı adı>.", "Please input transfer id": "Lütfen transfer ID girin", "Please input user name": "Lütfen kullanıcı adı girin", "Please input value": "<PERSON>ü<PERSON><PERSON> değer girin", "Please input your Password!": "Lütfen şifrenizi girin!", "Please input your Username!": "Lütfen kullanıcı adınızı girin!", "Please input your current password!": "Lütfen mevcut şifrenizi girin!", "Please input your password!": "Lütfen şifrenizi girin!", "Please input {label}": "Lütfen {label} girin", "Please input {label}!": "Lütfen {label} girin!", "Please make sure this IP address be available to avoid creating VM failure.": "SM (sanal makine) oluşturma hatasını önlemek için bu IP adresinin kullanılabilir olduğundan emin olun.", "Please make sure this IP address be available.": "Bu IP adresinin kullanılabilir olduğundan emin olun.", "Please note that when deleting a domain, all projects, users, and user groups under the domain will be deleted directly!": "", "Please reasonably plan the network and subnet to which the virtual network card belongs.": "Sanal ağ kartının ait olduğu ağ ve alt ağı uygun bir şekilde planlayın.", "Please save your token properly and it will be valid for {left}.": "Lütfen token'ınızı düzgün bir şekilde sa<PERSON>. {left} süresi boyunca geçerli olacaktır.", "Please select": "Lütfen seçin", "Please select a file": "Lütfen bir dos<PERSON> se<PERSON>", "Please select a file with the suffix {types}": "Lütfen {types} uzantısı olan bir dosya seçin", "Please select a network!": "Lütfen bir ağ seçin!", "Please select a parameter": "", "Please select a subnet!": "Lütfen bir alt ağ seçin!", "Please select a type!": "Lütfen bir tür seçin!", "Please select availability zone": "Lütfen kullanılabilirlik bölgesi seçin", "Please select image driver": "Lütfen imaj sürücüsü seçin", "Please select item!": "Lütfen öğe seçin!", "Please select login type!": "Lütfen giriş türünü seçin!", "Please select policy": "Lütfen ilke seçin", "Please select source": "Lütfen kaynak seçin", "Please select type": "<PERSON>üt<PERSON> tür se<PERSON>in", "Please select volume type": "Lütfen disk türü se<PERSON>in", "Please select your Region!": "Lütfen bölgenizi seçin!", "Please select {label}!": "Lütfen {label} seçin!", "Please select {name} first": "<PERSON><PERSON><PERSON><PERSON> önce {name} se<PERSON><PERSON>", "Please select: {name} or an image file that is the same as it": "Lütfen seçin: {name} veya onunla aynı bir gö<PERSON><PERSON><PERSON><PERSON> dos<PERSON>ı", "Please set CPU && Ram first.": "Lütfen önce CPU ve RAM ayarlayın.", "Please set MUNA": "Lütfen MUNA'yı a<PERSON>ın", "Please set a size no less than {minSize} GiB!": "", "Please set at least one role!": "Lütfen en az bir rol belirleyin!", "Please set the system disk size!": "", "Please upload files smaller than { size }GiB on the page. It is recommended to upload files over { size }GiB using API.": "Lütfen sayfada { size }GiB'den daha küçük dosyalar yükleyin. API kullanarak { size }GiB'den büyük dosyaları yüklemeniz önerilir.", "Pointer Record": "İşaretçi Kaydı", "Poland": "Polonya", "Policy": "<PERSON><PERSON><PERSON>", "Policy Detail": "", "Policy Edit": "", "Policy Name": "<PERSON><PERSON><PERSON>", "Policy Rules": "", "Pool Algorithm": "Havuz Algoritması", "Pool Description": "Havuz Açıklaması", "Pool Detail": "Havuz Detayı", "Pool ID": "<PERSON><PERSON><PERSON>", "Pool Info": "Ha<PERSON>z Bilgisi", "Pool Name": "Havuz Adı", "Pool Protocol": "Havuz Protokolü", "Pools": "Havuzlar", "Port": "Ağ <PERSON>ptörü", "Port Count": "Ağ Adaptörü Sayısı", "Port Detail": "Ağ Adaptörü Detayı", "Port Forwardings": "Ağ Adaptörü Yönlendirmeleri", "Port Group": "Ağ Adaptörü Grubu", "Port Groups": "Ağ Adaptörü Grupları", "Port ID": "Ağ Adaptörü ID'si", "Port Info": "Ağ Adaptörü Bilgisi", "Port Range": "Ağ Adaptörü Aralığı", "Port Security": "Ağ Adaptörü Güvenliği", "Port Security Enabled": "Ağ Adaptörü Güvenliği Etkin", "Port Type": "Ağ Adaptörü Türü", "Ports": "<PERSON><PERSON>", "Ports are either single values or ranges": "Ağ Adaptörleri tek değerler veya aralıklar olabilir", "Ports provide extra communication channels to your containers. You can select ports instead of networks or a mix of both, If the terminal port and the network are selected at the same time, note that the terminal port is not a terminal port of the selected network, and the container under the same network will only be assigned one IP address (The port executes its own security group rules by default).": "Ağ <PERSON>örle<PERSON>, konteynerlerinize ek iletişim kanalları sağlar. Ağlar yerine veya her ikisini karıştırarak ağ adaptörleri seçebilirsiniz, Terminal ağ adaptörü ve ağ aynı anda seçildiğinde, terminal ağ adaptörünün seçilen ağın bir terminal ağ adaptörü olmadığına dikkat edin ve aynı ağ altındaki konteynere yalnızca bir IP adresi atanır (Ağ adaptörü varsayılan olarak kendi güvenlik grubu kurallarını uygular).", "Ports provide extra communication channels to your instances. You can select ports instead of networks or a mix of both (The port executes its own security group rules by default).": "<PERSON><PERSON>, sanal makineleriniz için ek iletişim kanalları sağlar. Ağlar yerine veya her ikisini karıştırarak ağ adaptörleri seçebilirsiniz (Ağ adaptörü varsayılan olarak kendi güvenlik grubu kurallarını uygular).", "Portugal": "Portekiz", "Power Off": "Ka<PERSON><PERSON>", "Power On": "Aç", "Power State": "<PERSON><PERSON><PERSON>", "Powering Off": "Kapatılıyor", "Powering On": "Açılıyor", "Pre Live Migration": "Canlı Taşıma <PERSON>i", "Pre-Shared Key must be the same with Confirm Shared Key.": "Önceden Paylaşılan Anahtar, Onaylanan Paylaşılan Anahtar ile aynı olmalıdır.", "Pre-Shared Key(PSK) String": "Önceden Paylaşılan Anahtar (PSK) Dizisi", "Prefer": "<PERSON><PERSON><PERSON>", "Prefer(Thread siblings are preferred)": "Tercih(Eş Dişli Thread tercih edilir)", "Preferred": "<PERSON><PERSON><PERSON>", "Prefix": "Önek", "Prep Resize": "Yeniden Boyutlandırmayı Hazırla", "Prepare Template": "Taslağı Hazırla", "Previous": "<PERSON><PERSON><PERSON>", "Primary": "Birincil", "Primary is controlled by Designate, Secondary zones are slaved from another DNS Server.": "", "Private": "<PERSON><PERSON>", "Private Key": "<PERSON><PERSON>", "Profile": "Profil", "Progress": "<PERSON><PERSON><PERSON><PERSON>", "Project": "<PERSON><PERSON>", "Project Console": "<PERSON><PERSON>", "Project Detail": "<PERSON><PERSON>", "Project ID": "Proje ID'si", "Project ID/Name": "Proje ID/Adı", "Project Name": "<PERSON><PERSON>", "Project Num": "<PERSON><PERSON>", "Project Quota": "<PERSON>je <PERSON>", "Project Range": "<PERSON>je <PERSON>", "Project Scope": "<PERSON><PERSON>", "Project Scope (Project Name: Role Names)": "<PERSON><PERSON> (Proje Adı: Rol Adları)", "Project User Groups": "<PERSON>je <PERSON> Grupları", "Project Users": "<PERSON><PERSON>", "Projects": "<PERSON><PERSON><PERSON>", "Promote": "Yükselt", "Properties": "<PERSON><PERSON><PERSON><PERSON>", "Protected": "<PERSON><PERSON><PERSON>", "Protocol": "Protokol", "Protocol Type": "Protokol Türü", "Provider": "Sağlayıcı", "Provider Network Type": "Sağlayıcı Ağ Türü", "Provider Physical Network": "Sağlayıcı Fiziksel Ağı", "Provision State": "<PERSON><PERSON><PERSON><PERSON>", "Provisioning Status": "<PERSON><PERSON><PERSON><PERSON>", "Public": "<PERSON><PERSON>", "Public Access": "<PERSON><PERSON>", "Public Address": "<PERSON><PERSON>", "Public Images": "<PERSON><PERSON>", "Public Key": "<PERSON><PERSON>", "Published In": "Yayınlandı", "Published Out": "Yayınlandı", "Puerto Rico": "Porto Riko", "QCOW2 - QEMU image format": "QCOW2 - QEMU imaj formatı", "Qatar": "<PERSON><PERSON>", "QoS Bandwidth Egress Limit": "QoS Bant Genişliği Çıkış Sınırı", "QoS Bandwidth Ingress Limit": "QoS Bant Genişliği Giriş Sınırı", "QoS Bandwidth Limit": "QoS Bant Genişliği Sınırı", "QoS Detail": "QoS Detayı", "QoS Policies": "QoS İlkeleri", "QoS Policy": "QoS İlkesi", "QoS Policy Detail": "QoS İlkesi Detayı", "QoS Policy ID": "QoS İlkesi ID", "QoS Policy ID/Name": "QoS İlkesi ID/Adı", "QoS Spec": "QoS Özelleştirmesi", "QoS Spec ID": "QoS Özelleştirmesi ID", "QoS Specs": "QoS Özelleştirmesi", "QoS policies": "QoS ilkeleri", "Qos Policy": "QoS ilkesi", "Queued": "<PERSON><PERSON><PERSON><PERSON>", "Queued To Apply": "Uygulama İçin Sıraya Alındı", "Queued To Deny": "Red İçin Sı<PERSON>a <PERSON>", "Quota": "Kota", "Quota Overview": "Kota Genel Görünüm", "Quota exceeded": "Kota aşıldı", "Quota is not enough for extend share.": "Paylaşım genişletmek için yeterli kota yok.", "Quota is not enough for extend volume.": "Disk genişletmek için yeterli kota yok.", "Quota of key pair means: the number of allowed key pairs for each user.": "<PERSON><PERSON><PERSON> ç<PERSON> k<PERSON>, her kullanı<PERSON><PERSON> için izin verilen anahtar çifti sayısını ifade eder.", "Quota: Insufficient quota to create resources, please adjust resource quantity or quota(left { quota }, input { input }).": "Kota: Kaynakları oluşturmak için yeterli kota yok, lütfen kaynak miktarını veya kotayı ayarlayın (kalan { quota }, giri<PERSON> { input }).", "Quota: Insufficient { name } quota to create resources, please adjust resource quantity or quota(left { left }, input { input }).": "Kota: { name } i<PERSON><PERSON> kota yok, lütfen kaynak miktarını veya kotayı ayarlayın (kalan { quota }, giri<PERSON> { input }).", "Quota: Insufficient { name } quota to create resources.": "Kota: Kaynakları oluşturmak için için <PERSON> { name } kotası yok, ", "Quota: Project quotas sufficient resources can be created": "Kota: <PERSON>je kota<PERSON> ile yeterli kaynak oluşturulabilir", "RAM": "RAM", "RAM (MiB)": "RAM (MiB)", "RAW - Raw disk image format": "RAW - Ham disk imaj formatı", "RBAC Policies": "", "RBAC Policy Detail": "", "REJECT": "", "RESTORE COMPLETE": "GERİ YÜKLEME TAMAMLANDI", "RESUME COMPLETE": "DEVAM ET TAMAMLANDI", "RESUME FAILED": "DEVAM ET BAŞARISIZ", "ROLLBACK COMPLETE": "GERİ ALMA TAMAMLANDI", "ROLLBACK FAILED": "GERİ ALMA BAŞARISIZ", "ROLLBACK IN PROGRESS": "GERİ ALMA DEVAM EDİYOR", "ROUND_ROBIN": "ROUND_ROBIN", "RSVP": "RSVP", "Raid Interface": "Raid Arayüzü", "Ram Size (GiB)": "<PERSON> (GiB)", "Ram value is { ram }, NUMA RAM value is { totalRam }, need to be equal. ": "<PERSON> de<PERSON> { ram } ,NUMA RAM değeri { totalRam }'ne e<PERSON>it o<PERSON>lıdı<PERSON>.", "Ramdisk ID": "Ramdisk ID'si", "Ramdisk Image": "Ramdisk İmajı", "Rbac Policy": "", "Read and write": "<PERSON>uma ve yazma", "Read only": "<PERSON><PERSON><PERSON>", "Real Name": "Gerçek İsim", "Reason": "<PERSON><PERSON>", "Reason: ": "Neden: ", "Reboot": "<PERSON><PERSON><PERSON> b<PERSON>", "Reboot Container": "<PERSON><PERSON><PERSON><PERSON>", "Reboot Database Instance": "Veritabanı Sanal Makinesini Yeniden Başlat", "Reboot Instance": "<PERSON><PERSON>", "Rebooting": "Yeniden başlatılıyor", "Rebuild": "Yeniden oluştur", "Rebuild Block Device Mapping": "Blok Cihaz Eşlemesini Yeniden Oluştur", "Rebuild Container": "Konteyneri <PERSON>ş<PERSON>", "Rebuild Instance": "<PERSON><PERSON> Ma<PERSON>eyi Yeniden Oluştur", "Rebuild Spawning": "Oluşturma Yeniden Oluştur", "Rebuilding": "Yeniden oluşturuluyor", "Rebuilt": "<PERSON><PERSON>den oluşturuldu", "Recently a day": "Son bir gün i<PERSON>e", "Record Sets": "<PERSON><PERSON><PERSON>", "Records": "<PERSON><PERSON>tlar", "Recordset Detail": "Kayıt Seti Detayı", "Recordsets Detail": "Kayıt Setleri Detayı", "Recover": "<PERSON><PERSON>", "Recovering": "<PERSON><PERSON> yapılıyor", "Recovery Method": "", "Recycle Bin": "<PERSON><PERSON><PERSON><PERSON>", "Region": "<PERSON><PERSON><PERSON>", "Registry Enabled": "<PERSON>ıt Defteri Etkinleştirildi", "Related Policy": "", "Related Resources": "<PERSON><PERSON><PERSON><PERSON>", "Release": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Release Fixed IP": "Sabit IP Adresini Serbest Bırak", "Remote Group Id": "Uzak Grup Kimliği", "Remote IP Prefix": "Uzak IP Öneki", "Remote Security Group": "Uzak Güvenlik Grubu", "Remote Type": "<PERSON><PERSON>", "Remove": "Kaldır", "Remove Default Project": "", "Remove Network": "<PERSON>ğı Kaldır", "Remove Router": "Yönlendiriciyi Kaldır", "Remove Rule": "", "Remove default project for user": "", "Rename": "<PERSON><PERSON><PERSON>", "Rename is to copy the current file to the new file address and delete the current file, which will affect the creation time of the file.": "<PERSON><PERSON><PERSON>, mevcut dosyanın yeni dosya adresine kopyalanması ve mevcut dosyanın silinmesi anlamına gelir; bu, dosyan<PERSON>n oluşturma zamanını etkiler.", "Replication Change": "Çoğaltma Değişikliği", "Report Count": "<PERSON><PERSON>", "Republic of the Congo": "<PERSON><PERSON>", "Request ID": "İstek ID'si", "Require": "<PERSON><PERSON><PERSON><PERSON>", "Require(Need multithreading)": "Gerekli (Çoklu işleme gerektirir)", "Required Data Disk": "Gerekli V<PERSON> Di<PERSON>", "Rescue": "<PERSON><PERSON>", "Rescued": "Kurtarıldı", "Rescuing": "<PERSON><PERSON> yapılıyor", "Reserved": "Ayrılmış", "Reset Status": "<PERSON><PERSON><PERSON>", "Reset To Initial Value": "Başlangıç Değerine Sıfırla", "Reset failed, please retry": "Sıfırlama başar<PERSON><PERSON><PERSON>z, lütfen yeniden deneyin", "Resize": "<PERSON><PERSON><PERSON>ı<PERSON>", "Resize Cluster": "<PERSON><PERSON><PERSON>", "Resize Instance": "<PERSON><PERSON>", "Resize Volume": "Diski Yeniden Boyutlandır", "Resized": "Yeniden boyutlandırıldı", "Resizing or Migrating": "Yeniden boyutlandırılıyor veya Taşınıyor", "Resource": "<PERSON><PERSON><PERSON>", "Resource Class": "Kaynak Sınıfı", "Resource Class Properties": "Kaynak Sınıfı Özellikleri", "Resource Id": "Kaynak ID'si", "Resource Not Found": "Kaynak Bulunamadı", "Resource Pool": "<PERSON><PERSON><PERSON>", "Resource Status": "<PERSON><PERSON><PERSON>", "Resource Status Reason": "<PERSON><PERSON><PERSON>", "Resource Type": "Kaynak Tü<PERSON>ü", "Resource Types": "<PERSON><PERSON><PERSON>", "Resources Synced": "Senkronize Ed<PERSON>", "Resource Monitor": "<PERSON><PERSON><PERSON>", "Restart": "<PERSON><PERSON><PERSON> b<PERSON>", "Restart Container": "<PERSON><PERSON><PERSON><PERSON>", "Restart Database Service": "Veritabanı Hizmetini Yeniden Başlat", "Restarting": "Yeniden başlatılıyor", "Restore Backup": "Yedeklemey<PERSON>", "Restore From Snapshot": "Anlık Görüntüden Geri Yükle", "Restore backup": "Yedeklemey<PERSON>", "Restore from snapshot": "Anlık Görüntüden Geri Yükle", "Restoring": "<PERSON><PERSON>", "Restoring Backup": "Ye<PERSON><PERSON><PERSON>", "Restricted": "Sınırlı", "Restricted Situation": "Kısıtlı Durum", "Resume": "<PERSON><PERSON>", "Resume Complete": "Devam Etme Tamamlandı", "Resume Failed": "Devam Etme Başarısız", "Resume In Progress": "<PERSON><PERSON>", "Resume Instance": "<PERSON><PERSON>", "Resuming": "<PERSON><PERSON>", "Retry times for restart on failure policy": "Hata ilkesi için tekrar deneme sü<PERSON>", "Retyping": "<PERSON><PERSON><PERSON>", "Reunion": "Yeniden Birleştir", "Reverse DNS Detail": "Ters DNS Detayı", "Reverse Detail": "<PERSON><PERSON>", "Reverse Dns": "Ters DNS", "Revert Resize or Migrate": "Yeniden Boyutlandırmayı veya Taşımayı Geri Al", "Revert Resize/Migrate": "Yeniden Boyutlandırmayı veya Taşımayı Geri Al", "Reverting": "<PERSON><PERSON>ı<PERSON>", "Reverting Error": "<PERSON><PERSON> <PERSON>", "Reverting Resize or Migrate": "Yeniden Boyutlandırmayı/Taşımayı Geri Alınıyor", "Role": "Rol", "Role Detail": "Rol Detayı", "Role Name": "Rol Adı", "Roles": "Roller", "Rollback Complete": "<PERSON><PERSON> <PERSON>", "Rollback Failed": "<PERSON><PERSON> <PERSON>", "Rollback In Progress": "<PERSON><PERSON>", "Romania": "<PERSON><PERSON>", "Root Disk": "Kök Disk", "Root Disk (GiB)": "Kök Disk (GiB)", "Root directory": "<PERSON><PERSON><PERSON>", "Router": "Yönlendirici", "Router Advertisements Mode": "Yönlendirici Reklam Modu", "Router Detail": "Yönlendirici Detayı", "Router External": "<PERSON><PERSON>", "Router ID": "Yönlendirici ID'si", "Router Port": "", "Routers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Rule": "", "Rule Action": "", "Rule Detail": "", "Rule Edit": "", "Rule Numbers": "Kural Numaraları", "Rules": "<PERSON><PERSON><PERSON>", "Rules Number": "Kuralların <PERSON>ı<PERSON>ı", "Running": "Çalışıyor", "Running Threads": "Çalışan İş Parçacıkları", "Running Time": "Çalışma Süresi", "Runtime": "Çalışma Zamanı", "Russia": "<PERSON><PERSON><PERSON>", "Rwanda": "<PERSON><PERSON><PERSON>", "SCTP": "SCTP", "SNAPSHOT COMPLETE": "ANLIK GÖRÜNTÜ TAMAMLANDI", "SNAT Enabled": "SNAT Etkin", "SNI Certificate": "SNI Sertifikası", "SNI Enabled": "SNI Etkin", "SOURCE_IP": "KAYNAK_IP", "SSH Public Key Fingerprint": "SSH Genel Anahtar <PERSON>", "SSL Parsing Method": "SSL Ayrıştırma Yöntemi", "Saint Vincent and the Grenadines": "<PERSON>dinler", "Same subnet with LB": "YD ile aynı alt ağ", "Samoa": "Samoa", "San Marino": "San Marino", "Sao Tome and Principe": "Sao Tome ve Principe", "Saudi Arabia": "Suudi Arabistan", "Saving": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Scheduler Hints": "Zamanlama İpuçları", "Scheduling": "Zamanlama", "Search": "<PERSON><PERSON>", "Sec for DPD delay, > 0": "DPD gecikmesi için sn, > 0", "Sec for DPD timeout, > 0 & > DPD Interval": "DPD zaman aşımı için sn, > 0 & > DPD Aralığı", "Secondary": "İkincil", "Security Group": "Güvenlik Grubu", "Security Group Detail": "Güvenlik Grubu Detayı", "Security Group Info": "Güvenlik Grubu Bilgisi", "Security Group Num:": "Güvenlik Grubu Numarası:", "Security Group Rule": "Güvenlik Grubu Kuralı", "Security Group Rules": "Güvenlik Grubu Kuralları", "Security Groups": "Güvenlik Grupları", "Security Groups Adding": "Güvenlik Grupları Ekleniyor", "Security Groups Removing": "Güvenlik Grupları Kaldırılıyor", "Security Info": "Güvenlik Bilgisi", "Segment Detail": "", "Segment ID": "", "Segment Name": "", "Segmentation ID": "Se<PERSON><PERSON><PERSON>", "Segmentation Id": "Se<PERSON><PERSON><PERSON>", "Segments": "", "Select File": "<PERSON><PERSON><PERSON>", "Select Project": "<PERSON><PERSON>", "Select Project Role": "<PERSON>je <PERSON>", "Select User Group": "Kullanıcı Grubu Seçin", "Select Volume Snapshot": "Disk Anlık Görüntüsü Seçin", "Select a QoS Policy": "", "Select a login type": "<PERSON><PERSON> giriş türü seçin", "Select a network": "", "Select a project": "", "Select a region": "<PERSON><PERSON> bö<PERSON> se<PERSON>", "Select an object type": "", "Selected": "Seçilen", "Selected Members": "Seçilen <PERSON>", "Selected list": "Seçilen liste", "Sender Policy Framework": "Gönderici İlke Yapısı", "Senegal": "Senegal", "Serbia": "Sırbistan", "Serial": "Seri", "Server Certificate": "<PERSON><PERSON><PERSON>", "Server Certificates": "<PERSON><PERSON><PERSON>", "Server Group": "<PERSON><PERSON><PERSON>", "Server Group Detail": "Sunucu Grubu Detayı", "Server Group Member": "<PERSON><PERSON><PERSON>", "Server Groups": "<PERSON><PERSON><PERSON>", "Server Status": "<PERSON><PERSON><PERSON>", "Server Type": "<PERSON><PERSON><PERSON>", "Service": "Hizmet", "Service List": "<PERSON>z<PERSON>", "Service Locator": "Hizmet <PERSON>", "Service Port ID": "Hizmet Ağ Adaptörü Kimliği", "Service State": "<PERSON><PERSON><PERSON>", "Service Status": "<PERSON><PERSON><PERSON>", "Service Status Updated": "<PERSON>z<PERSON>", "Service Type": "", "Service Unavailable (code: 503) ": "<PERSON><PERSON><PERSON> (kod: 503) ", "Services": "<PERSON><PERSON><PERSON><PERSON>", "Set": "<PERSON><PERSON><PERSON>", "Set Admin Password": "Yönetici Parolasını Ayarla", "Set Boot Device": "Önyükleme Aygıtını Ayarla", "Set Default Project": "", "Set Domain Name PTR": "Alan <PERSON>ı PTR'sini <PERSON>", "Set IP": "IP Ayarla", "Set default project for user": "", "Seychelles": "<PERSON><PERSON><PERSON><PERSON>", "Share": "Paylaş", "Share Capacity (GiB)": "Paylaşım <PERSON>i (GiB)", "Share Detail": "Paylaşım Detayı", "Share File Storage": "<PERSON><PERSON><PERSON>ını Paylaş", "Share Group": "Paylaşım Grubu", "Share Group Detail": "Paylaşım Grubu Detayı", "Share Group Type": "Paylaşım Grubu Türü", "Share Group Type Detail": "Paylaşım Grubu Türü Detayı", "Share Group Types": "Paylaşım Grubu Türleri", "Share Groups": "Paylaşım Grupları", "Share Id": "Paylaşım ID'si", "Share Instance": "Paylaşım Sanal Makine", "Share Instance Detail": "Paylaşım Sanal Makine Detayı", "Share Instances": "Paylaşım Sanal Ma<PERSON>eleri", "Share Network": "Paylaşım Ağı", "Share Network Detail": "Paylaşım Ağı Detayı", "Share Network Subnet": "Paylaşım Ağı Alt Ağı", "Share Network Subnets": "Paylaşım Ağı Alt Ağları", "Share Networks": "Paylaşım Ağları", "Share Protocol": "Paylaşım Protokolü", "Share Replica ID": "Paylaşım <PERSON>", "Share Server": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Share Server Detail": "Paylaşım Sunucusu Detayı", "Share Servers": "Paylaşım Sunucuları", "Share Type": "Paylaşım Türü", "Share Type Detail": "Paylaşım Türü Detayı", "Share Type ID": "Paylaşım Türü Kimliği", "Share Type Name": "Paylaşım Türü Adı", "Share Types": "Pay<PERSON>ş<PERSON><PERSON>", "Shared": "Paylaşılan", "Shared Images": "Paylaşılan Görü<PERSON>ü<PERSON>", "Shared Network": "Paylaşılan Ağ", "Shared Networks": "Paylaşılan Ağlar", "Shared QoS Policies": "Paylaşılan QoS İlkeleri", "Shared QoS Policy": "", "Shared policy only can insert shared rules.": "", "Shares": "Paylaşımlar", "Shelve": "<PERSON><PERSON>", "Shelve Instance": "<PERSON><PERSON> Makineyi Rafa <PERSON>ldı<PERSON>", "Shelved": "Rafa Kaldırıldı", "Shelved Offloaded": "Rafa Kaldırılan Boşaltıldı", "Shelving": "<PERSON><PERSON>", "Shelving Image Pending Upload": "İmajı Rafa Kaldırma Bekliyor", "Shelving Image Uploading": "İmajı Rafa Kaldırma Yükleniyor", "Shelving Offloading": "<PERSON>fa Kaldırma Boşaltılıyor", "Show All Domain": "<PERSON><PERSON><PERSON> Göster", "Show Instance": "<PERSON><PERSON>", "Show all Data": "<PERSON><PERSON><PERSON> Verileri Göster", "Shrinking": "<PERSON><PERSON><PERSON><PERSON>", "Shrinking Error": "Daraltma Hatası", "Shrinking Possible Data Loss Error": "Daraltma Olası Veri Kaybı Hatası", "Shut Down": "Ka<PERSON><PERSON>", "Shut Off": "Ka<PERSON><PERSON>", "Shutoff": "Kapatıldı", "Sierra Leone": "Sierra Leone", "Sign Out": "Çıkış Yap", "Sign up": "<PERSON><PERSON><PERSON>", "Signal to send to the container: integer or string like SIGINT. When not set, SIGKILL is set as default value and the container will exit. The supported signals varies between platform. Besides, you can omit \"SIG\" prefix.": "Konteynere gönderilecek sinyal: tamsayı veya SIGINT gibi bir dize. <PERSON><PERSON><PERSON>mad<PERSON><PERSON><PERSON><PERSON>, varsay<PERSON><PERSON> değer olarak SIGKILL ayarlanır ve konteyner çıkış yapar. Desteklenen sinyaller platformlar arasında değişir. Ayrıca, \"SIG\" öneki hariç tutabilirsiniz.", "Singapore": "Singapur", "Size": "<PERSON><PERSON>", "Size (GiB)": "Boyut (GiB)", "Slovakia (Slovak Republic)": "Slovakya (Slovak Cumhuriyeti)", "Slovenia": "Slovenya", "Slow Query": "Yavaş Sorgu", "Small": "Küçük", "Small(Not recommended)": "Küçük (Önerilmez)", "Smart Scheduling": "Akıllı Zamanlama", "Snapshot Complete": "Anlık Görüntü Tamamlandı", "Snapshot Failed": "Anlık Görüntü Başarısız", "Snapshot In Progress": "Anlık Görüntü İlerlemede", "Snapshot Instance": "Sanal Makinenin Anlık Görüntüsü", "Snapshot Source": "Anlık Görüntü Kaynağı", "Snapshots can be converted into volume and used to create an instance from the volume.": "<PERSON><PERSON><PERSON><PERSON>, disk<PERSON><PERSON> dön<PERSON>ştürülerek diskten bir sanal makine oluşturmak için kullanılabilir.", "Snapshotting": "Anlık Görüntüleme", "Soft Delete Instance": "<PERSON><PERSON>", "Soft Deleted": "<PERSON><PERSON><PERSON><PERSON>", "Soft Deleting": "<PERSON><PERSON><PERSON><PERSON>", "Soft Power Off": "Yumuşak <PERSON>", "Soft Reboot": "Yu<PERSON>şak <PERSON>", "Soft Reboot Instance": "Yumuşak Yeniden Başlatma ile Sanal Makine", "Soft Rebooting": "Yumuşak Yeniden Başlatılıyor", "Soft-Affinity": "Yumuşak-Afinite", "Soft-Anti-Affinity": "Yumuşak-Anti-Afinite", "Solomon Islands": "<PERSON>", "Somalia": "Somali", "Sorry, the page you visited does not exist.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, z<PERSON><PERSON> ettiğiniz sayfa mevcut değil.", "Source": "<PERSON><PERSON><PERSON>", "Source IP": "", "Source IP Address/Subnet": "", "Source Path: {path}": "<PERSON><PERSON><PERSON>: {path}", "Source Port": "", "Source Port/Port Range": "Kaynak Ağ Adaptörü/Ağ Adaptör Aralığı", "South Africa": "G<PERSON>ney Afrika", "South Korea": "<PERSON><PERSON><PERSON>", "Spain": "İspanya", "Spawning": "Oluşturuluyor", "Spec": "Spesif<PERSON><PERSON><PERSON>", "Specification": "Özellik", "Specify Physical Node": "Fiziksel Düğümü Belirt", "Specify mount point.": "Bağlama noktasını belirtin.", "Specify the client IP address": "", "Specify the listener port": "", "Specify whether future replicated instances will be created on the same hypervisor (affinity) or on different hypervisors (anti-affinity). This value is ignored if the instance to be launched is a replica.": "Gelecekteki replike edilmiş Sanal Makinelerin aynı hipervizörde (afinite) veya farklı hipervizörlerde (anti-afinite) oluşturulup oluşturulmayacağını belirtin. <PERSON><PERSON> <PERSON>, başlatılacak sanal makinenin bir replika olduğunda yoksayılır.", "Specs": "<PERSON><PERSON><PERSON><PERSON>", "Sri Lanka": "Sri Lanka", "Stack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Stack Detail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Stack Events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Stack Faults": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Stack ID": "Yığın ID'si", "Stack Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Stack Resource": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Stack Resource Type": "<PERSON><PERSON><PERSON><PERSON><PERSON> Türü", "Stack Resources": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Stack Status": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Stacks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Stand Alone Ports Supported": "Tek Ağ Adaptörleri <PERSON>", "Standard Trait": "<PERSON><PERSON>", "Start": "<PERSON><PERSON><PERSON>", "Start Container": "<PERSON><PERSON><PERSON><PERSON>", "Start Instance": "<PERSON><PERSON>", "Start Of Authority": "Yetkilendirme Başlangıcı", "Start Source": "Kaynağı Başlat", "Start Source Name": "Kaynak Adını Başlat", "Start Time": "Başlama Zamanı", "Start auto refreshing data": "Otomatik veri ye<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "Start refreshing data every {num} seconds": "Her {num} saniyede bir veriyi ye<PERSON><PERSON><PERSON>i ba<PERSON>lat", "Started At": "Başlangıç <PERSON>", "Startup Parameters": "Başlangıç Parametreleri", "State": "Durum", "Static Routes": "Statik Rotalar", "Stats Information": "<PERSON><PERSON>il<PERSON>", "Status": "Durum", "Status Code": "<PERSON><PERSON>", "Status Detail": "Durum Detayı", "Status Reason": "<PERSON><PERSON>", "Stop": "<PERSON><PERSON><PERSON>", "Stop Container": "<PERSON><PERSON><PERSON><PERSON>", "Stop Database Service": "Veritabanı Hizmetini Durdur", "Stop Instance": "<PERSON><PERSON>", "Stop auto refreshing data": "Otomatik veri ye<PERSON><PERSON><PERSON>i durdur", "Stop refreshing data every {num} seconds": "Her {num} saniyede bir veriyi yeni<PERSON>eyi durdur", "Stopped": "Durdu", "Storage": "<PERSON><PERSON><PERSON>", "Storage Backends": "Depolama Altyapıları", "Storage Capacity(GiB)": "<PERSON><PERSON><PERSON>(GiB)", "Storage Cluster Bandwidth": "Depolama Kümesi Bant Genişliği", "Storage Cluster IOPS": "Depolama Kümesi IOPS", "Storage Cluster OSD Latency": "Depolama Kümesi OSD Gecikmesi", "Storage Cluster Status": "<PERSON><PERSON><PERSON>", "Storage Cluster Usage": "Depolama Kümes<PERSON>", "Storage Clusters": "<PERSON><PERSON><PERSON>", "Storage IOPS": "Depolama IOPS", "Storage Interface": "Depolama Arayüzü", "Storage Policy": "Depolama Politikası", "Storage Pool Capacity Usage": "Depolama Havuzu Kapasite Kullanımı", "Storage Types": "<PERSON><PERSON><PERSON>", "Sub Users": "Alt Kullanıcılar", "Subnet": "Alt Ağ", "Subnet Count": "Alt Ağ Sayısı", "Subnet Detail": "Alt Ağ Detayı", "Subnet ID": "Alt Ağ ID'si", "Subnet ID/Name": "Alt Ağ ID/Adı", "Subnet Name": "Alt Ağ Adı", "Subnets": "Alt Ağlar", "Subordinate Projects": "<PERSON><PERSON>", "Subordinate User Groups": "Ast Kullanıcı Grupları", "Succeeded": "Başarılı", "Success": "Başarılı", "Sudan": "Sudan", "Supports resumable transfer (recommended when uploading a large image)": "Destek kesme noktası devamı (büyük görüntüler yüklerken önerilir)", "Suriname": "Surinam", "Suspend": "Askıya Al", "Suspend Complete": "Askıya Alma Tamamlandı", "Suspend Failed": "Askıya Alma Başarısız", "Suspend In Progress": "Askıya Alma İlerlemekte", "Suspend Instance": "Sanal Makineyi Askıya Al", "Suspended": "Askıya Alındı", "Suspending": "Askıya Alma İşlemi Devam Ediyor", "Swaziland": "<PERSON><PERSON><PERSON><PERSON>", "Sweden": "İsveç", "Switch ID": "Anahtarlayıcı Kimliği", "Switch Info": "Anahtarlayıcı Bilgisi", "Switch Language": "<PERSON><PERSON>", "Switch Project": "<PERSON><PERSON>", "Switzerland": "İsviçre", "Syncing": "Senkronize Ediliyor", "Syrian Arab Republic": "<PERSON><PERSON><PERSON>", "System": "Sistem", "System Config": "Sistem Yapılandırması", "System Disk": "Siste<PERSON>", "System Info": "Sistem Bilgisi", "System Load": "Sistem Yükü", "System Roles": "Siste<PERSON>", "System Running Time": "Sistem Çalışma Süresi", "System is error, please try again later.": "Sistem<PERSON> hata <PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar den<PERSON>in.", "TCP": "TCP", "TCP Connections": "TCP Bağlantıları", "TLS Disabled": "TLS Devre Dışı", "TTL": "TTL", "TTL (Time to Live) for the zone.": "", "Tag is no longer than 60 characters": "Etiket en fazla 60 karakter olmalıdır", "Tags": "<PERSON><PERSON><PERSON><PERSON>", "Tags Info": "Etiketler Hakkında Bilgi", "Tags are not case sensitive": "Etiketler büyük-küçük harf duyarlı değildir", "Taiwan": "Tayvan", "Tajikistan": "Tacikistan", "Take effect after restart": "Yeniden başlatıldıktan sonra geçerli olur", "Tanzania": "Tanzanya", "Target Compute Host": "<PERSON><PERSON><PERSON>", "Target IP Address": "Hedef <PERSON> Adresi", "Target Port": "<PERSON><PERSON><PERSON>ptörü", "Target Project": "", "Target Project ID": "", "Target Project ID/Name": "", "Target Project Name": "", "Target Storage Backend": "<PERSON><PERSON><PERSON>", "Target Tenant": "", "Task State": "<PERSON><PERSON><PERSON><PERSON>", "Template Content": "Taslak İçeriği", "Template Name": "Taslak Adı", "Text Record": "<PERSON><PERSON>", "Thailand": "Tayland", "That is, after how many consecutive failures of the health check, the health check status of the back-end cloud server is changed from normal to abnormal": "<PERSON><PERSON>, sağlık kontrolündeki ardışık kaç başarısızlıktan sonra, arkayüz bulut sunucusunun sağlık durumu normalden anormal olarak değiştirilir", "The DNS nameserver to use for this cluster template": "Bu küme taslağı için kullanılacak DNS ad sunucusu", "The Federation of Saint Kitts and Nevis": "Saint Kitts ve Nevis <PERSON>", "The Provider is the encryption provider format (e.g. \"luks\")": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, şifrel<PERSON>e <PERSON>ğlayıcı <PERSON>ıdı<PERSON> (örn. \"luks\")", "The Republic of Macedonia": "<PERSON><PERSON><PERSON>", "The Republic of South Sudan": "Güney Sudan Cumhuriyeti", "The SSH key is a way to remotely log in to the cluster instance. If it’s not set, the value of this in the template will be used.": "SSH anahtarı, küme sanal makinesine uzaktan giriş yapmanın bir yoludur. Ayarlanmamışsa, taslağın değeri kullanılacaktır.", "The SSH key is a way to remotely log in to the cluster instance. The cloud platform only helps to keep the public key. Please keep your private key properly.": "SSH anahtarı, küme sanal makinesine uzaktan giriş yapmanın bir yoludur. Bulut platformu yalnızca genel anahtarı korumaya yardımcı olur. Özel anahtarınızı lütfen uygun şekilde saklayın.", "The SSH key is a way to remotely log in to the instance. The cloud platform only helps to keep the public key. Please keep your private key properly.": "SSH anahtarı, sanal makineye uzaktan giriş yapmanın bir yoludur. Bulut platformu yalnızca genel anahtarı korumaya yardımcı olur. Özel anahtarınızı lütfen uygun şekilde saklayın.", "The amphora instance is required for load balancing service setup and is not recommended": "Amfora sanal ma<PERSON>, yük dengeleme hizmeti kurulumu için gereklidir ve önerilmez", "The associated floating IP, virtual adapter, volume and other resources will be automatically disassociated.": "İlgili değişken IP, sanal adaptör, Disk ve diğer kaynaklar otomatik olarak ilişkilendirmeyi kaldırır.", "The certificate contains information such as the public key and signature of the certificate. The extension of the certificate is \"pem\" or \"crt\", you can directly enter certificate content or upload certificate file.": "<PERSON><PERSON><PERSON><PERSON>, genel anahtar ve sertifikanın imzası gibi bilgiler içerir. Sertifikanın uzantısı \"pem\" veya \"crt\"dir, doğrudan sertifika içeriğini girebilir veya sertifika dosyasını yükleyebilirsiniz.", "The changed node count can not be equal to the current value": "Değiştirilen düğüm sayısı mevcut değere eşit olamaz", "The command to execute": "Yürütülecek komut", "The container memory size in MiB": "MiB cinsinden konteyner bellek boyutu", "The container runtime tool to create container with": "Konteyner oluşturmak için konteyner çalıştırma aracı", "The creation instruction has been issued, please refresh to see the actual situation in the list.": "Oluşturma talimatı verildi, gerçek durumu görmek için lütfen yenileyin.", "The creation instruction was issued successfully, instance: {name}. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.": "Oluşturma talimatı ba<PERSON><PERSON><PERSON><PERSON> veril<PERSON>, Sanal Makine: {name}. \n Liste verilerinin değişimini takip etmek için birkaç saniye bekleyebilir veya verileri manuel olarak yenileyerek nihai görüntü sonucunu alabilirsiniz.", "The current operation requires the instance to be shut down:": "Geçerli işlem için sanal makinenin kapatılması gerekmektedir:", "The current platform has not yet enabled the {name} management module. Please contact the administrator to enable it": "Mevcut platform henüz {name} y<PERSON><PERSON>im modülünü etkinleştirmedi. Etkinleştirmek için lütfen yöneticiyle iletişime geçin", "The description can be up to 255 characters long.": "Açıklama en fazla 255 karakter uzunluğunda olabilir.", "The disk size in GiB for per container": "Her konteyner i<PERSON>in GiB cinsinden disk boyutu", "The domain name can only be composed of letters, numbers, dashes, in A dash cannot be at the beginning or end, and a single string cannot exceed more than 63 characters, separated by dots; At most can support 30 domain names, separated by commas;The length of a single domain name does not exceed 100 characters, and the total length degree does not exceed 1024 characters.": "<PERSON> adı yaln<PERSON><PERSON><PERSON> harf<PERSON>, <PERSON><PERSON><PERSON><PERSON>, tireler içerebilir; Bir tire başında veya sonunda olamaz ve tek bir dize 63 karakterden fazla olamaz; Noktalarla ayrılmış şekilde en fazla 30 alan adını destekleyebilir; Tek bir alan adının uzunluğu 100 karakteri geçemez ve toplam uzunluk 1024 karakteri aşmamalıdır.", "The entire inspection process takes 5 to 10 minutes, so you need to be patient. After the registration is completed, the node configuration status will return to the manageable status.": "Tüm inceleme süreci 5 ila 10 dakika sürer, bu nedenle sabırlı olmanız gerekir. <PERSON><PERSON><PERSON> tama<PERSON>ra, dü<PERSON><PERSON><PERSON> yapılandırma durumu yönetilebilir duruma dönecektir.", "The entrypoint which overwrites the default ENTRYPOINT of the image": "Görü<PERSON><PERSON><PERSON><PERSON>n <PERSON>sayılan ENTRYPOINT'unu üzerine yazan giriş noktası", "The feasible configuration of cloud-init or cloudbase-init service in the image is not synced to image's properties, so the Login Name is unknown.": "İmajdaki cloud-init veya cloudbase-init hizmetinin uygun yapılandırması imaj özellikleriyle senkronize edilmemiştir, bu nedenle Giriş Adı bilinmemektedir.", "The file with the same name will be overwritten.": "<PERSON><PERSON>ı isimdeki dosya üzerine yazılacaktır.", "The floating IP configured with port forwardings cannot be bound": "Ağ adaptörü yönlendirmeleri ile yapılandırılmış değişken IP bağlanamaz", "The format of the certificate content is: by \"----BEGIN CERTIFICATE-----\" as the beginning,\"-----END CERTIFICATE----\" as the end, 64 characters per line, the last line does not exceed 64 characters, and there cannot be blank lines.": "Sertifika içeriğinin formatı şöyledir: başlangıçta \"----SERTİFİKA BAŞLANGICI-----\" olarak, sonunda \"-----SERTİFİKA SONU----\" olar<PERSON>, satır baş<PERSON>na 64 karakter, son satır 64 karakteri aşmaz ve boş satırlar olmamalıdır.", "The host name of this container": "<PERSON>u konteynerin ana bilgisayar adı", "The http_proxy address to use for nodes in cluster": "<PERSON><PERSON>med<PERSON><PERSON> düğümler için kullanılacak http_proxy adresi", "The https_proxy address to use for nodes in cluster": "<PERSON><PERSON><PERSON><PERSON><PERSON> dü<PERSON> iç<PERSON> https_proxy adresi", "The image is not existed": "<PERSON><PERSON><PERSON> mev<PERSON>", "The instance architecture diagram mainly shows the overall architecture composition of the instance. If you need to view the network topology of the instance, please go to: ": "Sanal makine mimari <PERSON>, özellikle sanal makinenin genel mimari yapısını gösterir. Eğer sanal makinenin ağ topolojisini görmek isterseniz, lütfen şuraya gidin: ", "The instance deleted immediately cannot be restored": "<PERSON><PERSON><PERSON> silinen sanal makine geri yü<PERSON>nemez", "The instance has been locked. If you want to do more, please unlock it first.": "Sanal makine kilitlenmiştir. Daha fazla işlem yapmak istiyorsanız, lütfen önce kilidini açın.", "The instance is not shut down, unable to restore.": "<PERSON><PERSON> makine ka<PERSON>ılmadığından geri yüklenemiyor.", "The instance which is boot from volume will create snapshots for each mounted volumes.": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> sanal makine, her bir bağlanan disk i<PERSON><PERSON> anlık gö<PERSON><PERSON><PERSON><PERSON><PERSON> oluşturacaktır.", "The instances in the affinity group are allocated to the same physical machine as much as possible, and when there are no more physical machines to allocate, the normal allocation strategy is returned.": "Afinite grubundaki sanal makineler mümkün olduğunca aynı fiziksel makineye tahsis edilir ve başka tahsis edilecek fiziksel makine kalmadığında normal tahsis stratejisi uygulanır.", "The instances in the affinity group are strictly allocated to the same physical machine. When there are no more physical machines to allocate, the allocation fails.": "Afinite grubundaki sanal makineler kesin olarak aynı fiziksel makineye tahsis edilir. Başka tahsis edilecek fiziksel makine kalmadığında tahsis başarısız olur.", "The instances in the anti-affinity group are allocated to different physical machines as much as possible. When there are no more physical machines to allocate, the normal allocation strategy is returned.": "Anti-afinite grubundaki sanal makineler mümkün olduğunca farklı fiziksel makineler üzerine tahsis edilir ve başka tahsis edilecek fiziksel makine kalmadığında normal tahsis stratejisi uygulanır.", "The instances in the anti-affinity group are strictly allocated to different physical machines. When there are no more physical machines to allocate, the allocation fails.": "Anti-afinite grubundaki sanal makineler kesin olarak farklı fiziksel makinelere tahsis edilir. Başka tahsis edilecek fiziksel makine kalmadığında tahsis başarısız olur.", "The ip address {ip} is duplicated, please modify it.": "", "The ip is not within the allocated pool!": "IP tahsis edilen havuzun içinde değil!", "The ip of external members can be any, including the public network ip.": "Dış üyelerin IP adresleri herhangi bir olabilir, bunlar arasında dışarıya açık ağ IP'si de bulunur.", "The key pair allows you to SSH into your newly created instance. You can select an existing key pair, import a key pair, or generate a new key pair.": "<PERSON><PERSON><PERSON>, yeni o<PERSON>ulan sanal makineye SSH ile giriş yapmanıza izin verir. Varolan bir anahtar çifti seçebilir, bir anahtar çifti içe aktarabilir veya yeni bir anahtar çifti oluşturabilirsiniz.", "The kill signal to send": "Gönderilecek sonlandırma <PERSON>", "The limit of cluster instance greater than or equal to 1.": "Küme sanal makinesinin sınırı 1'den büyük veya eşit olmalıdır.", "The maximum batch size is {size}, that is, the size of the port range cannot exceed {size}.": "<PERSON><PERSON><PERSON>um toplu boyut {size} olarak belirlenmiştir, yani ağ <PERSON>ö<PERSON>ü aralığının boyutu {size}'ı geçemez.", "The maximum transmission unit (MTU) value to address fragmentation. Minimum value is 68 for IPv4, and 1280 for IPv6.": "Adres <PERSON>lanmasıyla başa çıkmak için kullanılan maksimum veri iletim Diski (MTU) değeri. Minimum değer IPv4 için 68, IPv6 için 1280'dir.", "The min size is {size} GiB": "Minimum boyut {size} GiB", "The name of the physical network to which a port is connected": "Bir ağ adaptörünün bağlandığı fiziksel ağın adı", "The name should be end with \".\"": "", "The name should contain letter or number, the length is 1 to 16, characters can only contain \"0-9, a-z, A-Z, -, _.\"": "İsim harf veya rakam içermeli, uzunluğu 1 ile 16 arasında olmalı ve karakterler yalnızca \"0-9, a-z, A-Z, -, _.\" karakterlerini içermelidir.", "The name should contain letter or number, the length is 2 to 64, characters can only contain \"0-9, a-z, A-Z, -, _.\"": "İsim harf veya rakam <PERSON>, uzunluğu 2 ile 64 arasında olmalı ve karakterler yalnızca \"0-9, a-z, A-Z, -, _.\" karakterlerini içermelidir.", "The name should start with letter or number, and be a string of 2 to 255, characters can only contain \"0-9, a-z, A-Z, -, _, .\"": "<PERSON>sim harf veya rakam ile başlamalı ve 2 ile 255 arasında karakter dizisi o<PERSON>, karak<PERSON>ler yalnızca \"0-9, a-z, A-Z, -, _, .\" içerebilir.", "The name should start with upper letter or lower letter, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].:^\".": "İsim büyük harf veya küçük harfle başlamalı ve 1 ile 128 arasında bir dize olma<PERSON>ı<PERSON>, ka<PERSON><PERSON><PERSON> yalnızca \"0-9, a-z, A-Z, \"-'_()[].:^\" içerebilir.", "The name should start with upper letter or lower letter, characters can only contain \"0-9, a-z, A-Z, -, _, .\"": "İsim büyük harf veya küçük harfle başlamalıdır ve karakterler yalnızca \"0-9, a-z, A-Z, -,.\" içerebilir.", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].\".": "", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].:^\".": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> harf, küçük harf veya Çince ile başlamalı ve 1 ila 128 arasında bir dize o<PERSON><PERSON>, ka<PERSON><PERSON><PERSON> yalnız<PERSON> \"0-9, a-z, A-Z, \"-'_()[].:^\" içerebilir.", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_.\".": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> harf, küçük harf veya Çince ile başlamalı ve 1 ila 128 arasında bir dize o<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> yalnız<PERSON> \"0-9, a-z, A-Z, \"-'_.\" içerebilir.", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 64, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].^\".": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> harf, k<PERSON><PERSON>ük harf veya Çince ile başlamalı ve 1 ila 64 arasında bir dize o<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> yalnız<PERSON> \"0-9, a-z, A-Z, \"-'_()[].^\" içerebilir.", "The name should start with upper letter, lower letter or chinese, and be a string of 3 to 63, characters can only contain \"0-9, a-z, A-Z, chinese, -, .\".": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> harf, k<PERSON><PERSON><PERSON>k harf veya Çince ile başlamalı ve 3 ila 63 arasında bir dize o<PERSON><PERSON>, ka<PERSON><PERSON><PERSON> yalnız<PERSON> \"0-9, a-z, A-Z, chinese, -, .\" içerebilir.", "The name should start with upper letter, lower letter, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, -, _\".": "İsim büyük harf veya küçük harfle başlamalı ve 1 ila 128 arasında bir dize olmal<PERSON><PERSON>r, ka<PERSON><PERSON><PERSON> yalnız<PERSON> \"0-9, a-z, A-Z, -, _\" içerebilir.", "The name should start with upper letter, lower letter, and be a string of 2 to 255, characters can only contain \"0-9, a-z, A-Z, -, ., _\".": "İsim büyük harf veya küçük harfle başlamalı ve 2 ila 255 arasında bir dize olmalıdır, ka<PERSON><PERSON><PERSON> yalnızca \"0-9, a-z, A-Z, -, ., _\" içerebilir.", "The name should start with upper letter, lower letter, and be a string of 3 to 63, characters can only contain \"0-9, a-z, A-Z, -\".": "İsim büyük harf veya küçük harfle başlamalı ve 3 ila 63 arasında bir dize olma<PERSON><PERSON>, ka<PERSON><PERSON><PERSON> yalnız<PERSON> \"0-9, a-z, A-Z, -\" içerebilir.", "The new password cannot be identical to the current password.": "Yeni şifre mevcut şifre ile aynı olamaz.", "The no_proxy address to use for nodes in cluster": "<PERSON><PERSON><PERSON><PERSON><PERSON> dü<PERSON>ü<PERSON> için k<PERSON>anılacak no_proxy adresi", "The number of allowed key pairs for each user.": "Her kullanıcı için izin verilen anahtar çifti sayısı.", "The number of vCPU cores should not exceed the maximum number of CPU cores of the physical node. Otherwise it will cause fail to schedule to any physical node when creating instance.": "vCPU çekirdek sayısı, fiziksel düğümün maksimum CPU çekirdek sayısını aşmamalıdır. <PERSON><PERSON><PERSON>, bir sanal makine oluşturulurken herhangi bir fiziksel düğüme zamanlama hatası nedeniyle planlama yapılamaz.", "The number of virtual cpu for this container": "Bu konteyner için sanal CPU sayısı", "The optional headers to insert into the request before it is sent to the backend member.": "", "The password must not be the same as the previous": "Ş<PERSON>re öncekiyle aynı olmamalıdır.", "The password must not be the same as the previous two": "Şifre önceki iki şifreyle aynı olmamalıdır.", "The password must not be the same as the previous {num}": "<PERSON><PERSON><PERSON> {num} <PERSON><PERSON><PERSON><PERSON> aynı olmamalıdır.", "The port created here will be automatically deleted when detach. If you need a reusable port, please go to the Virtual Adapter page to create and attach the port to instance.": "Burada oluşturulan ağ adaptörü, çıkarma işlemi gerçekleştirildiğinde otomatik olarak silinecektir. Tekrar kullanılabilir bir ağ adaptörü ihtiyacınız varsa, lütfen sanal adaptör sayfasına giderek ağ adaptörünü oluşturun ve sanal makineye bağlayın.", "The private key content format is: with \"-----BEGIN RSA PRIVATE KEY-----\" as the beginning,\"-----END RSA PRIVATE KEY-----\" as the end, 64 characters per line, the last line does not exceed 64 characters, and there cannot be blank lines.": "Özel anahtar içerik formatı: başlangıçta \"-----RSA ÖZEL ANAHTAR BAŞLANGICI-----\", sonunda \"-----RSA ÖZEL ANAHTAR SONU-----\", satır başına 64 karakter, son satır 64 karakteri geçmemeli ve boş satırlar olmamalıdır.", "The private key of the certificate, the extension of the private key is \"key\", you can directly enter the content of the private key file or upload a private key that conforms to the format document.": "<PERSON><PERSON><PERSON><PERSON><PERSON>n özel anahtarı, özel anahtarın uzantısı \"key\"dir, özel anahtar dosyasının içeriğini doğrudan girebilir veya BİÇİM belgesine uygun bir özel anahtar yükleyebilirsiniz.", "The resource class of the scheduled node needs to correspond to the resource class name of the flavor used by the ironic instance (for example, the resource class name of the scheduling node is baremetal.with-GPU, and the custom resource class name of the flavor is CUSTOM_BAREMETAL_WITH_GPU=1).": "Zamanlanan düğümün kaynak sınıfı, ironik sanal makine tarafından kullanılan şablonun kaynak sınıfı adına karşılık gelmelidir (<PERSON><PERSON><PERSON>in, zamanlama düğümünün kaynak sınıfı adı baremetal.with-GPU ve şablonun özel kaynak sınıfı adı CUSTOM_BAREMETAL_WITH_GPU=1).", "The resource has been deleted": "<PERSON><PERSON><PERSON>.", "The root and os_admin are default users and cannot be created!": "root ve os_admin var<PERSON><PERSON> k<PERSON>ı<PERSON>ır ve oluşturulamazlar!", "The root disk of the instance has snapshots": "", "The security group is similar to the firewall function and is used to set up network access control. ": "Güvenlik grubu, güvenlik duvarı işlevine benzer ve ağ erişim kontrolü kurmak için kullanılır.", "The security group is similar to the firewall function for setting up network access control, or you can go to the console and create a new security group. (Note: The security group you selected will work on all virtual LANs on the instances.)": "Güvenlik grubu, ağ eri<PERSON>im kontrolü kurmak için güvenlik duvarı işlevine benzerdir veya konsola giderek yeni bir güvenlik grubu oluşturabilirsiniz. (Not: Seçtiğiniz güvenlik grubu, Sanal Makinelerdeki tüm sanal LAN'larda çalışacaktır.)", "The selected VPC/subnet does not have IPv6 enabled.": "Seçilen VPC/alt ağda IPv6 etkinleştirilmemiş.", "The selected network has no subnet": "Seçilen ağda alt ağ bulunmuyor", "The selected project is different from the project to which the network belongs. That is, the subnet to be created is not under the same project as the network. Please do not continue unless you are quite sure what you are doing.": "Se<PERSON><PERSON>n proje, ağın ait olduğu projeden farklıdır. <PERSON><PERSON>, oluşturulacak alt ağ, ağla aynı projenin altında değil. <PERSON>in değilseniz devam etmeyin.", "The session has expired, please log in again.": "Oturum süresi do<PERSON>ş, lütfen tekrar giriş yapın.", "The shelved offloaded instance only supports immediate deletion": "Raftan kaldırılmış devre dışı bırakılan Sanal Makine yalnızca anında silmeyi destekler", "The size of the external port range is required to be the same as the size of the internal port range": "Harici ağ adaptörü aralığının boyutu, da<PERSON><PERSON> ağ adaptörü aralığının boyutuyla aynı olmalıdır", "The start source is a template used to create an instance. You can choose an image or a bootable volume.": "Başlangıç <PERSON>, bir Sanal Makine oluşturmak için kullanılan bir şablondur. Bir imaj veya önyüklenebilir Disk seçebilirsiniz.", "The starting number must be less than the ending number": "Başlang<PERSON>ç numarası, bitiş numarasından küçük olmalıdır", "The timeout for cluster creation in minutes.": "<PERSON><PERSON>me oluşturma için zaman aşımı süresi (dakika cinsinden).", "The timeout period of waiting for the return of the health check request, the check timeout will be judged as a check failure": "Sağlık kontrol isteğinin dönüşünü beklemek için zaman aşımı süresi, zaman aşımı kontrolü bir kontrol hatası olarak değerlendirilecektir", "The total amount of data is { total }, and the interface can support downloading { totalMax } pieces of data. If you need to download all the data, please contact the administrator.": "Toplam veri miktarı { total } ve arayüz, { totalMax } veri parçasını indirmeyi destekleyebilir. Tüm verileri indirmeniz gerekiyorsa lütfen yöneticiyle iletişime geçin.", "The trait name of the flavor needs to correspond to the trait of the scheduling node; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all necessary traits (for example: the trait of the scheduling node has HW_CPU_X86_VMX trait, and the flavor adds HW_CPU_X86_VMX, it can be scheduled to this node for necessary traits).": "Şablon özelliğinin özellik adı, planlama düğümün<PERSON>n özelliğiyle uyumlu olmalıdır; gerekli özelliklerin hepsiyle donatılmış bare metal ile Sanal Makine yalnızca hesaplama hizmeti, sanal makineyi çizelgeleyecektir (örneğin: zamanlama düğümünün özelliğinde HW_CPU_X86_VMX özelliği bulunur ve Şablon HW_CPU_X86_VMX eklerse, bu düğüm için gerekli özellikler).", "The trait of the scheduled node needs to correspond to the trait of the flavor used by the ironic instance; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all the necessary traits (for example, the ironic instance which use the flavor that has HW_CPU_X86_VMX as a necessary trait, can be scheduled to the node which has the trait of HW_CPU_X86_VMX).": "Zamanlanan düğü<PERSON><PERSON><PERSON>, ironik sanal makinesinin kullandığı şablonun özelliğiyle uyumlu olmalıdır; gerekli özellikleri ironik sanal makinesine enjekte ederek, hesaplama hizmeti sanal makinesi yalnızca tüm gerekli özelliklere sahip bare metali çizelgeleyecektir (<PERSON><PERSON><PERSON><PERSON>, gerekli bir özellik olarak HW_CPU_X86_VMX'e sahip bir şablon kullanan ironik sanal makinesi, HW_CPU_X86_VMX özelliğine sahip olan düğüme çizelgeleyebilir).", "The unit suffix must be one of the following: Kb(it), Kib(it), Mb(it), Mib(it), Gb(it), Gib(it), Tb(it), Tib(it), KB, KiB, MB, MiB, GB, GiB, TB, TiB. If the unit suffix is not provided, it is assumed to be KB.": "Disk soneki şunlardan biri olmalıdır: Kb(it), <PERSON><PERSON>(it), <PERSON>b(it), <PERSON><PERSON>(it), Gb(it), Gib(it), Tb(it), Tib(it), KB, KiB, MB, MiB, GB, GiB, TB, TiB. Disk soneği sağlanmadıysa, KB olarak kabul edilir.", "The user has been disabled, please contact the administrator": "Kullanıcı devre dışı bırakıldı, lütfen yöneticiyle iletişime geçin", "The user needs to ensure that the input is a shell script that can run completely and normally.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, giri<PERSON>in tamamen ve normal şekilde çalışabilen bir shell olduğundan emin olmalıdır.", "The value of the upper limit of the range must be greater than the value of the lower limit of the range.": "Aralığın üst sınır <PERSON>, aralığın alt sınır değerinden büyük olmalıdır.", "The volume associated with the backup is not available, unable to restore.": "Yedeklemeyle ilişkilendirilen disk kullanılamıyor, geri <PERSON>.", "The volume status can be reset to in-use only when the previous status is in-use.": "Disk durumu yalnızca önceki durum in-use olduğunda kullanıma geri alınabilir.", "The volume type needs to be consistent with the volume type when the snapshot is created.": "Anlık görüntü oluşturulurken disk türü, disk tür<PERSON><PERSON> tutarlı olmalıdır.", "The volume type needs to set \"multiattach\" in the metadata to support shared volume attributes.": "Paylaşılan disk özelliklerini desteklemek için disk türü, meta verilerde \"multiattach\" olarak ayarlanmalıdır.", "The working directory for commands to run in": "Komutların çalıştığı çalışma dizini", "The zone name should end with \".\"": "", "The {action} instruction has been issued, instance: {name}. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.": "{action} ta<PERSON><PERSON><PERSON> veril<PERSON>, <PERSON>al Ma<PERSON>: {name}. \n List verisinin değişikliklerini takip etmek için birkaç saniye bekleyebilir veya sonuçları görmek için verileri manuel olarak yenileyebilirsiniz.", "The {action} instruction has been issued. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.": "{action} talimatı verildi. \n List verisinin değişikliklerini takip etmek için birkaç saniye bekleyebilir veya sonuçları görmek için verileri manuel olarak yenileyebilirsiniz.", "The {name} has already been used by other {resource}({content}), please change.": "{name} zaten başka bir {resource}({content}) tarafından kullanılmış, lütfen değiştirin.", "The {name} {ports} have already been used, please change.": "{name} {ports} zaten kullanılmış, lütfen değiştirin.", "There are resources that cannot {action} in the selected resources, such as:": "Seçilen kaynaklarda {action} yapamayan kaynaklar var, örneğin:", "There are resources that cannot {action} in the selected resources.": "Seçilen kaynaklarda {action} yapamayan kaynaklar var.", "There are resources under the project and cannot be deleted.": "<PERSON><PERSON><PERSON> kaynaklar bulunuyor ve silinemezler.", "There is currently a image that needs to continue uploading. Please refresh page and upload the image. The new image cannot be used for the time being.": "<PERSON><PERSON> <PERSON>, yüklemeye devam edilmesi gereken bir ayna var. Görüntünün yüklenmesini tamamlamak için lütfen yenileyin veya geri dönün. <PERSON><PERSON>, şimdilik kesme noktası devam işlevini kullanamaz.", "There is currently no file to paste.": "<PERSON><PERSON> anda yapıştırılacak dosya yok.", "This operation creates a security group with default security group rules for the IPv4 and IPv6 ether types.": "<PERSON><PERSON> işlem, IPv4 ve IPv6 ether türleri i<PERSON><PERSON> var<PERSON>lan güvenlik grubu kuralları olan bir güvenlik grubu oluşturur.", "This service will automatically query the configuration (CPU, memory, etc.) and MAC address of the physical machine, and the ironic-inspector service will automatically register this information in the node information.": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ma<PERSON>eni<PERSON> (CPU, bellek, vb.) ve MAC adresini otomatik olarak sorgular ve ironic-inspector hizmeti bu bilgileri otomatik olarak düğüm bilgilerine kaydeder.", "This will delete all child objects of the load balancer.": "<PERSON><PERSON>, yük dengel<PERSON>nin tüm alt nesnelerini silecektir.", "Threads Activity Trends": "İş Parçacığı Etkinlik Trendleri", "Time Interval: ": "Zaman Aralığı:", "Time To Live": "<PERSON><PERSON><PERSON>", "Time To Live in seconds.": "Saniye cinsinden Yaş<PERSON>", "Time between running the check in seconds": "Saniye cinsinden kontrol çalıştırma aralığı", "Timeout(Minute)": "Zaman Aşımı(Dakika)", "Timeout(s)": "Zaman Aşımı(Saniye)", "Tips: without domain means \"Default\" domain.": "İpuçları: <PERSON> ad<PERSON> \"Default\" alan adı anlamına gelir.", "To open": "Açmak için", "Today CPU usage > 80% alert": "Bugünkü CPU kullanımı > 80% uyarısı", "Today Memory usage > 80% alert": "Bugünkü bellek kullanımı > 80% uyarısı", "Togo": "Togo", "Tokelau": "Tokelau", "Tonga": "Tonga", "Too many disks mounted on the instance will affect the read and write performance. It is recommended not to exceed 16 disks.": "Sanal makineye bağlanan çok fazla disk, okuma ve yazma performansını etkileyecektir. 16 diski aşmamanız önerilir.", "Topic": "<PERSON><PERSON>", "Topology": "<PERSON><PERSON><PERSON>", "Total": "Toplam", "Total Capacity": "Toplam Kapasite", "Total Connections": "Toplam Bağlantılar", "Total Consumers": "Toplam Tüketici", "Total Containers": "Toplam Konteynerler", "Total Exchanges": "<PERSON><PERSON>", "Total IPs": "Toplam IP'ler", "Total Queues": "Toplam Kuyruklar", "Total Ram": "Toplam RAM", "Total {total} items": "Toplam {total} öğe", "Trait Properties": "Özellik Özellikleri", "Traits": "<PERSON><PERSON><PERSON><PERSON>", "Transfer ID": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transfer Name": "Aktarım Adı", "Transferred": "Aktarıldı", "Transform Protocol": "Dönüş<PERSON>m <PERSON>", "Trinidad and Tobago": "Trinidad ve Tobago", "True": "Do<PERSON><PERSON>", "Tunisia": "<PERSON><PERSON>", "Turkey": "Türkiye", "Turkmenistan": "Türkmenistan", "Turks and Caicos Islands": "Turks ve Caicos Adaları", "Tuvalu": "Tuvalu", "Two-way authentication": "İki yönlü kimlik doğrulama", "Type": "<PERSON><PERSON><PERSON>", "UDP": "UDP", "UDPLite": "UDPLite", "UNHEALTHY": "İYİ DEĞİL", "UNKNOWN": "BİLİNMEYEN", "UOS": "UOS", "UPDATE COMPLETE": "GÜNCELLEME TAMAMLANDI", "UPDATE FAILED": "GÜNCELLEME BAŞARISIZ OLDU", "UPDATE IN PROGRESS": "GÜNCELLEME DEVAM EDİYOR", "USB Info": "USB Bilgisi", "USB Parameters": "USB Parametreleri", "USB model, used when configuring instance flavor": "", "USER": "KULLANICI", "UStack": "UStack", "UStack Reports": "UStack Raporları", "UStack Services": "UStack Hizmetleri", "UUID": "UUID", "Ubuntu": "Ubuntu", "Uccps": "Uccps", "Uganda": "Uganda", "Ukraine": "<PERSON><PERSON><PERSON>", "Unable to create instance: batch creation is not supported when specifying IP.": "Sanal Makine oluşturulamıyor: IP belirtildiğinde toplu oluşturma desteklenmez.", "Unable to create instance: insufficient quota to create resources.": "Sanal Makine oluşturulamıyor: Kaynakları oluşturmak için yeterli kota yok.", "Unable to create volume: insufficient quota to create resources.": "Disk oluşturulamıyor: Kaynakları oluşturmak için yeterli kota yok.", "Unable to delete router \"{ name }\". External gateway is opened, please clear external gateway first.": "\"{ name }\" adlı yönlendirici silinemedi. Dış ağ geçidi açık, lütfen önce dış ağ geçidini temizleyin.", "Unable to get {name} detail.": "{name} ayrıntısı alınamıyor.", "Unable to get {name}.": "{name} alınamıyor.", "Unable to get {title}, please go back to ": "{title} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lü<PERSON><PERSON> geri <PERSON>n ", "Unable to get {title}, please go to ": "{title} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, l<PERSON><PERSON><PERSON> gidin ", "Unable to paste into the same folder.": "Aynı klasöre yapıştırılamıyor.", "Unable to render form": "Form oluşturulamıyor", "Unable to {action} {name}.": "{action} {name} yapılamıyor.", "Unable to {action}, because : {reason}, instance: {name}.": "{action} ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>: {reason}, <PERSON><PERSON>: {name}.", "Unable to {action}, instance: {name}.": "{action} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>: {name}.", "Unable to {action}.": "{action} yapılamıyor.", "Unable to {title}, please go back to ": "{title} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lü<PERSON><PERSON> geri <PERSON>n ", "Unattached": "<PERSON><PERSON><PERSON>", "Unavailable": "Kullanılamıyor", "Unbootable": "Önyükleme yapılamıyor", "Unbounded": "Sınırsız", "United Arab Emirates": "Birleşik Arap Emirlikleri", "United Kingdom": "Birleşik Krallık", "United States": "Amerika Birleşik Devletleri", "Unknown": "Bilinmeyen", "Unless Stopped": "Durdurulmadığı sürece", "Unless you know clearly which AZ to create the volume in, you don not need to fill in here.": "Diski oluşturmak için hangi AZ'yi açıkça bildiğinizden emin değ<PERSON>z, burayı doldurmanıza gerek yok.", "Unlimit": "Sınırsız", "Unlock": "<PERSON><PERSON><PERSON>", "Unlock Instance": "<PERSON><PERSON> Ma<PERSON>enin <PERSON>", "Unmanage Error": "Yönetimi Kaldırma Hatası", "Unmanage Starting": "Yönetimi Kaldırmaya Başlama", "Unmanaged": "Yönetilmeyen", "Unpause": "Duraklatmayı Kaldır", "Unpause Container": "Konteynırın <PERSON>masını Kaldır", "Unpause Instance": "Sanal Makinenin Duraklatmasını Kaldır", "Unrescuing": "<PERSON><PERSON><PERSON>", "Unrestricted": "Sınırsız", "Unset": "Ayarlanmamış", "Unshelve": "<PERSON>fa kaldırmayı kaldır", "Unshelve Instance": "Sanal Makineyi Rafını Kaldır", "Unshelving": "<PERSON><PERSON>", "Unused": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Up": "Yukarı", "Update": "", "Update Access": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update At": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update Cluster Template": "<PERSON><PERSON><PERSON>ğını Güncelle", "Update Complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update Failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>ı<PERSON>", "Update In Progress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update Record Set": "<PERSON><PERSON><PERSON>ini Güncelle", "Update Segment": "", "Update Status": "<PERSON><PERSON><PERSON>", "Update Template": "Şablon<PERSON>", "Update User Password": "Kullanıcı Parolasını Güncelle", "Update user password": "Kullanıcı parolasını güncelle", "Updated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Updated At": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Updating": "Güncelleniyor", "Updating Password": "<PERSON><PERSON><PERSON>", "Upgrade Cluster": "<PERSON><PERSON><PERSON>", "Upload File": "<PERSON><PERSON><PERSON>", "Upload Type": "<PERSON><PERSON><PERSON><PERSON>", "Upload progress": "<PERSON><PERSON><PERSON><PERSON>", "Uploading": "Yükleniyor", "Uploads with mirrors are interrupted. The name of the mirror file is: {name}. Do you want to continue the image? Please select the mirror file in the button below and click the \"Continue\" button; Tips: If the wrong file is selected, it will not be interrupted. Clicking the \"Delete\" button will delete the task of uploading the image.": "Aynanın yüklenmesi kesintiye uğradı. Görüntü dosyasının adı: {name}. <PERSON><PERSON> görüntüsüne devam etmek istiyor musunuz? Lütfen aşağıdaki düğmeden yansı dosyasını seçin ve \"Devam\" dü<PERSON><PERSON><PERSON> tıklayın; İpuçları: Yanlış dosyayı seçerseniz devam edemezsiniz. \"Sil\" d<PERSON><PERSON><PERSON><PERSON> tıklamak, resmi yükleme görevini silecektir.", "Uruguay": "Uruguay", "Usage": "Kullanım", "Usage Type": "Kullanım Türü", "Usb Controller": "USB Kontrolcüsü", "Use Type": "Kullanım Türü", "Used": "Kullanılan", "Used IPs": "Kullanılan IP'ler", "Used by tunnel(s): {names}. ID(s): {ids}": "Tünel tarafından kullanılıyor: {names}. ID(ler): {ids}", "Used to restrict whether the application credential may be used for the creation or destruction of other application credentials or trusts.": "Uygulama kimlik bilgilerinin diğer uygulama kimlik bilgilerinin veya güvenlerinin oluşturulması veya yok edilmesi için kullanılıp kullanılamayacağını kısıtlamak için kullanılır.", "User": "Kullanıcı", "User Account": "Kullanıcı Hesabı", "User Center": "Kullanıcı Merkezi", "User Data": "Kullanıcı Verileri", "User Detail": "Kullanıcı Detayı", "User Edit": "Kullanıcı Düzenle", "User Group": "Kullanıcı Grubu", "User Group Detail": "Kullanıcı Grubu Detayı", "User Group ID/Name": "Kullanıcı Grubu ID/Adı", "User Group Name": "Kullanıcı Grubu Adı", "User Group Num": "Kullanıcı Grubu Sayısı", "User Group Num: ": "Kullanıcı Grubu Sayısı: ", "User Groups": "Kullanıcı Grupları", "User ID": "Kullanıcı ID", "User ID/Name": "Kullanıcı ID/Adı", "User Name": "Kullanıcı Adı", "User Num": "Kullanıcı Sayısı", "User Num: ": "Kullanıcı Sayısı: ", "User name can not be duplicated": "Kullanıcı adı çoğaltılamaz", "User need to change password": "Kullanıcının parolasını değiştirmesi gerekiyor", "Username": "Kullanıcı Adı", "Username or password is incorrect": "Kullanıcı adı veya parola yanlış", "Users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Using cascading deletion, when the volume has snapshots, the associated snapshot will be automatically deleted first, and then the volume will be deleted, thereby improving the success rate of deleting the volume.": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>, diskte anlık gör<PERSON><PERSON><PERSON><PERSON> varsa, ilişkili anlık görüntü önce otomatik olarak silinir ve ardından disk silinir, b<PERSON><PERSON><PERSON> diskin silinme başarı oranı artar.", "Using server groups, you can create cloud hosts on the same/different physical nodes as much as possible to meet the affinity/non-affinity requirements of business applications.": "Sunucu grupları kull<PERSON>rak, iş uygulamalarının afinite/anti-afinite gereksinimlerini karşılamak için mümkün olduğunca aynı/farklı fiziksel düğümlerde bulut sunucuları oluşturabilirsiniz.", "Uzbekistan": "Özbekistan", "VCPU (Core)": "VCPU (Çekirdek)", "VCPUs": "VCPUs", "VDI - VirtualBox compatible image format": "VDI - VirtualBox uyumlu imaj biçimi", "VGPU": "VGPU", "VGPU (Core)": "VGPU (Çekirdek)", "VHD - VirtualPC compatible image format": "VHD - VirtualPC uyumlu imaj <PERSON>i", "VIF Details": "VIF Ayrıntıları", "VIF Type": "VIF Türü", "VIR Domain Event": "", "VMDK - Hyper-V compatible image format": "VMDK - Hyper-V uyumlu imaj <PERSON>imi", "VNC": "VNC", "VNIC Type": "VNIC Türü", "VPN": "VPN", "VPN EndPoint Groups": "VPN Uç Nokta Grupları", "VPN Gateways": "VPN Ağ Geçitleri", "VPN Service": "VPN Hizmeti", "VPN Service ID": "VPN Hizmeti Kimliği", "VPNs": "VPN'ler", "VRRP": "VRRP", "Valid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Value": "<PERSON><PERSON><PERSON>", "Values": "<PERSON><PERSON><PERSON><PERSON>", "Vanuatu": "Vanuatu", "Vatican City State (Holy See)": "Vatikan Şehir <PERSON>i (Kutsal Makam)", "Vendor Interface": "Satıcı Arayüzü", "Venezuela": "Venezuela", "Verifying": "Doğrulanıyor", "Version": "S<PERSON>r<PERSON><PERSON>", "Vietnam": "Vietnam", "View": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "View Detail": "Detayları Görüntüle", "View Full Log": "<PERSON><PERSON><PERSON>", "View Rules": "Kuralları Görüntüle", "View virtual adapters": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "Virgin Islands (U.S.)": "Virgin Adaları (ABD)", "Virtual Adapter": "<PERSON><PERSON>", "Virtual Adapter ID": "<PERSON><PERSON>", "Virtual LAN": "Sanal Ağ", "Virtual LANs": "<PERSON><PERSON>", "Virtual Resource Overview": "Sanal Kaynak Genel Bakış", "Virtual Resources Used": "Kullanılan Sanal Kaynaklar", "Virtual adapter mainly used for binding instance and other operations, occupying the quota of the port.": "<PERSON><PERSON>, sanal makinenin bağlanması ve diğer işlemleri için kullanılır, a<PERSON> <PERSON><PERSON>r<PERSON>n<PERSON>n kotasını alır.", "Visibility": "Görünürlük", "Visualization Compute Optimized Type with GPU": "GPU ile Görselleştirme Optimize Edilmiş Hesaplama Türü", "Volume": "Disk", "Volume Backup": "Disk Yedekleme", "Volume Backup Capacity (GiB)": "Disk Yedekleme Ka<PERSON>itesi (GiB)", "Volume Backup Detail": "Disk Yedekleme Ayrıntısı", "Volume Backup Name": "Disk Yedekleme Adı", "Volume Backups": "Disk Yedeklemeleri", "Volume Capacity (GiB)": "Disk Kapasitesi (GiB)", "Volume Detail": "Disk Ayrıntısı", "Volume Driver": "Disk Sürücü", "Volume ID": "Disk Kimliği", "Volume ID/Name": "Disk Kimliği/Adı", "Volume Info": "Disk Bilgisi", "Volume Name": "Disk Adı", "Volume Size": "Disk Boyutu", "Volume Snapshot": "Disk Anlık Görüntüsü", "Volume Snapshot Detail": "Disk Anlık Görüntüsü Ayrıntısı", "Volume Snapshot Name": "Disk Anlık Görüntüsü Adı", "Volume Snapshots": "Disk Anlık Görüntüleri", "Volume Source": "Disk Kaynağı", "Volume Transfer": "Disk Transferi", "Volume Type": "Disk Türü", "Volume Type Detail": "Disk Türü Ayrıntısı", "Volume Types": "Disk Türleri", "Volumes": "<PERSON><PERSON><PERSON>", "Wallis And Futuna Islands": "Wallis ve <PERSON><PERSON>", "Warn": "Uyarı", "Warning": "Uyarı", "Weight": "Ağırlık", "Weights": "Ağırlıklar", "Welcome": "<PERSON><PERSON> geldiniz", "Western Sahara": "Batı Sahra", "When auto-expand/close is enabled, if there is no operation in the pop-up window, the pop-up window will be closed automatically after { seconds } seconds, and it will be automatically expanded when the displayed content changes.": "Otomatik genişletme/kapatma et<PERSON>leştirildiğinde, a<PERSON><PERSON><PERSON><PERSON><PERSON> pencerede işlem yapılmazsa, a<PERSON><PERSON><PERSON><PERSON><PERSON> pencere { seconds } saniye sonra otomatik olarak kapanacak ve görüntülenen içerik değiştiğinde otomatik olarak genişleyecektir.", "When the computing service starts the recycling instance interval, the instance will be stored in the recycling bin after deletion, and will be retained according to the corresponding time interval. You can choose to restore it within this period. After successful recovery, the status of the instance is running and related resources remain unchanged.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, geri dö<PERSON><PERSON><PERSON><PERSON><PERSON> aralığında sanal makinelemeyi başlattığında, sanal makine silindikten sonra geri dönüşüm kutusuna saklanır ve karşılık gelen zaman aralığına göre saklanır. Bu süre içinde geri yüklemeyi seçebilirsiniz. Başarılı bir geri yükleme işleminden sonra, sanal makinenin durumu çalışır durumunda olacak ve ilgili kaynaklar değişmeyecektir.", "When the volume is \"bootable\" and the status is \"available\", it can be used as a startup source to create an instance.": "Disk \"başlangıç yapılabilir\" durumdaysa ve durumu \"kullanılabilir\" durumdaysa, bir sanal makine oluşturmak için bir başlangıç kaynağı olarak kullanılabilir.", "When you do online backup of the volume that has been bound, you need to pay attention to the following points:": "Bağlanmış diski çevrimiçi yedeklediğinizde, aşağıdaki noktalara dikkat etmeniz gerekmektedir:", "When you restore a backup, you need to meet one of the following conditions:": "Yedekleme geri yüklemesi yapmak için aşağıdaki koşullardan birini karşılamalısınız:", "When your Yaml file is a fixed template, variable variables can be stored in an environment variable file to implement template deployment. The parameters in the environment variable file need to match the parameters defined in the template file.": "Yaml dosyanız sabit bir taslaksa, değişken değişkenler taslak dağıtımını gerçekleştirmek için bir çevre değişken dosyasında saklanabilir. Çevre değişken dosyasındaki parametreler, taslakta tanımlanan parametrelerle eşleşmelidir.", "Whether enable or not using the floating IP of cloud provider.": "Bulut sağlayıcının kayan IP'sini kullanıp kullanmayacağınız.", "Whether the Login Name can be used is up to the feasible configuration of cloud-init or cloudbase-init service in the image.": "<PERSON><PERSON><PERSON>ın kullanılıp kullanılamayacağı, görüntüdeki cloud-init veya cloudbase-init hizmetinin uygun yapılandırmasına bağlıdır.", "Whether the boot device should be set only for the next reboot, or persistently.": "Önyükleme cihazının yalnızca bir sonraki yeniden başlatma için mi yoksa kalıcı olarak mı ayarlanması gerektiği.", "Which Network Interface provider to use when plumbing the network connections for this Node": "Bu Düğüm için ağ bağlantılarını bağlarken hangi Ağ Arayüzü sağlayıcısının kullanılacağı", "Windows": "Windows", "Workdir": "Çalışma Dizini", "Working Directory": "Çalışma Dizini", "X86 Architecture": "X86 Mimarisi", "YAML File": "YAML Dosyası", "Yemen": "Yemen", "Yes": "<PERSON><PERSON>", "Yes - Create a new system disk": "", "You are not allowed to delete policy \"{ name }\" used by firewalls: { firewalls }.": "", "You are not allowed to delete policy \"{ name }\".": "", "You are not allowed to delete router \"{ name }\".": "\"{ name }\" adlı yönlendiriciyi silme izniniz yok.", "You are not allowed to delete rule \"{ name }\" in use.": "", "You are not allowed to delete rule \"{ name }\".": "", "You are not allowed to delete snapshot \"{ name }\", which is used by creating volume \"{volumes}\".": "\"{volumes}\" adlı Diski oluştururken kullanılan \"{ name }\" adlı anlık görüntüyü silme izniniz yok.", "You are not allowed to delete snapshot \"{ name }\".": "\"{ name }\" adlı anlık görüntüyü silme izniniz yok.", "You are not allowed to jump to the console.": "Konsola geçiş yapma izniniz yok.", "You are not allowed to { action } \"{ name }\".": "\"{ name }\" adlı { action } etme izniniz yok.", "You are not allowed to { action } {name}.": "{name} adlı  { action } etme izniniz yok.", "You are not allowed to {action}, instance: {name}.": "{action} i<PERSON><PERSON><PERSON>, <PERSON><PERSON>: {name}.", "You are not allowed to {action}.": "{action} i<PERSON><PERSON>i yapma izniniz yok.", "You can manually specify a physical node to create an instance.": "Bir sanal makine oluşturmak için fiziksel bir düğümü manuel olarak belirleyebilirsiniz.", "You don't have access to get {name}.": "{name} öğesini almak için erişim izniniz yok.", "You may update the editable properties of the RBAC policy here.": "", "Yugoslavia": "Yugoslavya", "Zambia": "Zambiya", "Zimbabwe": "Zimbabve", "Zone": "", "Zone ID": "<PERSON><PERSON><PERSON>si", "Zone ID/Name": "", "Zone Name": "<PERSON><PERSON><PERSON>", "Zones Detail": "Bölgeler Ayrıntısı", "abandon stack": "yığını terk et", "add access rule": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> e<PERSON>", "add network": "a<PERSON>", "add router": "yönlendirici ekle", "all": "<PERSON><PERSON><PERSON><PERSON>", "an optional string field to be used to store any vendor-specific information": "herhangi bir satıcıya özgü bilgiyi depolamak için kullanılacak isteğe bağlı bir dize alanı", "application credential": "<PERSON>y<PERSON><PERSON>a <PERSON>", "associate floating ip": "sabit IP ilişkilendir", "attach interface": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "authorized by group ": "grup tarafından yetkilendirildi ", "auto": "", "auto_priority": "", "availability zones": "mev<PERSON> b<PERSON><PERSON><PERSON>", "available": "mevcut", "bare metal node": "bare metal düğümü", "bare metal nodes": "bare metal düğümleri", "be copied": "kopyalanacak", "be cut": "kesilecek", "be deleted": "<PERSON><PERSON><PERSON><PERSON>", "be rebooted": "yeniden başlatılacak", "be recovered": "kurtarılmış olacak", "be released": "serbest bırakılacak", "be soft rebooted": "yumuşak yeniden başlatılacak", "be started": "başlatılacak", "be stopped": "<PERSON><PERSON><PERSON>", "capsules": "<PERSON><PERSON><PERSON><PERSON>", "certificate": "sertif<PERSON>", "cidr": "CIDR", "cinder services": "cinder hizmetleri", "clusters": "<PERSON><PERSON><PERSON>", "clustertemplates": "k<PERSON><PERSON> taslakları", "compute hosts": "hesap<PERSON>a ana bil<PERSON>ı", "compute services": "<PERSON><PERSON><PERSON><PERSON><PERSON>i", "configurations": "yapılandırmalar", "confirm resize or migrate": "yeniden boyutlandırmayı veya taşımayı onaylayın", "connect subnet": "alt ağı bağla", "container objects": "<PERSON><PERSON>", "containers": "k<PERSON>ynerler", "create DSCP marking rule": "DSCP işaretleme kuralı oluştur", "create a new network/subnet": "yeni bir ağ/alt ağı oluştur", "create a new security group": "yeni bir güvenlik grubu oluştur", "create allowed address pair": "izin verilen adres çifti oluştur", "create bandwidth limit rule": "bant genişliği sınırlama kuralı oluştur", "create baremetal node": "bare metal düğümü oluştur", "create default pool": "varsayılan havuz oluştur", "create encryption": "şifreleme o<PERSON>ş<PERSON>", "create firewall policy": "", "create flavor": "Şablon oluştur", "create instance snapshot": "Sanal Makine anlık görüntüsü oluştur", "create ipsec site connection": "IPSec site bağlantısı oluştur", "create network": "ağ oluştur", "create router": "yönlendirici oluştur", "create share": "paylaşım oluştur", "create share group": "paylaşım grubu oluştur", "create share group type": "paylaşım grubu türü oluştur", "create share network": "paylaşım ağı oluştur", "create share type": "paylaşım türü oluştur", "create stack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create volume": "Disk oluştur", "create volume snapshot": "Disk anlık görüntüsü oluştur", "create volume type": "Disk türü oluştur", "create vpn": "vpn oluştur", "create vpn endpoint group": "vpn uç nokta grubu oluştur", "create vpn ike policy": "vpn ike ilkesi oluştur", "create vpn ipsec policy": "vpn ipsec ilkesi oluştur", "data": "veri", "database backups": "veritabanı yedeklemeleri", "database instances": "veritabanı Sanal Makineleri", "delete": "sil", "delete allowed address pair": "izin verilen adres <PERSON>ini sil", "delete application credential": "<PERSON>y<PERSON><PERSON>a k<PERSON> sil", "delete bandwidth egress rules": "bant genişliği çıkış kurallarını sil", "delete bandwidth ingress rules": "bant genişliği giriş kurallarını sil", "delete certificate": "sertifikayı sil", "delete container": "konteyneri sil", "delete default pool": "var<PERSON><PERSON>lan havuzu sil", "delete domain": "alan adın<PERSON> sil", "delete dscp marking rules": "DSCP işaretleme kurallarını sil", "delete firewall": "", "delete flavor": "Şabloni sil", "delete group": "grubu sil", "delete host": "", "delete image": "imajı sil", "delete instance": "sanal makineyi sil", "delete instance snapshot": "sanal makine anlık görüntüsünü sil", "delete ipsec site connection": "IPSec site bağlantısını sil", "delete ironic instance": "ironik sanal makinesini sil", "delete keypair": "an<PERSON><PERSON> sil", "delete listener": "dinleyiciyi sil", "delete load balancer": "<PERSON><PERSON><PERSON> sil", "delete member": "<PERSON><PERSON>yi sil", "delete network": "ağı sil", "delete policy": "", "delete port forwarding": "ağ adaptörü yönlendirmeyi sil", "delete project": "projei sil", "delete qos policy": "QoS ilkesini sil", "delete role": "r<PERSON><PERSON> sil", "delete router": "yönlendiriciyi sil", "delete rule": "", "delete segments": "", "delete stack": "yığıyı sil", "delete static route": "statik rotayı sil", "delete subnet": "alt ağı sil", "delete user": "kullanıcıyı sil", "delete virtual adapter": "sanal adapt<PERSON><PERSON><PERSON> sil", "delete volume": "Diski sil", "delete volume backup": "Disk yedeklemesini sil", "delete volume snapshot": "Disk anlık görüntüsünü sil", "delete vpn": "vpn'i sil", "delete vpn IKE policy": "vpn IKE ilkesini sil", "delete vpn IPsec policy": "vpn IPsec ilkesini sil", "delete vpn endpoint groups": "vpn uç nokta gruplarını sil", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detach instance": "sanal makineyi ayır", "detach security group": "güvenlik grubunu ayır", "disable cinder service": "cinder hizmetini devre dışı bırak", "disable compute service": "hesa<PERSON><PERSON>a hizmetini devre dışı bırak", "disable neutron agent": "neutron ajanını devre dışı bırak", "disassociate floating ip": "açık IP ilişkisini kes", "disconnect subnet": "alt ağı kes", "dns zones": "dns bölgeleri", "domain": "<PERSON>an adı", "domains": "<PERSON>an <PERSON>", "download image": "<PERSON><PERSON><PERSON>", "e.g. 2001:Db8::/48": "örn. 2001:Db8::/48", "edit baremetal node": "bare metal düğümünü düzenle", "edit default pool": "<PERSON><PERSON><PERSON><PERSON> havu<PERSON> dü<PERSON>le", "edit health monitor": "sağlık izleyiciyi düzenle", "edit image": "<PERSON><PERSON><PERSON>ü<PERSON>", "edit instance snapshot": "Sanal Makine anlık görüntüsünü dü<PERSON>", "edit member": "<PERSON><PERSON>yi düzenle", "edit system permission": "sistem iznini düzenle", "egress": "çıkış", "enable cinder service": "cinder hizmetini etkin<PERSON>ştir", "enable compute service": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enable neutron agent": "neutron ajanını etkinleştir", "external port": "<PERSON><PERSON><PERSON>", "external ports": "<PERSON><PERSON><PERSON>", "extra specs": "ek ö<PERSON>", "firewall": "", "firewall policies": "", "firewall rule": "", "firewall rules": "", "firewalls": "", "flavor": "Şablon", "floating ip": "değişken ip", "floating ips": "değişken i<PERSON>'ler", "heat services": "heat hizmetleri", "host aggregates": "ana bi<PERSON><PERSON><PERSON><PERSON>", "hosts": "ana bi<PERSON><PERSON><PERSON><PERSON>", "hypervisor": "hipervizör", "image": "<PERSON>aj", "images": "<PERSON><PERSON><PERSON>", "in": "<PERSON><PERSON><PERSON>", "ingress": "<PERSON><PERSON><PERSON>", "insert": "ekle", "insert rule": "", "instance": "<PERSON><PERSON>", "instance snapshot": "Sanal Makine anlık görüntüsü", "instance snapshots": "Sanal Makine anlık görüntüleri", "instance: {name}.": "<PERSON><PERSON>: {name}.", "instances": "<PERSON><PERSON>", "internal port": "<PERSON><PERSON><PERSON> a<PERSON>", "internal ports": "<PERSON><PERSON><PERSON> a<PERSON>", "ipsec site connection": "ipsec site bağlantısı", "jump to the console": "konsola git", "keypair": "<PERSON><PERSON><PERSON>", "keypairs": "<PERSON><PERSON><PERSON>", "labels": "<PERSON><PERSON><PERSON>", "list page": "liste sayfası", "listener": "dinley<PERSON>", "listeners": "<PERSON><PERSON><PERSON><PERSON>", "live migrate": "canlı taşı", "load balancer": "<PERSON><PERSON><PERSON>", "lock instance": "sanal makineyi kilitle", "manage ports": "", "manage qos spec": "QOS özelliğini yönet", "manage resource types": "kaynak tü<PERSON> y<PERSON>", "message": "<PERSON>saj", "message.reason": "mesaj.neden", "metadata": "meta veri", "migrate": "taşı", "modify instance tags": "<PERSON><PERSON> Ma<PERSON>e etike<PERSON>ini <PERSON>", "modify project tags": "proje etiketlerini değiştir", "network": "ağ", "networks": "<PERSON><PERSON><PERSON>", "neutron agent": "neutron ajanı", "neutron agents": "neutron ajanları", "ns1.example.com admin.example.com 2013022001 86400 7200 604800 300 <ul><li>The primary name server for the domain, which is ns1.example.com or the first name server in the vanity name server list.</li><li>The responsible party for the domain: admin.example.com.</li><li>A timestamp that changes whenever you update your domain.</li><li>The number of seconds before the zone should be refreshed.</li><li>The number of seconds before a failed refresh should be retried.</li><li>The upper limit in seconds before a zone is considered no longer authoritative.</li><li>The negative result TTL (for example, how long a resolver should consider a negative result for a subdomain to be valid before retrying).</li></ul>": "", "open external gateway": "dış geçidi aç", "out": "çıkış", "paste files to folder": "dosyaları klasöre yapıştır", "pause instance": "sanal makineyi du<PERSON>", "phone": "telefon", "please select network": "lütfen ağı seçin", "please select subnet": "lütfen alt ağı seçin", "policy": "", "port": "<PERSON><PERSON>", "port forwarding": "ağ adaptörü yönlendirme", "port forwardings": "<PERSON><PERSON> adaptö<PERSON>ü yö<PERSON>dirmeleri", "port groups": "ağ adaptörü grupları", "ports": "<PERSON><PERSON>", "project": "proje", "projects": "projeler", "qemu_guest_agent enabled": "qemu_guest_agent etkin", "qoS policy": "QoS ilkesi", "qos specs": "QoS özellikleri", "quota set to -1 means there is no quota limit on the current resource": "kota -1 olarak ayarlandığında, mevcut kaynakta kota sınırı yok demektir", "read": "<PERSON>uma", "reboot instance": "sanal makineyi yeniden ba<PERSON>lat", "rebuild instance": "sanal makineyi yeniden o<PERSON>", "receive": "al", "recordsets": "ka<PERSON>ıt setleri", "recover instance": "sanal makineyi kurtar", "recycle bins": "geri <PERSON><PERSON><PERSON><PERSON> kut<PERSON>ı", "release fixed ip": "sabit ip'yi serbest bırak", "remove network": "ağı kaldır", "remove router": "yönlendiriciyi kaldır", "remove rule": "", "reserved_host": "", "resize": "<PERSON><PERSON><PERSON>", "resume instance": "sanal makineyi devam ettir", "revert resize or migrate": "yeniden boyutlandırmayı veya taşımayı geri al", "rh_priority": "", "role": "rol", "roles": "roller", "router": "yönlendirici", "routers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "security group": "güvenlik grubu", "security group rules": "güvenlik grubu kuralları", "security groups": "güvenlik grupları", "segments": "", "select an existing port": "mevcut bir ağ adaptö<PERSON>ü se<PERSON>", "server group": "<PERSON><PERSON><PERSON> grubu", "server groups": "<PERSON><PERSON><PERSON> grup<PERSON>ı", "services": "<PERSON><PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "share": "paylaş", "share access rules": "<PERSON><PERSON><PERSON><PERSON><PERSON> er<PERSON><PERSON><PERSON>", "share group": "paylaşım grubu", "share group type": "paylaşım grubu türü", "share groups": "paylaşım grupları", "share instance": "<PERSON><PERSON><PERSON><PERSON>m sanal makine", "share instances": "<PERSON><PERSON><PERSON><PERSON>m sanal makineleri", "share metadata": "paylaşım meta verisi", "share network": "paylaşım ağı", "share server": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "share servers": "pay<PERSON><PERSON><PERSON>m sunucuları", "share type": "paylaşım türü", "share types": "<PERSON><PERSON><PERSON><PERSON><PERSON> tü<PERSON>", "shelve instance": "sanal makineyi rafa kaldır", "smtp.example.com": "smtp.example.com", "soft reboot instance": "yum<PERSON><PERSON><PERSON> yeniden başlatma sanal makine", "stack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stack events": "<PERSON>ı<PERSON><PERSON><PERSON>", "stack resources": "<PERSON>ı<PERSON><PERSON><PERSON> kaynakları", "stacks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start instance": "sanal makin<PERSON>i ba<PERSON>", "static routers": "statik yönlendiriciler", "stop instance": "sanal makineyi durdur", "storage backend": "depolama arkayüzü", "subnet": "alt ağ", "subnets": "alt ağlar", "suspend instance": "sanal makineyi askıya al", "the Republic of Abkhazia": "<PERSON><PERSON><PERSON><PERSON>", "the folder is not empty": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "the policy is in use": "ilke kullanılıyor", "the router has connected subnet": "yönlendirici bağlı alt ağa sahip", "the vpn gateway is in use": "vpn ağ geçidi kullanılıyor", "time / 24h": "zaman / 24s", "to delete": "<PERSON><PERSON><PERSON>", "transmit": "iletmek", "unlock instance": "sanal makinenin kilidini aç", "unpause instance": "sanal makineyi devam ettir", "unshelve instance": "sanal makineyi rafa kaldırma", "update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update status": "<PERSON><PERSON><PERSON>", "update template": "taslağı güncelle", "used": "kullanılan", "user": "kullanıcı", "user group": "kullanıcı grubu", "user groups": "kullanıcı grupları", "users": "kullanı<PERSON>ılar", "vCPUs": "vCPU'lar", "vCPUs and ram are not used for bare metal scheduling": "vCPUs ve RAM bare metal zamanlaması için kullanılmaz", "volume": "disk", "volume backup": "disk yedekleme", "volume backups": "disk ye<PERSON><PERSON><PERSON><PERSON>", "volume capacity": "disk kapasitesi", "volume snapshot": "disk an<PERSON><PERSON>k görüntüsü", "volume snapshots": "disk an<PERSON>ık görüntüleri", "volume type": "disk türü", "volume type qos": "disk türü qos", "volume type {type}": "disk türü {type}", "volume type {type} capacity": "disk türü {type} kapasitesi", "volume types": "disk türleri", "volumes": "diskler", "vpn IKE policy": "vpn IKE ilkesi", "vpn IPsec policy": "vpn IPsec ilkesi", "vpn endpoint groups": "vpn uç nokta grupları", "vpn services": "vpn hizmetleri", "write": "yaz", "{ name } Format Error (e.g. *********** or ***********/24)": "", "{ name } Format Error (e.g. FE80:0:0:0:0:0:0:1 or FE80:0:0:0:0:0:0:1/10)": "", "{ size } GiB": "{ size } GiB", "{ size } KiB": "{ size } KiB", "{ size } MiB": "{ size } MiB", "{ size } TiB": "{ size } TiB", "{ size } bytes": "{ size } bayt", "{action} successfully, instance: {name}.": "{action} ba<PERSON><PERSON><PERSON><PERSON><PERSON>, sanal makine: {name}.", "{action} successfully.": "{action} başarılı.", "{action} {name} successfully.": "{action} {name} başarılı.", "{hours} hours {leftMinutes} minutes {leftSeconds} seconds": "{hours} saat {leftMinutes} dakika {leftSeconds} saniye", "{interval, plural, =1 {one day} other {# days} } later delete": "{interval, plural, =1 {one day} other {# days} } sonra sil", "{interval, plural, =1 {one hour} other {# hours} } later delete": "{interval, plural, =1 {one hour} other {# hours} } sonra sil", "{interval, plural, =1 {one minute} other {# minutes} } later delete": "{{interval, plural, =1 {one minute} other {# minutes} } sonra sil", "{interval, plural, =1 {one week} other {# weeks} } later delete": "{interval, plural, =1 {one week} other {# weeks} } sonra sil", "{minutes} minutes {leftSeconds} seconds": "{minutes} minutes {leftSeconds} saniye", "{name} type": "{name} türü", "{name} type capacity": "{name} tür<PERSON> kapasite", "{name} type capacity (GiB)": "{name} tü<PERSON><PERSON> ka<PERSON>ite (GiB)", "{name} type snapshots": "{name} t<PERSON><PERSON><PERSON>örü<PERSON>üleri", "{name} {id} could not be found.": "{name} {id} bulunamadı.", "{number} {resource}": "{number} {resource}", "{pageSize} items/page": "{pageSize} öğe/sayfa", "{seconds} seconds": "{seconds} saniye"}