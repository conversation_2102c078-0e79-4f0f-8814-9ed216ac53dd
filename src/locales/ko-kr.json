{"3600": "3600", " You can go to the console to ": "콘솔로 이동", "\"Shared\" volume can be mounted on multiple instances": "\"공유된\" 볼륨은 다수의 인스턴스에서 마운트가 가능합니다.", "\"v=spf1 ipv4=********* include:examplesender.email +all\" <ul><li><b>v=spf1:</b> Tells the server that this contains an SPF record. Every SPF record must begin with this string.</li> <li><b>Guest List:</b> Then comes the “guest list” portion of the SPF record or the list of authorized IP addresses. In this example, the SPF record is telling the server that ipv4=********* is authorized to send emails on behalf of the domain.</li> <li><b>include:examplesender.net:</b> is an example of the include tag, which tells the server what third-party organizations are authorized to send emails on behalf of the domain. This tag signals that the content of the SPF record for the included domain (examplesender.net) should be checked and the IP addresses it contains should also be considered authorized. Multiple domains can be included within an SPF record but this tag will only work for valid domains.</li><li><b>-all:</b> Tells, the server that addresses not listed in the SPF record are not authorized to send emails and should be rejected.</li></ul>": "", "'ip' rule represents IPv4 or IPv6 address, 'cert' rule represents TLS certificate, 'user' rule represents username or usergroup, 'cephx' rule represents ceph auth ID.": "‘ip’  룰은 IPv4 또는 IPv6 주소를 나타내며 ’cert’ 룰은 TLS 인증서를 ‘user’ 룰은 사용자 이름과 사용자 그룹을 ’cephx’ 룰은 ceph auth ID 를 나타냅니다.", "-1 means no connection limit": "-1은 연결 제한이 없는 것을 의미합니다.", ".": ".", "0 iodef mailto:<EMAIL> <ul><li><b>0:</b> is flag. An unsigned integer between 0-255.</li> <li><b>iodef:</b> An ASCII string that represents the identifier of the property represented by the record.<br />Available Tags: \"issue\", \"issuewild\", \"iodef\"</li><li><b>mailto:<EMAIL>:</b> The value associated with the tag.</li></ul>": "", "1. The backup can only capture the data that has been written to the volume at the beginning of the backup task, excluding the data in the cache at that time.": "백업 테스트 시작시 볼륨에 작성된 데이터 수집만 백업 가능하며 캐쉬에 저장된 데이터는 제외됩니다.", "1. The name of the custom resource class property should start with CUSTOM_, can only contain uppercase letters A ~ Z, numbers 0 ~ 9 or underscores, and the length should not exceed 255 characters (for example: CUSTOM_BAREMETAL_SMALL).": "사용자 리소스 클래스 속성의 이름은 CUSTOM_으로 시작되며, A ~ Z 대문자만 포함, 0 ~ 9 숫자 또는 언더스코어, 길이는 255자를 초과하지 않아야 합니다. (예: CUSTOM_BAREMETAL_SMALL).", "1. The name of the trait should start with CUSTOM_, can only contain uppercase letters A ~ Z, numbers 0 ~ 9 or underscores, and the length should not exceed 255 characters (for example: CUSTOM_TRAIT1).": "트레인 이름은 CUSTOM으로 시작되며 A ~ Z 대문자만 포함, 0 ~ 9 숫자 또는 언더스코어, 길이는 255자를 초과하지 않아야 합니다.(예: CUSTOM_TRAIT1)", "1. The volume associated with the backup is available.": "백업과 결합된 볼륨이 사용가능합니다.", "1. You can create {resources} using ports or port ranges.": "포트 또는 포트 범위로 {resource}를 생성 가능합니다.", "10 0 5060 server1.example.com. <ul><li>\"10\" is the priority of the record. The lower the value, the higher the priority.</li><li>0 is the weight of the record. This is the weight of which this record has a chance to be used when there are multiple matching SRV records of the same priority.</li><li>5060 is the port of the record. This specifies the port on which the application or service is running.</li> <li>server1.example.com is the target of the record. This specifies the domain of the application or service the record is for. SRV records must specify a target which is either an A record or AAAA record, and may not use CNAME records.</li></ul>": "", "10 mail.example.com <ul><li><b>10:</b> Priority</li> <li><b>mail.example.com:</b> Value</li></ul>": "", "10s": "10s", "1D": "1D", "1H": "1H", "1min": "1min", "2. In the same protocol, you cannot create multiple {resources} for the same source port or source port range.": "동일한 프로토콜내에서 포트 또는 포트 범위로 다수의 {resource}를 생성 가능합니다.", "2. The trait of the scheduled node needs to correspond to the trait of the flavor used by the ironic instance; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all the necessary traits (for example, the ironic instance which use the flavor that has CUSTOM_TRAIT1 as a necessary trait, can be scheduled to the node which has the trait of CUSTOM_TRAIT1).": "예약된 노드의 trait는 ironic 인스턴스에 사용하는 플레이버의 trait와 일치해야 합니다. 필요한 trait를 베어메탈 노드에 주입함으로써 컴퓨팅 서비스는 필요로 하는 모든 trait로 가진 상태로 컴퓨팅 서비스는 예약됩니다(예를 들면 필요한 trait를 가진 CUSTOM_TRAIT1 flavor를 사용하는 ironic 인스턴스는 CUSTOM_TRAIT1 trait를 가진 노드에 예약될 수 있습니다. ", "2. The volume associated with the backup has been mounted, and the instance is shut down.": "백업과 결합된 볼륨이 마운트 되었으며 인스턴스는 종료합니다.", "2. To ensure the integrity of the data, it is recommended that you suspend the write operation of all files when creating a backup.": "데이터 무결성을 검사하기 위해 백업 생성시 모든 파일에 대한 쓰기 동작을 중단하는 것을 권장합니다.", "2. You can customize the resource class name of the flavor, but it needs to correspond to the resource class of the scheduled node (for example, the resource class name of the scheduling node is baremetal.with-GPU, and the custom resource class name of the flavor is CUSTOM_BAREMETAL_WITH_GPU=1).": "플레이버의 리소스 클래스 이름에 대한 사용자화가 가능하지만 예약된 노드의 리소스 클레스에 대한 응답(?)이 필요합니다. (예를 들면 스케쥴링 노드의 리소스 클래스 이름은 baremetal.with-GPU이며 플레이버의 커스텀 리소스 클래스 이름은 CUSTOM_BAREMETAL_WITH_GPU=1)", "3. When using a port range to create a port mapping, the size of the external port range is required to be the same as the size of the internal port range. For example, the external port range is 80:90 and the internal port range is 8080:8090.": "포트 범위를 사용하여 포트 매핑을 생성할 때 외부 포트 범위의 크기는 내부 포트 범위의 크기와 동일해야 합니다. 예를 들어 외부 포트 범위가 80:90이면 내부 포트 범위는 8080:8090입니다.", "4 2 123456789abcdef67890123456789abcdef67890123456789abcdef123456789 <ul> <li><b>4 is Algorithm:</b> Algorithm (0: reserved; 1: RSA; 2: DSA, 3: ECDSA; 4: Ed25519; 6:Ed448)</li> <li><b>2 is Type:</b> Algorithm used to hash the public key (0: reserved; 1: SHA-1; 2: SHA-256)</li> <li><b>Last parameter is Fingerprint:</b> Hexadecimal representation of the hash result, as text</li> </ul>": "", "4. When you use a port range to create {resources}, multiple {resources} will be created in batches. ": " {resources} 생성을 위해 포트 범위를 사용하면 배치로 다수의 {resources}가 생성됩니다.", "5min": "5분", "8 to 16 characters, at least one uppercase letter, one lowercase letter, one number.": "8 ~ 16 문자, 최소 대문자 한개, 소문자 한개, 숫자 한개, 특수 문자 한개", "8 to 32 characters, at least one uppercase letter, one lowercase letter, one number and one special character.": "8 ~ 32 문자, 최소 대문자 한개, 소문자 한개, 숫자 한개, 특수 문자 한개", "<username> or <username>@<domain>": "<사용자 이름> 또는 <사용자 이름>@<도메인>", "A command that will be sent to the container": "컨테이너로 전송될 명령어", "A container with the same name already exists": "동일한 이름의 컨테이너가 존재합니다.", "A dynamic scheduling algorithm that estimates the server load based on the number of currently active connections. The system allocates new connection requests to the server with the least number of current connections. Commonly used for long connection services, such as database connections and other services.": "동적 스케쥴링 알고리즘은 현재 연결된 커넥션 수로 서버 로드를 예측합니다. 시스템은 최소 연결을 가진 서버로 새로운 연결을 할당합니다. 데이터베이스 연결 및 다른 서비스 처럼 오래 지속되는 연결을 갖는 서비스에 활용됩니다.", "A host aggregate can be associated with at most one AZ. Once the association is established, the AZ cannot be disassociated.": "호스트 집합은 한개의 AZ와 결합될 수 있습니다. 결합되고 나면 AZ는 분리될 수 없습니다.", "A public container will allow anyone to use the objects in your container through a public URL.": "퍼블릭 컨테이너는 퍼블릭 URL을 통해 컨테이너 내부의 객체를 사용할 수 있습니다.", "A rule specified before insertion or after insertion a rule. If both are not specified, the new rule is inserted as the first rule of the policy.": "", "A snapshot is an image which preserves the disk state of a running instance, which can be used to start a new instance.": "스냅샷은 실행중인 인스턴의 디스크 상태를 보존하거나 새로운 인스턴스를 시작할 수 있는 이미지입니다.", "A template is a YAML file that contains configuration information, please enter the correct format.": "템플릿은 설정 정보를 포함한 YAML 파일입니다. 올바른 형식으로 입력해주세요.", "A template is a YAML file that contains configuration information.": "템플릿은 설정 정보를 포한한 YAML 파일입니다.", "ADMINISTRATOR": "ADMINISTRATOR", "ADOPT COMPLETE": "적용 완료", "AH": "AH", "AKI - Amazon kernel image format": "AKI - 아마존 커널 이미지 형식", "ALLOW": "", "AMI - Amazon server image format": "AMI - 아마존 서버 이미지 형식", "ANY": "ANY", "API Address": "API 주소", "ARI - Amazon ramdisk image format": "ARI - Amazon 램디스크 이미지 형식", "ARM Architecture": "ARM 아키텍처", "Abandon Stack": "스택", "Abandoning this stack will preserve the resources deployed by the stack.": "스택을  중지하면 스택에 의해 배포된 리소들은 보존됩니다.", "Abort Upload": "업로드 중지", "Accept Volume Transfer": "볼륨 이전 받기", "Access Control": "액세스 제어", "Access Key": "액세스 키", "Access Level": "액세스 수준", "Access Rules": "액세스 규칙", "Access Rules Status": "접근 규칙 상태", "Access To": "접근", "Access Type": "접근 유형", "Access Type Setting": "접근 유형 설정", "Action": "조작", "Action Logs": "작업 로그", "Active": "사용 중인", "Active Status": "활성 상태", "Add": "추가", "Add Access Rule": "접근 규칙 추가", "Add Custom Metadata": "사용자 메타데이터 추가", "Add Data Disks": "데이터 디스크 추가", "Add Environment Variable": "환경 변수 추가", "Add Exposed Ports": "노출 포트 추가", "Add External Members": "외부 멤버 추가", "Add Extra Info": "추가 사양 정보", "Add Extra Spec": "추가 사양 추가", "Add Host": "", "Add IP": "IP 추가", "Add Label": "라벨 추가", "Add Member": "멤버 추가", "Add Metadata": "메타데이터 추가", "Add NUMA Node": "NUMA 노드 추가", "Add Network": "네트워크 추가", "Add Policy": "", "Add Property": "속성 추가", "Add Router": "라우터 추가", "Add Virtual LAN": "가상 LAN 추가", "Add hosts to the aggregate or remove hosts from it. Hosts can be in multiple aggregates.": "집합(Aggregate)에 호스트를 추가하거나 제거한다. 호스트는 여러 집합(Aggregate)에 존재할 수 있다.", "Add network": "네트워크 추가", "Add scheduler hints": "스케쥴러 힌트 추가", "Additional Labels": "추가 레이블", "Additional routes announced to the instance, one entry per line(e.g. *************/24,***********)": "인스턴스에 추가적인 경로가 알림됨. 라인당 한개의 엔트리(예. *************/24,***********)", "Additional routes announced to the instance, one entry per line(e.g. {ip})": "인스턴스에 추가적인 경로가 알림됨. 라인당 한개의 엔트리(예. {ip})", "Address": "주소", "Address Record": "주소 레코드", "Addresses": "주소", "Admin State": "관리 상태", "Admin State Up": "관리자 상태 활성화", "Admin Status": "관리자 상태", "Administrator": "관리자", "Adopt Complete": "적용 완료", "Adopt Failed": "적용 실패", "Adopt In Progress": "적용 진행중", "Advanced": "고급", "Advanced Options": "고급 옵션", "Advanced Params": "고급 매개변수", "Affiliated Domain": "제휴 도메인", "Affiliated Domain ID/Name": "제휴 도메인 ID/이름", "Affinity": "어피니티", "Affinity (mandatory):": "어피니티 (필수)", "Affinity (not mandatory):": "어피니티 (필수 아님)", "Afghanistan": "아프가니스탄", "After attaching interface, you may need to login the instance to update the network interface configuration and restart the network service.": "인스턴스를 결합 후 네트워크 인터페이스 설정을 수정하기 위해 인스턴스에 로그인 후 네트워크 서비스를 재시작해야 합니다.", "After disable the compute service, the new instance will not schedule to the compute node.": "컴퓨트 서비스를 비활성화 후 새로운 인스턴스는 컴퓨트 노드에 스케쥴되지 않습니다", "After shelving, the instance will be shut down, resources will be released, and the snapshot will be saved to Glance. This will take about a few minutes, please be patient. You also can choose to unshelve to restore the instance.": "보류(shelving)하게되면 인스턴스는 종료되고 자원은 해제되며 글랜스에 스냅샷이 저장됩니다. 몇분이 소요될 수 있습니다. 잠시만 기다려 주세요. 인스턴스를 복원하기 위해 보류를 해제할 수 있습니다.", "After the share is expanded, the share cannot be reduced.": "쉐어가 확장된 후 쉐어를 줄일 수 없습니다.", "After the volume is expanded, the volume cannot be reduced.": "볼륨이 확장된 후 볼륨을 줄일 수 없습니다.", "Agent": "에이전트", "Agree to force shutdown": "강제 종료 동의", "Albania": "알바니아", "Algeria": "알제리", "All": "모든", "All Flavors": "모든 Flavor", "All ICMP": "모든 ICMP", "All Images": "모든 이미지", "All Networks": "모든 네트워크", "All Port": "모든 포트", "All Proto": "", "All QoS Policies": "모든 QoS 정책", "All TCP": "모든 TCP", "All UDP": "모든 UDP", "All data downloaded.": "모든 데이터가 다운로드 되었습니다.", "All network segments are indicated by \"*\", not \"0.0.0.0/0\"": "모든 네트워크 세그먼트는 \"0.0.0.0/0\"이 아닌 \"*\"로 표시됩니다.", "Allocate IP": "IP 할당", "Allocation Pools": "할당 풀", "Allowed Address Pairs": "허용된 주소 쌍", "Allowed Host": "허용된 호스트", "Always": "항상", "American Samoa": "미국령 사모아", "An object with the same name already exists": "동일한 이름의 객체가 이미 존재합니다.", "Andorra": "안도라", "Angola": "앙골라", "Anguilla": "안굴라", "Anti-Affinity": "안티 어피니티", "Anti-affinity (mandatory):": "안티 어피니티(필수)", "Anti-affinity (not mandatory):": "안티 어피니티(필수 아님))", "Antigua and Barbuda": "안티구아와 바르부다", "Any": "Any", "Any(Random)": "Any(랜덤)", "Application Credentials": "애플리케이션 크레덴셜", "Application Template": "애플리케이션 템플릿", "Apply Latency(ms)": "지연 적용", "Applying": "적용중", "Arch": "아키텍처", "Architecture": "아키텍처", "Are you sure set the project { project } as the default project? User login is automatically logged into the default project.": "{ project } 프로젝트를 기본 프로젝트로 설정하시겠습니까? 사용자 로그인은 기본 프로젝트에 자동으로 로그인됩니다.", "Are you sure to cancel transfer volume { name }? ": "볼륨 { name } 전송을 취소합니까?", "Are you sure to delete instance { name }? ": "인스턴스 { name }를 삭제 하시겠습니까?", "Are you sure to delete volume { name }? ": "볼륨 { name }을 삭제 하시겠습니까?", "Are you sure to download data?": "데이터를 다운르도 하시겠습니까?", "Are you sure to forbidden domain { name }? Forbidden the domain will have negative effect, and users associated with the domain will not be able to log in if they are only assigned to the domain": "{ name } 도메인을 차단 하시겠습니까? 도메인을 차단하면 부정적인 영향을 줄 수 있으며 도메인에 할당된  사용자라면 로그인 할 수 없게 됩니다.", "Are you sure to forbidden project { name }? Forbidden the project will have negative effect, and users associated with the project will not be able to log in if they are only assigned to the project": "{ name } 프로젝트를 차단하시겠습니까? 프로젝트를 차단하면 부정적인 영향을 줄 수 있으며 프로젝트에 할당된 사용자라면 로그인 할 수  없게 됩니다.", "Are you sure to forbidden user { name }? Forbidden the user will not allow login in ": "{ name } 사용자를 차단하시겠습니까? 사용자를 차단하면 로그인이 허용되지 않습니다.", "Are you sure to jump directly to the console? The console will open in a new page later.": "콘솔로 이동하시겠습니까? 콘솔은 새 창에서 열리게됩니다.", "Are you sure to remove the default project?": "기본 프로젝트를 제거하시겠습니까?", "Are you sure to shelve instance { name }? ": "{ name } 인스턴스를 보관하시겠습니까?", "Are you sure to { action } {name}?": "{ action } { name}을(를) 진행하시겠습니까?", "Are you sure to {action} (Host: {name})?": "", "Are you sure to {action} (Segment: {name})?": "", "Are you sure to {action} (instance: {name})?": "{ action } (인스턴스: { name })을(를) 진행하시겠습니까?", "Are you sure to {action}?": "{ action } 을 실행하시겠습니까?=", "Are you sure to {action}? (Record Set: {name} - {id})": "{ action }을(를) 진행하시겠습니까? (레코드 셋: { name } - { id })", "Are you sure to {action}? (Zone: {name})": "{ action }을(를) 진행하시겠습니까? (존: { name })", "Argentina": "아르헨티나", "Armenia": "아르메니아", "Aruba": "아루바", "Associate": "연결", "Associate Floating IP": "유동 IP 연결", "Associate IP": "IP 연결", "Associate Network": "네트워크 연결", "Associated Ports": "", "Associated QoS Spec ID": "관련 QoS 사양 ID", "Associated QoS Spec ID/Name": "관련 QoS 사양 ID/이름", "Associated Resource": "연결된 리소스", "Associated Resource Types": "연결된 리소스 유형", "Associated Resources": "연결된 리소스", "Associations": "결합", "Attach": "연결", "Attach Instance": "인스턴스 연결", "Attach Interface": "인터페이스 연결", "Attach Network": "네트워크 연결", "Attach Security Group": "보안 그룹 연결", "Attach USB": "USB 연결", "Attach Volume": "볼륨 연결", "Attach volume": "볼륨 연결", "Attached Device": "연결된 디바이스", "Attached To": "연결된 곳", "Attaching": "연결", "Attachments Info": "첨부 정보", "Attributes": "속성", "Audited": "", "Australia": "호주", "Austria": "오스트리아", "Auth Algorithm": "인증 알고리즘", "Auth Key": "인증 키", "Auto": "자동", "Auto Healing": "자동 힐링", "Auto Inspect": "자동 검사", "Auto Scaling": "자동 확장", "Auto allocate mac address": "MAC 주소 자동 할당", "Auto scaling feature will be enabled": "자동 확장 기능 활성화됩니다.", "Automatically Assigned Address": "자동 할당된 주소", "Automatically repair unhealhty nodes": "비정상 노드 자동 복구", "Availability Zone": "가용 영역", "Availability Zone Hints": "가용 영역 힌트", "Availability Zone Info": "가용 영역 정보", "Availability Zone Name": "가용 영역 명", "Availability Zones": "가용 영역", "Availability zone refers to a physical area where power and network are independent of each other in the same area. In the same region, the availability zone and the availability zone can communicate with each other in the intranet, and the available zones can achieve fault isolation.": "가용영역은 동일한 지역에서 서로 다른 전원 및 네트워크를 가진 물리적인 영역을 의미합니다. 동일한 영역에서 가용 영역과 가용 영역은 인터넷을 통해 연결될 수 있으며 가용영역은 장애를 격리 시킬수 있습니다. ", "Available": "사용 가능", "Available Zone": "가용 영역", "Average PGs per OSD": "OSD당 평균 PG", "Awaiting Transfer": "전송 대기 중", "Azerbaijan": "아제르바이젠", "BLOCK I/O(B)": "BLOCK I/O(B)", "Back": "뒤로", "Back End": "끝으로 가기", "Back to Home": "홈 페이지로 돌아가기", "Back to login page": "로그인 페이지로 돌아가기", "Backend": "백엔드", "Backend Name": "백엔드 명", "Backing Up": "백업 중", "Backup": "백업", "Backup Detail": "백업 파일 상세", "Backup File": "백업 파일", "Backup File Location": "백업 파일 위치", "Backup Mode": "백업 모드", "Backups": "백업", "Backups & Snapshots": "백업 & 스냅샷", "Bad Gateway (code: 502) ": "잘못된 게이트웨이(코드: 502)", "Bahamas": "바하마제도", "Bahrain": "바레인", "BandWidth Limit Egress": "이그레스 대역폭 제한", "BandWidth Limit Ingress": "인그레스 대역폭 제한", "Bandwidth limit": "대역폭 제한", "Bangladesh": "방글라데시", "Barbados": "바베이도스", "Bare Metal": "베어 메탈", "Bare Metal Enroll": "베어 메탈 등록", "Bare Metal Node Detail": "베어메탈 노드 상세", "Bare Metal Nodes": "베어메탈 노드", "BareMetal Parameters": "베어메탈 파라미터", "Base Config": "기본 설정", "Base Info": "기본 정보", "Basic Parameters": "기본 파라미터", "Batch Allocate": "배치 할당", "Before deleting the project, it is recommended to clean up the resources under the project.": "프로젝트를 삭제하기 전에 프로젝트 하위의 자원을 정리하는 것이 좋습니다.", "Belarus": "벨라루스", "Belgium": "벨기에", "Belize": "벨리즈", "Benin": "베냉", "Bermuda": "버뮤다", "Bhutan": "부탄", "Big Data": "빅 데이터", "Bind Device": "디바이스 결합", "Bind Device Type": "디바이스 유형 결합", "Bind Resource": "리소스 결합", "Bind Resource Name": "리소스 명 결합", "Binding": "결합", "Binding Groups": "그룹 결합", "Binding Instance": "인스턴스 결합", "Binding Profile": "프로파일 결합", "Binding Users": "사용자 결합", "Blank Volume": "빈 볼륨", "Block Device Mapping": "블록 디바이스 매핑", "Block Migrate": "블록 마이그레션", "Block Storage Services": "블록 스토리지 서비스", "Blocked": "차단", "Bolivia": "볼리비아", "Boot Device": "부트 디바이스", "Boot From Volume": "볼륨에서 부팅", "Boot Interface": "부트 인터페이스", "Boot Mode of BIOS": "BIOS의 부팅 모드", "Bootable": "부팅가능", "Bootable Volume": "부팅가능 볼륨", "Bosnia and Herzegovina": "보스니아 및 헤르체고비나", "Both of Frontend and Backend": "프론트엔드 및 백엔드", "Botswana": "보트스와나", "Brazil": "브라질", "British Indian Ocean Territory": "영국령 인도양 식민지", "Brunei Darussalam": "브루나이 다루살람", "Build": "빌드", "Building": "빌딩", "Bulgaria": "불가리아", "Burkina Faso": "부르키나 파소", "Burst limit": "버스티 제한", "Burundi": "부룬디", "By default, for security reasons, application credentials are forbidden from being used for creating or destructing additional application credentials or keystone trusts. If your application credential needs to be able to perform these actions, check unrestricted.": "기본적으로 보안상의 이유로 추가 애플리케이션 자격 증명이나 Keystone 신뢰를 생성하거나 파괴하는 데 애플리케이션 자격 증명을 사용하는 것이 금지되어 있습니다. 애플리케이션 자격 증명이 이러한 작업을 수행할 수 있어야 하는 경우 무제한을 선택하세요.", "CA Certificate": "CA 인증서", "CA Certificates": "CA 인증서", "CHECK COMPLETE": "검사 완료", "CIDR": "CIDR", "CIDR Format Error(e.g. ***********/24, 2001:DB8::/48)": "CIDR 형식 오류(예. ***********/24, 2001:DB8::/48)", "CIFS": "CIFS", "CMD": "CMD", "COE": "COE", "COE Version": "COE 버전", "CPU": "CPU", "CPU %": "CPU %", "CPU (Core)": "CPU (Core)", "CPU Arch": "CPU 아키텍처", "CPU Cores": "CPU 코어", "CPU Policy": "CPU 정책", "CPU Thread Policy": "CPU 쓰레드 정책", "CPU Usage(%)": "CPU 사용량(%)", "CPU Usages (Core)": "CPU 사용량 (Core)", "CPU value is { cpu }, NUMA CPU value is { totalCpu }, need to be equal. ": "CPU 값은 { cpu }, NUMA CPU 값은 { totalCpu }, 동일해야 합니다.", "CPU(Core)": "CPU(Core)", "CREATE COMPLETE": "생성 완료", "CREATE FAILED": "생성 실패", "CREATE IN PROGRESS": "생성 진행중", "Cache Service": "캐시서비스", "Cameroon": "Cameroon", "Can add { number } {name}": "{number} {name}을 추가", "Canada": "Canada", "Cancel": "취소", "Cancel Download": "다운로드 취소", "Cancel Select": "선택 취소", "Cancel Transfer": "전송 취소", "Cancel download successfully.": "다운로드를 취소했습니다.", "Cancel upload successfully.": "업로드를 취소했습니다.", "Canonical Name Record": "CNAME 레코드", "Capacity & Type": "용량 & 유형", "Capacity (GiB)": "용량 (GiB)", "Cape Verde": "Cape Verde", "Capsule Detail": "캡슐 세부 정보", "Capsule Type": "캡슐 유형", "Capsules": "캡슐", "Cascading deletion": "연쇄 삭제", "Cast Rules To Read Only": "읽기 전용 규칙 정하기", "Category": "카테고리", "Cayman Islands": "Cayman Islands", "CentOS": "CentOS", "Central African Republic": "Central African Republic", "CephFS": "CephFS", "Cephx": "Cephx", "Cert": "인증서", "Certificate Authority Authorization Record": "CAA 레코드", "Certificate Content": "인증서 내용", "Certificate Detail": "인증서 세부 정보", "Certificate Name": "인증서 이름", "Certificate Type": "인증서 유형", "Certificates": "인증서", "Chad": "Chad", "Change Password": "비밀번호 변경", "Change Type": "유형 변경", "Change password": "비밀번호 변경", "Change type": "유형 변경", "Changed Node Count": "변경된 노드 수", "Channel": "채널", "Chassis ID": "섀시 ID", "Check Can Live Migrate Destination": "목적지의 라이브 마이그레이션 가능 여부 확인", "Check Can Live Migrate Source": "출발지의 라이브 마이그레이션 가능 여부 확인", "Check Complete": "완료 확인", "Check Failed": "실패 확인", "Check In Progress": "진행중 확인", "Checksum": "체크섬", "Chile": "Chile", "China": "China", "Choose a Network Driver": "네트워크 드라이버 선택", "Choose a host to live migrate instance to. If not selected, the scheduler will auto select target host.": "인스턴스를 라이브 마이그레이션할 호스트를 선택합니다. 선택하지 않으면, 스케줄러가 대상 호스트를 자동으로 선택합니다.", "Choose a host to migrate instance to. If not selected, the scheduler will auto select target host.": "인스턴스를 마이그레이션할 호스트를 선택합니다. 선택하지 않으면, 스케줄러가 대상 호스트를 자동으로 선택합니다.", "Choosing a QoS policy can limit bandwidth and DSCP": "QoS 정책을 선택하면 대역폭과 DSCP가 제한될 수 있습니다.", "Christmas Island": "Christmas Island", "Cidr": "CIDR", "Cinder Service": "Cinder 서비스", "Cipher": "암호", "Clean Failed": "실패 정리", "Clean Wait": "대기 정리", "Cleaning": "정리", "Clear Gateway": "게이트웨이 지우기", "Clear selected": "선택 지우기", "Click To View": "보려면 클릭", "Click here for filters.": "필터를 보려면 여기를 클릭하세요.", "Click to Upload": "업로드하려면 클릭", "Click to show detail": "세부 정보를 보려면 클릭", "Clone Volume": "볼륨 복제", "Clone volume": "볼륨 복제", "Close": "닫기", "Close External Gateway": "외부 게이트웨이 닫기", "Close all notifications.": "모든 알림 닫습니다.", "Close external gateway": "외부 게이트웨이 닫기", "Cloud": "클라우드", "Cloud Container Engine": "클라우드 컨테이너 엔진", "Cluster Detail": "클러스터 세부 정보", "Cluster Distro": "클러스터 배포판", "Cluster Info": "클러스터 정보", "Cluster Management": "클러스터 관리", "Cluster Name": "클러스터 이름", "Cluster Network": "클러스터 네트워크", "Cluster Template": "클러스터 템플릿", "Cluster Template Detail": "클러스터 템플릿 세부 정보", "Cluster Template Name": "클러스터 템플릿 이름", "Cluster Templates": "클러스터 템플릿", "Cluster Type": "클러스터 유형", "Clusters": "클러스터", "Clusters Management": "클러스터 관리", "Cocos (Keeling) Islands": "Cocos (Keeling) Islands", "Code": "코드", "Cold Migrate": "콜드 마이그레이션", "Colombia": "Colombia", "Command": "명령어", "Command to run to check health": "health 체크를 위해 실행할 명령어", "Command was successfully executed at container {name}.": "명령어가 {name} 컨테이너에서 성공적으로 실행되었습니다.", "Commas ‘,’ are not allowed to be in a tag name in order to simplify requests that specify lists of tags": "쉼표 ','는 태그 목록을 지정하는 요청을 단순화하기 위해 태그 이름에 포함될 수 없습니다.", "Commit Latency(ms)": "커밋 지연 시간(ms)", "Common Server": "공용 서버", "Comoros": "Comoros", "Compute": "Compute", "Compute Hosts": "Compute 호스트", "Compute Live Migration": "Compute 라이브 마이그레이션", "Compute Live Resize Instance": "Compute 인스턴스 라이브 크기 조정", "Compute Node status": "Compute 노드 상태", "Compute Optimized": "Compute 최적화", "Compute Optimized Info": "Compute 최적화 정보", "Compute Optimized Type": "Compute 최적화 유형", "Compute Optimized Type with GPU": "Compute 최적화 GPU 유형", "Compute Pause Instance": "Compute 인스턴스 중지", "Compute Reboot Instance": "Compute 인스턴스 재부팅", "Compute Resume Instance": "Compute 인스턴스 재개", "Compute Service": "Compute 서비스", "Compute Services": "Compute 서비스", "Compute Start Instance": "Compute 인스턴스 시작", "Compute Stop Instance": "Compute 인스턴스 정지", "Compute Suspend Instance": "Compute 인스턴스 중지", "Compute Unpause Instance": "Compute 인스턴스 재개", "Conductor Live Migrate Instance": "Conductor 인스턴스 라이브 마이그레이션", "Conductor Live Resize Instance": "Conductor 인스턴스 라이브 크기 조정", "Conductor Migrate Server": "Conductor 서버 마이그레이션", "Config Overview": "설정 개요", "Configuration": "설정", "Configuration Detail": "설정 세부 정보", "Configuration Group": "설정 그룹", "Configuration Group ID/Name": "설정 그룹 ID/이름", "Configuration Groups": "설정 그룹", "Configuration Update": "설정 업데이트", "Configured Disk (GiB)": "설정된 디스크 (GiB)", "Configured Memory (GiB)": "설정된 메모리 (GiB)", "Confirm": "확인", "Confirm Config": "설정 확인", "Confirm Password": "비밀번호 확인", "Confirm Resize or Migrate": "크기 조정 또는 마이그레이션 확인", "Confirm Shared Key": "공유 키 확인", "Confirming Resize or Migrate": "크기 조정 또는 마이그레이션 확인", "Connect Subnet": "서브넷 연결", "Connect router": "라우터 연결", "Connected Threads": "연결된 스레드", "Connection Examples": "연결 예시", "Connection Information": "연결 정보", "Connection Limit": "연결 제한", "Consecutive failures needed to report unhealthy": "unhealthy 보고를 위한 연속적인 실패 횟수", "Console": "콘솔", "Console Interface": "콘솔 인터페이스", "Console Log": "콘솔 로그", "Consumer": "소비자", "Container": "컨테이너", "Container Creating": "컨테이너 생성", "Container Deleting": "컨테이너 삭제", "Container Detail": "컨테이너 세부 정보", "Container Format": "컨테이너 포맷", "Container Killing": "컨테이너 종료", "Container Name": "컨테이너 이름", "Container Pausing": "컨테이너 중지", "Container Rebooting": "컨테이너 재부팅", "Container Rebuilding": "컨테이너 리빌딩", "Container Restarting": "컨테이너 재시작", "Container Starting": "컨테이너 시작", "Container Status": "컨테이너 상태", "Container Stopping": "컨테이너 정지", "Container Unpausing": "컨테이너 재개", "Container Version": "컨테이너 버전", "Containers": "컨테이너", "Containers CPU": "컨테이너 CPU", "Containers Disk (GiB)": "컨테이너 디스크 (GiB)", "Containers Info": "컨테이너 정보", "Containers Management": "컨테이너 관리", "Containers Memory (MiB)": "컨테이너 메모리 (MiB)", "Content": "내용", "Content Type": "내용 유형", "Continue": "계속하다", "Continue Upload": "업로드 계속", "Control Attribute": "", "Control Attributes": "", "Control Location": "컨트롤 위치", "Cook Islands": "Cook Islands", "Copy": "복사", "Copy File": "파일 복사", "CoreOS": "CoreOS", "Costa Rica": "Costa Rica", "Cote D'Ivoire": "Cote D'Ivoire", "Count": "개수", "Crashed": "충돌됨", "Create": "생성", "Create Allowed Address Pair": "허용된 주소 쌍 생성", "Create Application Credentials": "애플리케이션 인증서 생성", "Create Backup": "백업 생성", "Create Bandwidth Limit Rule": "대역폭 제한 규칙 생성", "Create Bare Metal Node": "베어메탈 노드 생성", "Create Capsule": "캡슐 생성", "Create Certificate": "인증서 생성", "Create Cluster": "클러스터 생성", "Create Cluster Template": "클러스터 템플릿 생성", "Create Complete": "생성 완료", "Create Configurations": "설정 생성", "Create Container": "컨테이너 생성", "Create DSCP Marking Rule": "DSCP 표시 규칙 생성", "Create Database": "데이터베이스 생성", "Create Database Backup": "데이터베이스 백업 생성", "Create Database Instance": "데이터베이스 인스턴스 생성", "Create Default Pool": "디폴트 풀 생성", "Create Domain": "도메인 생성", "Create Encryption": "암호화 생성", "Create Extra Spec": "추가 사양 생성", "Create Failed": "생성 실패", "Create Firewall": "", "Create Firewall Policy": "", "Create Flavor": "Flavor 생성", "Create Folder": "폴더 생성", "Create Host Aggregate": "호스트 집계 생성", "Create IPsec Site Connection": "IPsec 사이트 연결 생성", "Create Image": "이미지 생성", "Create In Progress": "생성 진행중", "Create Instance": "인스턴스 생성", "Create Instance Snapshot": "인스턴스 스냅샷 생성", "Create Ironic Instance": "Ironic 인스턴스 생성", "Create Keypair": "키페어 생성", "Create Listener": "리스너 생성", "Create Loadbalancer": "로드밸런서 생성", "Create Network": "네트워크 생성", "Create New Network": "새로운 네트워크 생성", "Create Node": "노드 생성", "Create Policy": "", "Create Port": "포트 생성", "Create Port Forwarding": "포트 포워딩 생성", "Create Port Group": "포트 그룹 생성", "Create Project": "프로젝트 생성", "Create QoS Policy": "QoS 정책 생성", "Create QoS Spec": "QOS 스펙 생성", "Create RBAC Policy": "", "Create Record Set": "레코드셋 생성", "Create Role": "역할 생성", "Create Router": "라우터 생성", "Create Rule": "규칙 생성", "Create Security Group": "보안 그룹 생성", "Create Segment": "", "Create Server Group": "서버 그룹 생성", "Create Share": "공유 생성", "Create Share Group": "공유 그룹 생성", "Create Share Group Type": "공유 그룹 유형 생성", "Create Share Metadata": "공유 메타데이터 생성", "Create Share Network": "공유 네트워크 생성", "Create Share Type": "공유 유형 생성", "Create Snapshot": "스냅샷을 생성", "Create Stack": "스택 생성", "Create Static Route": "정적 경로 생성", "Create Subnet": "서브넷 생성", "Create Time": "시간 생성", "Create Transfer": "전송 생성", "Create Type": "유형 생성", "Create User": "사용자 생성", "Create User Group": "사용자 그룹 생성", "Create VPN": "VPN 생성", "Create VPN Endpoint Group": "VPN 엔드포인트 그룹 생성", "Create VPN IKE Policy": "VPN IKE 정책 생성", "Create VPN IPsec Policy": "VPN IPsec 정책 생성", "Create Virtual Adapter": "가상 어댑터 생성", "Create Volume": "볼륨 생성", "Create Volume Backup": "볼륨 백업 생성", "Create Volume Snapshot": "볼륨 스냅샷 생성", "Create Volume Type": "볼륨 유형 생성 ", "Create Zone": "Zone 생성", "Create a full backup, the system will automatically create a new backup chain, the full backup name is the backup chain name; Create an incremental backup, the system will automatically create an incremental backup under the newly created backup chain.": "전체 백업을 생성하면, 시스템이 새 백업 체인을 자동으로 생성하고, 전체 백업 이름은 백업 체인 이름입니다. 증분 백업을 생성하면, 시스템이 새로 생성된 백업 체인 아래에 증분 백업을 자동으로 생성합니다.", "Create firewall": "", "Create host aggregate": "호스트 집계 생성", "Create image": "이미지 생성", "Create instance": "인스턴스 생성", "Create ironic instance": "ironic 인스턴스 생성", "Create new AZ": "새로운 AZ 생성", "Create rule": "규칙 생성", "Create security group": "보안 그룹 생성", "Create server group": "서버 그룹 생성", "Create static route": "정적 경로 생성", "Create volume": "볼륨 생성", "Create volume backup": "볼륨 백업 생성", "Created": "생성됨", "Created At": "생성일", "Created Time": "생성된 시간", "Created Volumes": "생성된 볼륨", "Creating": "생성중", "Creating From Snapshot": "스냅샷에서 생성", "Creation Timeout (Minutes)": "생성 타임아웃 (분)", "Credential Type": "인증서 유형", "Croatia (local name: Hrvatska)": "Croatia (local name: Hrvatska)", "Cuba": "Cuba", "Current Availability Zones": "현재 AZ", "Current Compute Host": "현재 Compute 호스트", "Current Connections": "현재 연결", "Current Disk (GiB)": "현재 디스크 (GiB)", "Current Flavor": "현재 Flavor", "Current Host": "현재 호스트", "Current Interface": "현재 인터페이스", "Current Master Node Count": "현재 마스터 노드 수", "Current Node Count": "현재 노드 수", "Current Password": "현재 비밀번호", "Current Path: ": "현재 경로", "Current Project": "현재 프로젝트", "Current Project Images": "현재 프로젝트 이미지", "Current Project Networks": "현재 프로젝트 네트워크", "Current Project QoS Policies": "현재 프로젝트 QoS 정책", "Current QoS policy name": "현재 QoS 정책 이름", "Current Rules": "", "Current Status": "현재 상태", "Current Storage Backend": "현재 스토리지 백엔드", "Current data downloaded.": "현재 데이터가 다운로드되었습니다.", "Custom": "사용자 정의", "Custom Headers": "맞춤 헤더", "Custom ICMP Rule": "사용자 ICMP 규칙", "Custom Metadata": "사용자 정의 메타데이터", "Custom Properties Info": "사용자 정의 속성 정보", "Custom TCP Rule": "사용자 TCP 규칙", "Custom Trait": "사용자 정의 기능", "Custom UDP Rule": "사용자 UDP 규칙", "Cut": "잘라내기", "Cut File": "파일 잘라내기", "Cyprus": "Cyprus", "Czech Republic": "", "DC/OS": "", "DCCP": "", "DEGRADED: One or more of the entity’s components are in ERROR": "DEGRADED: 엔터티 구성 요소 중 하나 이상이 ERROR 상태입니다.", "DELETE COMPLETE": "삭제 성공", "DELETE FAILED": "삭제 실패", "DELETE_IN PROGRESS": "삭제 진행중", "DENY": "", "DHCP": "DHCP", "DHCP Agent": "DHCP 에이전트", "DHCP Agents": "DHCP 에이전트들", "DISK IOPS": "디스크 속도", "DISK Usage(%)": "디스크 사용량", "DNS": "DNS", "DNS Assignment": "", "DNS Name": "DNS 이르", "DNS Nameservers": "DNS 서버 이름", "DNS Reverse": "", "DNS Zones": "", "DNS Zones Detail": "", "DPD Action": "", "DPD Interval (sec)": "DPD 주기(초)", "DPD actions controls the use of Dead Peer Detection Protocol.": "DPD Action은 Dead Peer Detection Protocol의 사용을 제어합니다.", "DPD timeout (sec)": "", "DRAINING: The member is not accepting new connections": "DRAINING: 구성원이 새 연결을 수락하지 않습니다.", "DSCP Marking": "", "Danger": "", "Data Disk": "", "Data Disks": "", "Data Protection": "데이터 보호", "Data Source Type": "데이터 소스 타입", "Database": "데이터베이스", "Database Backup Detail": "데이터베이스 백업 상세정보", "Database Disk (GiB)": "데이터베이스 디스크(GiB)", "Database Flavor": "", "Database Instance": "데이터베이스 인스턴스", "Database Instance Detail": "데이터베이스 인터페이스 상세설명", "Database Instance Name": "데이터베이스 인스턴스 이름", "Database Instance Status": "데이터베이스 인스턴스 상태", "Database Instances": "", "Database Name": "데이터베이스 이름", "Database Port": "데이터베이스 포트", "Database Service": "데이터베이스 서비스", "Databases": "", "Datastore": "데이터 스토어", "Datastore Type": "데이터 스토어 타입", "Datastore Version": "데이터 스토어 버전", "Deactivated": "", "Debian": "데비안", "Dedicated": "", "Default Policy": "기본 정책", "Default Project": "기본 프로젝트", "Default Project ID/Name": "기본 프로젝트 ID/이름", "Default is slaac, for details, see https://docs.openstack.org/neutron/latest/admin/config-ipv6.html": "기본값은 slaac입니다. 자세한 내용은 다음을 참조하세요. https://docs.openstack.org/neutron/latest/admin/config-ipv6.html", "Defaults": "", "Defines the admin state of the health monitor.": "Health monitor의 관리 상태를 정의합니다.", "Defines the admin state of the listener.": "Listener의 관리 상태를 정의합니다", "Defines the admin state of the pool.": "Pool의 관리 상태를 정의합니다.", "Defines the admin state of the port.": "Port의 관리 상태를 정의합니다.", "Degraded": "", "Delay Interval(s)": "지연 주기(초)", "Delete": "삭제", "Delete Allowed Address Pair": "Allowed Address Pair 삭제", "Delete Application Credential": "Application Credential 삭제", "Delete Bandwidth Egress Rules": "Bandwidth Egress Rules 삭제", "Delete Bandwidth Ingress Rules": "Bandwidth Ingress Rules 삭제", "Delete Capsule": "Capsule 삭제", "Delete Certificate": "Certificate 삭제", "Delete Cluster": "Cluster 삭제", "Delete Cluster Template": "Cluster Template 삭제", "Delete Complete": "삭제 완료", "Delete Configuration": "삭제 설정", "Delete Container": "Container 삭제", "Delete DSCP Marking Rules": "DSCP Marking Rules 삭제", "Delete Database": "Database 삭제", "Delete Database Backup": "", "Delete Default Pool": "", "Delete Domain": "Domain 삭제", "Delete Encryption": "Encryption 삭제", "Delete Extra Specs": "Extra Specs 삭제", "Delete Failed": "삭제 실패", "Delete File": "File 삭제", "Delete Firewall": "", "Delete Flavor": "Flavor 삭제", "Delete Folder": "Folder 삭제", "Delete Group": "Group 삭제", "Delete Host Aggregate": "Host Aggregate 삭제", "Delete IPsec Site Connection": "IPsec Site Connection 삭제", "Delete Image": "Image 삭제", "Delete In Progress": "", "Delete Instance": "Instance 삭제", "Delete Instance Snapshot": "Instance Snapshot 삭제", "Delete Keypair": "Keypair 삭제", "Delete Listener": "Listener 삭제", "Delete Load Balancer": "Load Balancer 삭제", "Delete Member": "Member 삭제", "Delete Metadata": "Metada<PERSON> 삭제", "Delete Network": "Network 삭제", "Delete Node": "Node 삭제", "Delete Policy": "", "Delete Port": "Port 삭제", "Delete Port Forwarding": "Port Forwarding 삭제", "Delete Port Group": "Port Group 삭제", "Delete Project": "Project 삭제", "Delete QoS Policy": "QoS Policy 삭제", "Delete QoS Spec": "QOS Spec 삭제", "Delete RBAC Policy": "", "Delete Record Set": "Record Set 삭제", "Delete Role": "Role 삭제", "Delete Router": "Router 삭제", "Delete Rule": "Rule 삭제", "Delete Security Group": "Security Group 삭제", "Delete Server Group": "Server Group 삭제", "Delete Share": "Share 삭제", "Delete Share Access Rule": "Share Access Rule 삭제", "Delete Share Group": "Share Group 삭제", "Delete Share Group Type": "Share Group Type 삭제", "Delete Share Metadata": "Share Metadata 삭제", "Delete Share Network": "Share Network 삭제", "Delete Share Server": "Share Server 삭제", "Delete Share Type": "Share Type 삭제", "Delete Static Route": "Static Route 삭제", "Delete Subnet": "Subnet 삭제", "Delete User": "User 삭제", "Delete VPN": "VPN 삭제", "Delete VPN EndPoint Groups": "VPN EndPoint Groups 삭제", "Delete VPN IKE Policy": "VPN IKE Policy 삭제", "Delete VPN IPsec Policy": "VPN IPsec Policy 삭제", "Delete Virtual Adapter": "Virtual Adapter 삭제", "Delete Volume": "Volume 삭제", "Delete Volume Backup": "Volume Backup 삭제", "Delete Volume Snapshot": "Volume Snapshot 삭제", "Delete Volume Type": "Volume Type 삭제", "Delete Volume on Instance Delete": "인스턴스 삭제 시 볼륨 삭제", "Delete Zone": "Zone 삭제", "Delete metadata": "Metada<PERSON> 삭제", "Deleted": "삭제됨", "Deleted At": "삭제 날짜", "Deleted with the instance": "Instance와 함께 삭제됨.", "Deleting": "삭제 중", "Deleting this stack will delete all resources deployed by the stack.": "이 Stack을 삭제하면 Stack에 의해 배포된 모든 리소스가 삭제됩니다.", "Democratic Republic of the Congo": "", "Denmark": "", "Denying": "", "Deploy Failed": "Deploy 실패", "Deploy Wait": "", "Deploying": "", "Deployment Parameters": "", "Description": "설명", "Dest Folder": "", "Destination": "목적지", "Destination CIDR": "", "Destination IP": "", "Destination IP Address/Subnet": "", "Destination Port": "", "Destination Port/Port Range": "", "Detach": "", "Detach Instance": "", "Detach Interface": "", "Detach Network": "", "Detach Security Group": "", "Detach Volume": "", "Detach interface": "", "Detaching": "", "Detail": "상세", "Detail Info": "상세정보", "Details": "", "Details *": "", "Details about the PTR record.": "PTR record의 상세정보.", "Device": "", "Device ID": "장치 ID", "Device ID/Name": "", "Device Owner": "", "Devicemapper": "", "Direct": "", "Direction": "방향", "Disable": "비활성화", "Disable Cinder Service": "Cinder Service 비활성화", "Disable Compute Host": "Compute Host 비활성화", "Disable Compute Service": "Compute Service 비활성화", "Disable Gateway": "Gateway 비활성화", "Disable Neutron Agent": "Neutron Agent 비활성화", "Disable SNAT": "SNAT 비활성화", "Disable TLS": "TLS 비활성화", "Disable compute host": "Compute Host 비활성화", "Disabled": "비활성화됨", "Disabling port security will turn off the security group policy protection and anti-spoofing protection on the port. General applicable scenarios: NFV or operation and maintenance Debug.": "Port security을 비활성화하면 Port의 Security group policy 보호와 Anti-spoofing 보호가 꺼집니다. 적용 가능한 일반적인 시나리오: NFV 운영 또는 Maintenance, Debug.", "Disabling the project will have a negative impact. If the users associated with the project are only assigned to the project, they will not be able to log in": "프로젝트를 비활성화하면 부정적인 영향을 미칩니다. 프로젝트와 관련된 사용자가 해당 프로젝트에만 할당된 경우 로그인할 수 없습니다.", "Disassociate": "연결 해제", "Disassociate Floating IP": "Floating IP 연결 해제", "Disassociate Floating Ip": "Floating IP 연결 해제", "Disconnect Subnet": "", "Discovery URL": "", "Disk": "디스크", "Disk (GiB)": "디스크 (GiB)", "Disk Format": "디스크 포맷", "Disk Info": "디스크 정보", "Disk Tag": "디스크 태그", "Disk allocation (GiB)": "디스크 할당 (GiB)", "Disk size is limited by the min disk of flavor, image, etc.": "Disk size는 Flavor, Image 등의 최소 Disk size에 의해 제한됩니다.", "Djibouti": "", "Do Build And Run Instance": "Instance build 및 Run을 수행", "Do HH:mm": "", "Do not reset the normally mounted volume to the \"available\"、\"maintenance\" or \"error\" status. The reset state does not remove the volume from the instance. If you need to remove the volume from the instance, please go to the console of the corresponding project and use the \"detach\" operation.": "정상적으로 마운트된 볼륨을 \"available\",\"maintenance\" 또는 \"error\" 상태로 재설정하지 마십시오. 상태 재설정은 인스턴스에서 볼륨을 제거하지 않습니다. 인스턴스에서 볼륨을 제거해야 하는 경우 해당 프로젝트의 콘솔로 이동하여 \"detach\" 작업을 수행하십시오.", "Do not set with a backend": "백엔드로 설정하지 않음", "Docker": "도커", "Docker Hub": "도커 허브", "Docker Storage Driver": "도커 스토리지 드라이버", "Docker Swarm": "도커 스웜", "Docker Swarm Mode": "도커 스웜 모드", "Docker Volume Size (GiB)": "도커 볼륨 크기 (GiB)", "Domain": "", "Domain Detail": "", "Domain ID": "", "Domain ID/Name": "", "Domain Manager": "Domain 관리자", "Domain Name": "도메인 이름", "Domain name ending in.": "도메인 이름 끝", "Domains": "", "Dominica": "", "Down": "", "Download": "다운로드", "Download File": "다운로드 파일", "Download Image": "이미지 다운로드", "Download all data": "", "Download canceled!": "다운로드 취소!", "Download current data": "", "Download progress": "다운로드 진행", "Downloading": "다운로드 중", "Draining": "", "Driver": "드라이버", "Driver Handles Share Servers": "", "Driver Info": "드라이버 정보", "Driver Interface": "드라이버 인터페이스", "Duplicate tag name: {tag}": "", "EGP": "", "ENTRYPOINT": "엔트리포인트", "ESP": "", "Each instance belongs to at least one security group, which needs to be specified when it is created. Instances in the same security group can communicate with each other on the network, and instances in different security groups are disconnected from the internal network by default.": "각 인스턴스는 하나 이상의 Security Group에 속하며, Security Group을 생성할 때 지정해야 합니다. 동일한 Security Group의 인스턴스는 네트워크에서 서로 통신할 수 있으며 다른 Security Group의 인스턴스는 기본적으로 내부 네트워크에서 연결이 끊어집니다.", "Each new connection request is assigned to the next server in order, and all requests are finally divided equally among all servers. Commonly used for short connection services, such as HTTP services.": "각 새 연결 요청은 순서대로 다음 서버에 할당되고, 모든 요청은 모든 서버에서 동일하게 분할됩니다. 일반적으로 HTTP 서비스와 같은 짧은 연결 서비스에 사용됩니다.", "Each server can have up to 50 tags": "각 서버는 최대 50개의 태그를 가질 수 있습니다", "East Timor": "", "Ecuador": "", "Edit": "", "Edit Bandwidth Egress Limit Rule": "Bandwidth Egress Limit Rule 편집", "Edit Bandwidth Ingress Limit Rule": "Bandwidth Ingress Limit Rule 편집", "Edit Bare Metal Node": "Bare Metal Node 편집", "Edit Consumer": "Consumer 편집", "Edit Container": "Container 편집", "Edit DSCP Marking Rule": "DSCP Marking Rule 편집", "Edit Default Pool": "Default Pool 편집", "Edit Domain": "Domain 편집", "Edit Domain Permission": "Domain Permission 편집", "Edit Extra Spec": "Extra Spec 편집", "Edit Flavor": "Flavor 편집", "Edit Health Monitor": "Health Monitor 편집", "Edit Host Aggregate": "Host Aggregate 편집", "Edit IPsec Site Connection": "IPsec Site Connection 편집", "Edit Image": "Image 편집", "Edit Instance": "Instance 편집", "Edit Instance Snapshot": "Instance Snapshot 편집", "Edit Listener": "Listener 편집", "Edit Load Balancer": "Load Balancer 편집", "Edit Member": "Member 편집", "Edit Metadata": "Metada<PERSON> 편집", "Edit Port": "Port 편집", "Edit Port Forwarding": "Port Forwarding 편집", "Edit Port Group": "Port Group 편집", "Edit Project": "Project 편집", "Edit QoS Policy": "QoS Policy 편집", "Edit Quota": "Quota 편집", "Edit Role": "Role 편집", "Edit Router": "Router 편집", "Edit Rule": "Rule 편집", "Edit Share Metadata": "Share Metadata 편집", "Edit Subnet": "Subnet 편집", "Edit System Permission": "System Permission 편집", "Edit User": "User 편집", "Edit User Group": "User Group 편집", "Edit VPN": "VPN 편집", "Edit VPN EndPoint Groups": "VPN EndPoint Groups 편집", "Edit VPN IKE Policy": "VPN IKE Policy 편집", "Edit VPN IPsec Policy": "VPN IPsec Policy 편집", "Edit Volume Backup": "Volume Backup 편집", "Edit host aggregate": "Host Aggregate 편집", "Edit metadata": "Metada<PERSON> 편집", "Edit quota": "Quota 편집", "Edit rule": "", "Editing only changes the content of the file, not the file name.": "편집은 파일 이름이 아닌 파일의 내용만 변경합니다.", "Effective Mode": "", "Effective mode after configuration changes": "설정 변경 후 Effective Mode", "Egress": "", "Egress Policy": "", "Egress Policy ID": "", "Egress Policy Name": "", "Egypt": "", "Eject": "", "El Salvador": "", "Email": "", "Email Address": "", "Email for the zone. Used in SOA records for the zone.": "영역에 대한 이메일입니다. 영역의 SOA 레코드에 사용됩니다.", "Enable": "활성화", "Enable Admin State": "Admin State 활성화", "Enable Compute Host": "Compute Host 활성화", "Enable Compute Service": "Compute Service 활성화", "Enable DHCP": "DHCP 활성화", "Enable Domain": "Domain 활성화", "Enable Floating IP": "Floating IP 활성화", "Enable Health Check": "Health Check 활성화", "Enable Health Monitor": "Health Monitor 활성화", "Enable Load Balancer": "Load Balancer 활성화", "Enable Neutron Agent": "Neutron Agent 활성화", "Enable Project": "Project 활성화", "Enable QoS Policy": "QoS Policy 활성화", "Enable Registry": "Registr 활성화", "Enable SNAT": "SNAT 활성화", "Enable Service": "Service 활성화", "Enable User": "User 활성화", "Enable auto heal": "Auto Heal 활성화", "Enable auto remove": "Auto Remove 활성화", "Enable compute host": "compute host 활성화", "Enable interactive mode": "Interactive Mode 활성화", "Enabled": "활성됨", "Enabled Load Balancer for Master Nodes": "마스터 노드에 대해 로드밸런서 사용", "Enabled Network": "활성화된 네트워크", "Encapsulation Mode": "캡슐화 모드", "Encrypted": "암호화 됨", "Encryption": "암호화", "Encryption Algorithm": "암호화 알고리즘", "Encryption Info": "암호화 정보", "End Time": "종료 시간", "Endpoint Counts": "엔드포인트 개수", "Endpoints": "엔드포인트", "Engine ID": "엔진 ID", "Enroll": "등록", "Enter Maintenance Mode": "", "Enter an integer value between 1 and 65535.": "1과 65535 사이의 정수를 입력하세요.", "Enter query conditions to filter": "필터링할 쿼리 조건을 입력하세요.", "Entered: {length, plural, =1 {one character} other {# characters} }(maximum {maxCount} characters)": "입력됨: {length, plural, =1 {one character} other {# characters} }(최대 {maxCount} 글자수)", "Environment": "환경", "Environment Variable": "환경 변수", "Environment Variables": "환경 변수", "Ephemeral Disk (GiB)": "", "Equatorial Guinea": "", "Eritrea": "", "Error": "오류", "Error Deleting": "삭제 에러", "Error Extending": "확장 에러", "Error Restoring": "복구 에러", "Estonia": "", "Ether Type": "", "Ethiopia": "", "Event": "", "Event Time": "이벤트 시각", "Evictions": "", "Execute Command": "실행 명령어", "Execution Result": "실행 결과", "Existing Volume": "존재하는 볼륨", "Exit Policy": "종료 정책", "Exp: ": "", "Expand": "확장", "Expand Advanced Options": "고급 옵션 보기", "Expired Time": "만료 시간", "Expires At": "만료 예정", "Export Location": "위치 내보내기", "Export Locations": "위치 내보내기", "Exposed Ports": "노출된 포트", "Extend Root Volume": "root 볼륨 확장", "Extend Share": "Share 확장", "Extend Volume": "볼륨 확장", "Extend volume": "볼륨 확장", "Extending": "확장", "Extending Error": "확장 에러", "External": "외부", "External Fixed IP": "외부 고정 아이피", "External Fixed IPs": "외부 고정 아이피", "External Gateway": "외부 게이트웨이", "External IP": "외부 IP", "External IP(V4)": "외부 IP(V4)", "External IP(V6)": "외부 IP(V6)", "External Network": "외부 네트워크", "External Network ID/Name": "외부 네트워크 ID/이름", "External Network Info": "외부 네트워크 정보", "External Networks": "외부 네트워크", "External Port": "외부 포트", "External Port/Port Range": "외부 포트 범위", "Extra Infos": "추가 정보", "Extra Specs": "추가 사양", "FAKE": "", "FLAT": "", "Fail Rollback": "실패 롤백", "Failed": "실패함", "Failover Segment": "", "Falkland Islands (Malvinas)": "", "Faroe Islands": "", "Fault": "장애", "Fedora": "", "Fiji": "", "File": "파일", "File System Used Space": "파일 시스템 사용 공간", "File URL": "파일 URL", "Filename": "파일 이름", "Files: {names}": "파일: {names}", "Fill In The Parameters": "매개 변수 입력", "Fingerprint": "", "Finish Resize": "Resize 완료", "Finland": "", "Firewall": "", "Firewall Detail": "", "Firewall Policies": "", "Firewall Policy": "", "Firewall Port": "", "Firewall Rule": "", "Firewall Rules": "", "Firewalls": "", "Fixed IP": "고정 IP", "Fixed IP Address": "고정 IP 주소", "Fixed IPs": "고정 IP", "Fixed Network": "고정 네트워크", "Fixed Subnet": "고정 서브넷", "Flavor": "", "Flavor Detail": "Flavor 세부 정보", "Flavor Info": "Flavor 정보", "Flavor Name": "Flavor 이름", "Flavor families, used to configure the instance flavor classification": "", "Flavor of Master Nodes": "마스터 노드 Flavor", "Flavor of Nodes": "노드 Flavor", "Flavors": "", "Floating IP": "", "Floating IP Address": "Floating IP 주소", "Floating IP Enabled": "Floating IP 활성화됨", "Floating IPs": "Floating IP", "Floating Ip": "Floating IP", "Floating Ip Address": "Floating IP 주소", "Floating Ip Detail": "Floating IP 세부 정보", "Floating ip has already been associate, Please check Force release": "Floating ip가 이미 할당되었습니다. 강제 해제를 확인해 주세요.", "Folder Detail": "폴더 세부 정보", "Folder Name": "폴더 이름", "For GPU type, you need to install GPU drivers in the instance operating system.": "GPU 타입의 경우, 인스턴스 운영치제에 GPU 드라이버를 설치해야 합니다.", "For GRE networks, valid segmentation IDs are 1 to 4294967295": "GRE 네트워크의 경우, 유효한 segmentation ID는 1에서 4294967295까지 입니다.", "For VLAN networks, valid segmentation IDs are 1 to 4094": "VLAN 네트워크의 경우, 유효한 segmentation ID는 1에서 4094까지 입니다.", "For VXLAN networks, valid segmentation IDs are 1 to 16777215": "VXLAN 네트워크의 경우, 유효한 segmentation ID는 1에서 16777215까지 입니다.", "Forbidden": "금지된", "Forbidden Domain": "금지된 Domain", "Forbidden Project": "금지된 Project", "Forbidden User": "금지된 User", "Forbidden the domain will have a negative impact, all project and user in domain will be forbidden": "도메인이 금지되면 부정적인 영향을 미치고 도메인의 모든 프로젝트와 사용자가 제한됩니다.", "Force Delete": "강제 삭제", "Force Delete Container": "강제 컨테이너 삭제", "Force Delete Share Instance": "강제 공유 인스턴스 삭제", "Force release": "강제 해제", "Force shutdown must be checked!": "", "Forced Down": "강제 Down", "Forced Shutdown": "강제 Shutdown", "Forced shutdown may result in data loss or file system damage. You can also take the initiative to shut down and perform operations.": "강제 종료 시 데이터가 손실되거나 파일 시스템이 손상될 수 있습니다. 주도적으로 종료하고 작업을 수행할 수도 있습니다.", "Forgot your password?": "패스워드를 분실 하셨나요?", "Format": "", "Forward Slash ‘/’ is not allowed to be in a tag name": "슬래시 ‘/’는 태그 이름에 포함될 수 없습니다.", "France": "", "Free": "", "FreeBSD": "", "French Guiana": "", "French Polynesia": "", "Frequent login failure will cause the account to be temporarily locked, please operate after 5 minutes": "로그인 실패가 잦으면 계정이 일시적으로 잠깁니다. 5분 후에 시도하세요.", "From port": "", "Front End": "", "Frontend": "", "Full": "", "Full Backup": "", "GPU Count": "GPU 개수", "GPU Info": "GPU 정보", "GPU Model": "GPU 모델", "GPU Parameters": "GPU 파라미터", "GPU Type": "GPU 타입", "GPU model, used when configuring Compute Optimized Type with GPU": "", "GPU pass-through will load GPU devices directly to the instance for use. VGPU is a GPU virtualization solution. GPU resources will be segmented and distributed to multiple instances for shared use.": "GPU pass-through는 GPU 디바이스를 인스턴스에 직접 로드하여 사용합니다. VGPU는 GPU 가상화 솔루션입니다. GPU 리소스는 공유 사용을 위해 여러 인스턴스로 분할 및 분배됩니다.", "GRE": "", "Gabon": "", "Gambia": "", "Gateway": "", "Gateway IP": "", "Gateway Time-out (code: 504) ": "Gateway 타임아웃 (code: 504)", "Gateway ip {gateway_ip} conflicts with allocation pool {pool}": "Gateway ip {gateway_ip}가 할당 pool {pool}과 충돌됩니다", "General Purpose": "", "Generated Time": "", "Georgia": "", "Germany": "", "Get OpenRC file": "OpenRC 파일 받기", "Get Token": "토큰 받기", "Get {name} detail error.": "", "Get {name} error.": "", "Ghana": "", "Gibraltar": "", "Given IP": "", "Glance": "", "Glance Image": "", "Global Setting": "전역 세팅", "GlusterFS": "", "Grant Databases Access": "데이터베이스 접근 허용", "Greece": "", "Greenland": "", "Grenada": "", "Guadeloupe": "", "Guam": "", "Guatemala": "", "Guinea": "", "Guinea Bissau": "", "Guyana": "", "HDFS": "", "HEALTHY": "", "HTTP Proxy": "", "HTTP Version not supported (code: 505) ": "HTTP 버전이 지원되지 않습니다. (code: 505} ", "HTTPS Proxy": "", "Haiti": "", "Hard Reboot": "강제 재부팅", "Hard Rebooting": "강제 재부팅", "Hash": "", "Health Check CMD": "", "Health Check Interval": "Health Check 간격", "Health Check Retries": "Health Check 재시도", "Health Check Timeout": "Health Check 타임아웃", "Health Checking Log": "Health Check 로그", "Health Inspection": "건강 검사", "Health Monitor": "Health Monitor", "Health Monitor Delay": "Health Monitor 지연시간", "Health Monitor Detail": "Health Monitor 세부 정보", "Health Monitor Max Retries": "Health Monitor 최대 재시도 횟수", "Health Monitor Name": "Health Monitor 이름", "Health Monitor Timeout": "Health Monitor 타임아웃", "Health Monitor Type": "Health Monitor 타입", "Health Status": "", "HealthMonitor Type": "HealthMonitor 타입", "Healthy": "정상", "Heartbeat Timestamp": "Heartbeat 타임스템프", "Hello, {name}": "{name}, 안녕하세요", "Heterogeneous Computing": "", "Hidden": "숨겨짐", "Hide Advanced Options": "고급 옵션 숨기기", "Hide Default Firewalls": "", "Hide Default Policies": "", "Hide Default Rules": "", "High Clock Speed": "", "Home": "홈페이지", "Home page": "홈페이지", "Honduras": "", "Hong Kong": "", "Host": "", "Host Aggregate": "Host 집합", "Host Aggregates": "Host 집합", "Host Average Network IO": "Host 평균 네트워크 IO", "Host CPU Usage": "Host CPU 사용률", "Host Detail": "Host 상세 정보", "Host Disk Average IOPS": "Host 디스크 평균 IOPS", "Host Memory Usage": "HOST 메모리 사용률", "Host Name": "", "Host Routes": "", "Host Routes Format Error(e.g. *************/24,***********)": "Host Routes 형식 에러(e.g. *************/24,***********)", "Host Routes Format Error(e.g. ::0a38:01fe/24,::0a38:01fe)": "Host Routes 형식 에러(e.g. ::0a38:01fe/24,::0a38:01fe)", "Hostname": "", "Hosts": "", "Hosts Detail": "Hosts 상세 정보", "Hungary": "", "Hypervisor Detail": "Hypervisor 상세 정보", "Hypervisors": "", "ICMP": "", "ICMP Code": "ICMP 코드", "ICMP Type": "ICMP 타입", "ICMP Type/ICMP Code": "ICMP 타입/ICMP 코드", "ID": "", "ID/Floating IP": "", "ID/Name": "ID/이름", "IGMP": "", "IKE Policies": "IKE 정책", "IKE Policy": "IKE 정책", "IKE Version": "IKE 버전", "IP": "", "IP Address": "IP 주소", "IP Distribution Mode": "", "IP Protocol": "IP 프로토콜", "IP Usage": "IP 사용률", "IP Version": "IP 버전", "IP address allocation polls, one enter per line(e.g. ***********,***********00)": "IP 주소 할당 풀, 한 줄에 하나씩 입력하세요. (e.g. ***********,***********00)", "IP address allocation polls, one enter per line(e.g. {ip})": "IP 주소 할당 풀, 한 줄에 하나씩 입력하세요.(e.g. {ip})", "IPMI Address": "IPMI 주소", "IPMI Bridge": "", "IPMI Password": "IPMI 패스워드", "IPMI Port": "IPMI 포트", "IPMI Privilege Level": "IPMI 권한 레벨", "IPMI Protocol Version": "IPMI 프로토콜 버전", "IPMI Username": "IPMI 계정명", "IPMITool": "", "IPXE": "", "IPsec Policies": "IPsec 정책", "IPsec Policy": "IPsec 정책", "IPsec Site Connection": "", "IPsec Site Connections": "", "IPsec site connection Detail": "", "IPv4": "", "IPv4 Address": "IPv4 주소", "IPv6": "", "IPv6 Address": "IPv6 주소", "IPv6 Address Record": "IPv6 주소 레코드", "IPv6-Encap": "", "IPv6-Frag": "", "IPv6-ICMP": "", "IPv6-NoNxt": "", "IPv6-Opts": "", "IPv6-Route": "", "ISO - Optical disc image format": "ISO - 광디스크 이미지 포멧", "Iceland": "", "Id": "", "Identifier of the physical port on the switch to which node’s port is connected to": "노드의 포트가 연결된 스위치의 물리 포트 식별자", "Identity": "", "If \"Enable\" fails to roll back, the resource will be deleted after the creation fails; if \"Disable\" fails to roll back, the resource will be retained after the creation fails.": "만약 \"사용\"이 롤백에 실패하면 리소스는 생성 실패 후 삭제되고 \"사용 안 함\"이 롤백에 실패하면 리소스는 생성 실패 후에도 보존됩니다.", "If OS is Linux, system will reset root password, if OS is Windows, system will reset Administrator password.": "OS가 Linux인 경우 시스템은 루트 암호를 재설정하고, OS가 Windows인 경우 시스템은 관리자 암호를 재설정합니다.", "If an instance is using this flavor, deleting it will cause the instance's flavor data to be missing. Are you sure to delete {name}?": "인스턴스가 해당 flavor를 사용하는 경우, 삭제하면 인스턴스의 flavor 정보가 유실됩니다. {name}을(를) 삭제하시겠습니까?", "If checked, the network will be enable.": "이 옵션을 체크하면 네트워크가 활성화됩니다.", "If exposed port is specified, this parameter will be ignored.": "노출된 포트가 지정된 경우 이 매개 변수는 무시됩니다.", "If it is an SNI type certificate, a domain name needs to be specified": "SNI 유형의 인증서인 경우 도메인 이름을 지정해야 합니다.", "If it’s not set, the value of this in the template will be used.": "설정되지 않은 경우 템플릿의 값이 사용됩니다.", "If no gateway is specified, the first IP address will be defaulted.": "게이트웨이를 지정하지 않으면 첫 번째 IP 주소가 기본값이 됩니다.", "If not provided, the roles assigned to the application credential will be the same as the roles in the current token.": "제공되지 않은 경우 응용프로그램 자격 증명에 할당된 역할은 현재 토큰의 역할과 동일합니다.", "If nova-compute on the host is disabled, it will be forbidden to be selected as the target host.": "호스트에서 nova-compute가 비활성화되어 있으면 대상 호스트로 선택하는 것이 금지됩니다.", "If set then all tenants will be able to see this share.": "설정하면 모든 tenant가 이 share를 조회할 수 있습니다.", "If the capacity of the disk is large, the type modify operation may take several hours. Please be cautious.": "디스크 용량이 큰 경우 유형 수정 작업이 몇 시간 걸릴 수 있습니다. 신중하세요.", "If the listener has an SNI certificate installed, it cannot be removed. Please delete the listener or replace the SNI certificate": "listener에 SNI 인증서가 설치되어 있으면 삭제할 수 없습니다. listener를 삭제하거나 SNI 인증서를 교체하세요.", "If the root disk has a snapshot, it will affect the deletion of the original disk during reconstruction or the recovery of the instance snapshot.": "", "If the value is set to 0, it means unlimited": "값이 0으로 설정되어 있으면 무제한임을 의미합니다.", "If the volume associated with the snapshot has changed the volume type, please modify this option manually; if the volume associated with the snapshot keeps the volume type unchanged, please ignore this option. (no need to change).": "스냅샷과 연결된 볼륨의 볼륨 유형이 변경된 경우, 이 옵션을 수동으로 수정하세요. 만약 스냅샷과 연결된 볼륨의 볼륨 유형이 변경되지 않는 경우 이 옵션을 무시하세요. (변경할 필요 없음).", "If this parameter is selected, resumable uploads are supported, but the total upload time may be increased by a small amount. Images smaller than 200M are not recommended.": "이 매개 변수를 선택하면 재개 가능한 업로드가 지원되지만, 총 업로드 시간은 소량 증가할 수 있습니다. 200M보다 작은 이미지는 권장되지 않습니다.", "If this parameter is specified, Zun will create a security group with a set of rules to open the ports that should be exposed, and associate the security group to the container.": "이 매개 변수가 지정된 경우 Zun은 노출되어야 하는 포트를 허용하는 rule을 포함한 Security Group을 생성하고, 해당 Security Group을 컨테이너에 연결합니다.", "If you are not authorized to access any project, or if the project you are involved in has been deleted or disabled, contact the platform administrator to reassign the project": "프로젝트에 엑세스할 권한이 없거나 기존의 프로젝트가 삭제 또는 비활성화된 경우, 플랫폼 관리자에게 프로젝트 재할당을 문의하세요.", "If you are not sure which authentication method to use, please contact your administrator.": "사용할 인증 방법을 잘 모르는 경우 관리자에게 문의하십시오.", "If you choose a port which subnet is different from the subnet of LB, please ensure connectivity between the two.": "LB의 Subnet과 다른 Subnet의 포트를 선택할 경우, 두 서브넷 간의 연결 여부를 확인하세요.", "If you do not fill in parameters such as cpus, memory_mb, local_gb, cpu_arch, etc., you can automatically inject the configuration and Mac address of the physical machine by performing the \"Auto Inspect\" operation.": "CPU, memory, local_gb, cpu_arch 등의 매개 변수를 입력하지 않으면 \"자동 검사\" 작업을 수행하여 물리적 시스템의 구성 및 MAC 주소를 자동으로 넣을 수 있습니다.", "If you still want to keep the disk data, it is recommended that you create a backup for the disk before deleting.": "디스크의 데이터를 보존하려면 디스크를 제거하기 전에 디스크를 백업하는 것이 좋습니다.", "Illegal JSON scheme": "", "Image": "", "Image & OS": "", "Image Backup": "Image 백업", "Image Detail": "Image 상세 정보", "Image Driver": "Image 드라이버", "Image Info": "Image 정보", "Image Name": "Image 이름", "Image Pending Upload": "", "Image Pulling": "Image 다운로드", "Image Size": "Image 크기", "Image Snapshot Pending": "", "Image Uploading": "이미지 업로드", "Images": "이미지", "Immediate effect": "즉각적인 효과", "Immediately delete": "즉시 삭제", "Implied Roles": "내재된 역할", "Import Keypair": "키 페어 가져오기", "Import Metadata": "메타데이터 가져오기", "Import metadata": "메타데이터 가져오기", "Importing": "가져오는 중", "In Cluster": "클러스터 내", "In Use": "사용 중", "In general, administrator for Windows, root for Linux, please fill by image uploading.": "일반적으로 Windows의 경우 administrator, Linux의 경우 root로 이미지 업로드시 지정합니다.", "In order to avoid data loss, the instance will shut down and interrupt your business. Please confirm carefully.": "데이터 손실을 방지하기 위해 인스턴스는 종료됩니다.", "In the last 30 days": "지난 30일", "In the last 7 days": "지난 7일", "In the last hour": "지난 1시간", "In-use": "사용 중", "Inactive": "비활성", "Increment Backup": "증분 백업", "Incremental": "증분", "Incremental Backup": "증분 백업", "India": "", "Indicates whether this VPN can only respond to connections or both respond to and initiate connections.": "VPN이 연결만 가능한지, 연결에 모두 응답과 초기화가 모두 가능한지를 나타냅니다.", "Indonesia": "", "Infinity": "무한대", "Info": "정보", "Ingress": "인그레스", "Ingress Policy": "", "Ingress Policy ID": "", "Ingress Policy Name": "", "Init Complete": "초기화 완료", "Init Failed": "초기화 실패", "Init In Progress": "초기화 진행 중", "Initial Admin User": "초기 관리자", "Initial Databases": "초기 데이터베이스", "Initial Volume Size": "초기 볼륨 크기", "Initialize Databases": "데이터베이스 초기화", "Initiator Mode": "초기 모드", "Input destination port or port range (example: 80 or 80:160)": "입력 대상 포트 또는 포트 범위(예: 80 또는 80:160)", "Input external port or port range (example: 80 or 80:160)": "외부 포트 또는 포트 범위 입력(예: 80 또는 80:160)", "Input internal port or port range (example: 80 or 80:160)": "입력 내부 포트 또는 포트 범위(예: 80 또는 80:160)", "Input source port or port range (example: 80 or 80:160)": "입력 소스 포트 또는 포트 범위(예: 80 또는 80:160)", "Insecure Registry": "암호화되지 않은 레지스트리", "Insert": "", "Insert After": "", "Insert Before": "", "Insert Rule": "", "Inspect Failed": "검사 실패", "Inspecting": "검사 중", "Instance": "인스턴스", "Instance \"{ name }\" has already been locked.": "인스턴스 \"{ name }\"이(가) 이미 잠겼습니다.", "Instance \"{ name }\" is ironic, can not soft reboot it.": "인스턴스 \"{ name }\"은(는) Ironic 입니다. 소프트 재부팅할 수 없습니다.", "Instance \"{ name }\" is locked, can not delete it.": "인스턴스 \"{ name }\"이(가) 잠겼습니다. 삭제할 수 없습니다.", "Instance \"{ name }\" is locked, can not pause it.": "인스턴스 \"{ name }\"이(가) 잠겼습니다. 일시 중지할 수 없습니다.", "Instance \"{ name }\" is locked, can not reboot it.": "인스턴스 \"{ name }\"이(가) 잠겼습니다. 재부팅할 수 없습니다.", "Instance \"{ name }\" is locked, can not resume it.": "인스턴스 \"{ name }\"이(가) 잠겼습니다. 다시 시작할 수 없습니다.", "Instance \"{ name }\" is locked, can not soft reboot it.": "인스턴스 \"{ name }\"이(가) 잠겼습니다. 소프트 재부팅할 수 없습니다.", "Instance \"{ name }\" is locked, can not start it.": " 인스턴스 \"{ name }\"이(가) 잠겨 있어 시작할 수 없습니다.", "Instance \"{ name }\" is locked, can not stop it.": "인스턴스 \"{ name }\"이(가) 잠겼습니다. 중지할 수 없습니다.", "Instance \"{ name }\" is locked, can not suspend it.": "인스턴스 \"{ name }\"이(가) 잠겼습니다. 일시 중지할 수 없습니다.", "Instance \"{ name }\" is locked, can not unpause it.": "인스턴스 \"{ name }\"이(가) 잠겼습니다. 일시 중지를 해제할 수 없습니다.", "Instance \"{ name }\" is not locked, can not unlock it.": "인스턴스 \"{ name }\"이(가) 잠기지 않았으므로 잠금 해제할 수 없습니다.", "Instance \"{ name }\" status is not active, can not soft reboot it.": "인스턴스 \"{ name }\" 상태가 활성이 아니므로 소프트 재부팅할 수 없습니다.", "Instance \"{ name }\" status is not in active or shutoff, can not reboot it.": "인스턴스 \"{ name }\" 상태가 활성 상태가 아니거나 종료되었습니다. 재부팅할 수 없습니다.", "Instance \"{ name }\" status is not in active or suspended, can not stop it.": "인스턴스 \"{ name }\" 상태가 활성이 아니거나 일시 중단되었습니다. 중지할 수 없습니다.", "Instance \"{ name }\" status is not in active, can not pause it.": "인스턴스 \"{ name }\" 상태가 활성 상태가 아니므로 일시 중지할 수 없습니다.", "Instance \"{ name }\" status is not in active, can not suspend it.": "인스턴스 \"{ name }\" 상태가 활성이 아니므로 일시 중지할 수 없습니다.", "Instance \"{ name }\" status is not in paused, can not unpause it.": "인스턴스 \"{ name }\" 상태가 일시 중지되지 않았으므로 일시 중지를 해제할 수 없습니다.", "Instance \"{ name }\" status is not in suspended, can not resume it.": "인스턴스 \"{ name }\" 상태가 일시 중단되지 않았으므로 재개할 수 없습니다.", "Instance \"{ name }\" status is not shutoff, can not start it.": "인스턴스 \"{ 이름 }\" 상태가 종료되지 않았으므로 시작할 수 없습니다.", "Instance Addr": "인스턴스 주소", "Instance Architecture": "인스턴스 아키텍처", "Instance Console Log": "인스턴스 콘솔 로그", "Instance Detail": "인스턴스 세부 정보", "Instance ID": "인스턴스 ID", "Instance IP": "인스턴스 IP", "Instance Info": "인스턴스 정보", "Instance Port": "", "Instance Related": "인스턴스 관련", "Instance Snapshot": "인스턴스 스냅샷", "Instance Snapshot Detail": "인스턴스 스냅샷 세부 정보", "Instance Snapshot Name": "인스턴스 스냅샷 이름", "Instance Snapshots": "인스턴스 스냅샷", "Instance Status": "인스턴스 상태", "Instance UUID": "", "Instance-HA": "", "Instances": "인스턴스", "Instances \"{ name }\" are locked, can not delete them.": "인스턴스 \"{ name }\"이(가) 잠겼습니다. 삭제할 수 없습니다.", "Insufficient {name} quota to create resources (left { quota }, input { input }).": "리소스를 생성하기 위한 \"{ name }\" 할당량이 부족합니다(left { quota }, input { input }).", "Interface Info": "인터페이스 정보", "Interface Name:": "인터페이스 이름", "Interface for vendor-specific functionality on this node": "이 노드의 공급업체별 기능에 대한 인터페이스", "Interface used for attaching and detaching volumes on this node": "이 노드에서 볼륨을 연결 및 분리하는 데 사용되는 인터페이스", "Interface used for configuring RAID on this node": "이 노드에서 RAID를 구성하는 데 사용되는 인터페이스", "Interfaces": "인터페이스", "Internal Ip Address": "내부 IP 주소", "Internal Network Bandwidth (Gbps)": "내부 네트워크 대역폭(Gbps)", "Internal Port": "내부 포트", "Internal Port/Port Range": "내부 포트/포트 범위", "Internal Server Error (code: 500) ": "내부 서버 오류(코드: 500)", "Invalid": "유효하지 않은", "Invalid CIDR.": "잘못된 CIDR.", "Invalid IP Address": "잘못된 IP 주소", "Invalid IP Address and Port": "잘못된 IP 주소 및 포트", "Invalid Mac Address. Please Use \":\" as separator.": "잘못된 Mac 주소입니다. \":\"를 구분 기호로 사용하십시오.", "Invalid Tag Value: {tag}": "잘못된 태그 값: {tag}", "Invalid combination": "잘못된 조합", "Invalid: ": "유효하지 않은: ", "Invalid: Allocation Pools Format Error(e.g. ***********,***********00) and start ip should be less than end ip": "잘못됨: 할당 풀 형식 오류(예: ***********,***********00) 및 시작 IP는 종료 IP보다 작아야 합니다", "Invalid: Allocation Pools Format Error(e.g. fd00:dead:beef:58::9,fd00:dead:beef:58::13) and start ip should be less than end ip": "잘못됨: 할당 풀 형식 오류(예: fd00:dead:beef:58::9,fd00:dead:beef:58::13) 및 시작 IP는 종료 IP보다 작아야 합니다.", "Invalid: CIDR Format Error(e.g. **********/24)": "잘못됨: CIDR 형식 오류(예: **********/24)", "Invalid: DNS Format Error(e.g. 1001:1001::)": "잘못됨: DNS 형식 오류(예: 1001:1001::)", "Invalid: DNS Format Error(e.g. ***************)": "잘못됨: DNS 형식 오류(예: ***************)", "Invalid: Domain name cannot be duplicated": "잘못됨: 도메인 이름은 중복될 수 없습니다.", "Invalid: Password must be the same with confirm password.": "잘못됨: 암호는 암호 확인과 동일해야 합니다.", "Invalid: Please input a valid ip": "잘못됨: 유효한 IP를 입력하십시오", "Invalid: Please input a valid ipv4": "잘못됨: 유효한 ipv4를 입력하십시오", "Invalid: Please input a valid ipv6.": "잘못됨: 유효한 ipv6을 입력하십시오.", "Invalid: Project name can not be chinese": "잘못됨: 프로젝트 이름은 중국어일 수 없습니다.", "Invalid: Project names in the domain can not be repeated": "유효하지 않음: 도메인의 프로젝트 이름은 반복될 수 없습니다.", "Invalid: Quota value(s) cannot be less than the current usage value(s): { used } used.": "잘못됨: 할당량 값은 현재 사용 값보다 작을 수 없습니다: { used } 사용됨.", "Invalid: User Group names in the domain can not be repeated": "유효하지 않음: 도메인의 사용자 그룹 이름은 반복될 수 없습니다.", "Invalid: User names in the domain can not be repeated": "유효하지 않음: 도메인의 사용자 이름은 반복될 수 없습니다.", "Ip Address": "IP 주소", "Iran (Islamic Republic of)": "", "Iraq": "", "Ireland": "", "Ironic Instance": "Ironic 인스턴스", "Ironic Instance Name": "Ironic 인스턴스 이름", "Is Current Project": "현재 프로젝트", "Is Public": "공용", "Is admin only": "관리자 전용", "Is associate to floating ip: ": "유동 IP에 연결됨: ", "Is external network port": "외부 네트워크 포트", "Isolate": "격리", "Isolate(No multithreading)": "격리(멀티스레딩 없음)", "Israel": "", "It is IPv6 type.": "IPv6 유형입니다.", "It is recommended that the { instanceType } instance simultaneously set large page memory to large. { instanceType } instances also require faster memory addressing capabilities.": "{ instanceType } 인스턴스는 대용량 페이지 메모리를 동시에 대용량으로 설정하는 것이 좋습니다. { instanceType } 인스턴스도 더 빠른 메모리 주소 지정 기능이 필요합니다.", "It is recommended that you perform this cloning operation on a disk without any reading/writing": "읽기/쓰기 없이 디스크에서 이 복제 작업을 수행하는 것이 좋습니다.", "It is recommended that you use the private network address 10.0.0.0/8, **********/12, ***********/16": "사설 네트워크 주소 10.0.0.0/8, **********/12, ***********/16을 사용하는 것이 좋습니다", "It is recommended that { instanceType } instance simultaneously set NUMA affinity policy for PCIE device to force or priority matching. This configuration can further improve PCIE computing performance.": "{ instanceType } 인스턴스가 동시에 PCIE 장치에 대한 NUMA 선호도 정책을 강제로 설정하거나 일치 우선 순위를 지정하는 것이 좋습니다. 이 구성은 PCIE 컴퓨팅 성능을 더욱 향상시킬 수 있습니다.", "It is recommended to install and use this agent. The instance created with this image can be used to modify the password (qemu_guest_agent needs to be installed when creating the image).": "이 에이전트를 설치하여 사용하는 것이 좋습니다. 이 이미지로 생성된 인스턴스는 비밀번호를 수정하는 데 사용할 수 있습니다(이미지 생성 시 qemu_guest_agent를 설치해야 함).", "It is recommended to refer to the following description format, otherwise it may not be effective": "", "It is recommended to set CPU binding strategy as binding on { instanceType } instance. This configuration further improves the performance of the instance CPU.": "CPU 바인딩 전략을 { instanceType } 인스턴스 바인딩으로 설정하는 것이 좋습니다. 이 구성은 인스턴스 CPU의 성능을 더욱 향상시킵니다.", "It is recommended to set the CPU thread binding policy as thread binding in { instanceType } instance, which can further improve the CPU performance of instance.": "CPU 스레드 바인딩 정책을 { instanceType } 인스턴스의 스레드 바인딩으로 설정하는 것이 좋습니다. 이렇게 하면 인스턴스의 CPU 성능을 더욱 향상시킬 수 있습니다.", "It is suggested to use the marked AZ directly, too much AZ will lead to the fragmentation of available resources": "표시된 AZ를 직접 사용하는 것이 좋습니다. AZ가 너무 많으면 사용 가능한 리소스가 조각화될 수 있습니다.", "It is unreachable for all floating ips.": "모든 부동 IP에 대해 연결할 수 없습니다.", "It is unreachable for this floating ip.": "이 부동 IP에 연결할 수 없습니다.", "Italy": "", "Items in Cache": "캐시 항목", "Jamaica": "", "Japan": "", "Jordan": "", "Jump to Console": "콘솔로 이동", "Kampuchea": "", "Kazakhstan": "", "Kenya": "", "Kernel ID": "커널 ID", "Kernel Image": "커널 이미지", "Kernel Version": "커널 버전", "Key": "키", "Key Pair": "키 페어", "Key Pairs": "키 페어", "Key Size (bits)": "키 크기(비트)", "Keypair": "키 페어", "Keypair Detail": "키 페어 세부 정보", "Keypair Info": "키 쌍 정보", "Keystone Credentials": "Keystone 자격 증명", "Keystone token is expired.": "Keystone 토큰이 만료되었습니다.", "Kill": "제거", "Kill Container": "컨테이너 제거", "Kill Signal": "제거 신호 ", "Killed": "제거 ", "Kubernetes": "쿠버네티스", "Kuwait": "", "Kyrgyzstan": "", "LB Algorithm": "LB 알고리즘", "LEAST_CONNECTIONS": "LEAST_CONNECTIONS", "Labels": "라벨", "Lao People's Democratic Republic": "", "Large": "크기가 큰", "Large Screen": "큰 화면", "Large(Optimal performance)": "대형(최적 성능)", "Last 2 Weeks": "지난 2주", "Last 24H Status": "마지막 24시간 상태", "Last 7 Days": "지난 7일", "Last Day": "마지막 날", "Last Hour": "지난 시간", "Last Updated": "마지막 업데이트", "Last week alarm trend": "지난 주 알람 추세", "Latvia": "", "Leave Maintenance Mode": "유지 관리 모드 나가기", "Lebanon": "", "Left": "왼쪽", "Lesotho": "", "Liberia": "", "Libyan Arab Jamahiriya": "", "Liechtenstein": "", "Lifetime": "평생", "Lifetime Value": "평생 가치", "Listener": "리스너", "Listener Connection Limit": "리스너 연결 제한", "Listener Description": "리스너 설명", "Listener Detail": "리스너 세부 정보", "Listener Name": "리스너 이름", "Listener Number": "리스너 번호", "Listener Protocol": "리스너 프로토콜", "Listener Protocol Port": "리스너 프로토콜 포트", "Listeners": "리스너", "Lithuania": "", "Live Migrate": "라이브 마이그레이션", "Live Migration At Destination": "대상에서 라이브 마이그레이션", "Load Balancer": "로드 밸런서", "Load Balancer Detail": "로드 밸런서 세부 정보", "Load Balancer Name": "로드 밸런서 이름", "Load Balancers": "로드 밸런서", "Load Template from a file": "파일에서 템플릿 불러오기", "Load from local files": "로컬 파일에서 불러오기", "LoadBalancers Instances": "LoadBalancers 인스턴스", "Local": "로컬", "Local Endpoint Group": "로컬 엔드포인트 그룹", "Local Endpoint Group ID": "로컬 엔드포인트 그룹 ID", "Local Link Connection": "로컬 링크 연결", "Local Network": "로컬 네트워크", "Local SSD": "로컬 SSD", "Local Subnet": "로컬 서브넷", "Locality": "", "Lock": "잠금", "Lock Instance": "인스턴스 잠금", "Lock Status": "잠금 상태", "Lock instance will lock the operations that have a direct impact on the operation of the instance, such as: shutdown, restart, delete, the mounting and unmounting of volume, etc. It does not involve the capacity expansion and change type of volume.": "인스턴스 잠금은 종료, 재시작, 삭제, 볼륨 마운트 및 마운트 해제 등과 같이 인스턴스 작동에 직접적인 영향을 미치는 작업이 불가능합니다. 용량 확장 및 볼륨 유형 변경을 포함하지 않습니다. ", "Locked": "잠김", "Log": "로그", "Log Length": "로그 길이", "Log in": "로그인", "Login Name": "로그인 이름", "Login Password": "로그인 비밀번호", "Login Type": "로그인 입력", "Logs": "로그", "Luxembourg": "", "MAC Address": "MAC 주소", "MAC Learning State": "MAC 학습 상태", "MAPRFS": "MAPRFS", "MEM %": "메모리 %", "MEM LIMIT (MiB)": "메모리 제한(MiB)", "MEM USAGE (MiB)": "메모리 사용량(MiB)", "MTU": "MTU", "Mac Address": "Mac 주소", "MacVTap": "MacVTap", "Macau": "", "Madagascar": "", "Mail Exchange Record": "MX 레코드", "Maintained": "", "Maintenance": "", "Malawi": "", "Malaysia": "", "Maldives": "", "Mali": "", "Malta": "", "Manage Access": "액세스 관리", "Manage Access Rule": "액세스 규칙 관리", "Manage Error": "오류 관리", "Manage Host": "호스트 관리", "Manage Metadata": "메타데이터 관리", "Manage Ports": "", "Manage QoS Spec": "QOS 스펙 관리", "Manage Resource Types": "리소스 타입 관리", "Manage Security Group": "보안 그룹 관리", "Manage Starting": "시작 관리", "Manage State": "상태 관리", "Manage User": "사용자 관리", "Manage User Group": "사용자 그룹 관리", "Manage host": "호스트 관리", "Manage user": "사용자 관리", "Manage user group": "사용자 그룹 관리", "Manageable": "관리 가능", "Management": "관리", "Management Reason": "관리 영역", "Mandatory for secondary zones. The servers to slave from to get DNS information.": "보조 영역의 경우 필수입니다. DNS 정보를 얻기 위해 슬레이브할 서버입니다.", "Manu": "", "Manual input": "수동 입력", "Manually Assigned Address": "수동으로 할당된 주소", "Manually Specify": "수동 지정", "Marshall Islands": "", "Martinique": "", "Master Node Addresses": "마스터 노드 주소", "Master Node Flavor": "마스터 노드 Flavor", "Master Node LB Enabled": "마스터 노드 LB 사용", "Masters": "마스터", "Mauritania": "", "Mauritius": "", "Max Avail": "최대 사용 가능", "Max BandWidth": "최대 대역폭", "Max Burst": "최대 버스트", "Max Retries": "최대 재시도 횟수", "Max Retry": "최대 재시도", "Max connect": "최대 연결", "Maximum interval time for each health check response": "각 상태 확인 응답의 최대 간격 시간", "Maximum time to allow one check to run in seconds": "1회 검사를 실행할 수 있는 최대 시간(초)", "Mayotte": "", "Mem": "메모리", "Member Count": "멤버 수", "Member Detail": "멤버 세부 정보", "Member Num": "멤버 수", "Members": "멤버", "Members of Each Group": "각 그룹의 구성원", "Members of Each Server Group": "각 서버 그룹의 구성원", "Memory": "메모리", "Memory (GiB)": "메모리(GiB)", "Memory (MiB)": "메모리(MiB)", "Memory Optimized": "메모리 최적화", "Memory Page": "메모리 페이지", "Memory Page Size": "메모리 페이지 크기", "Memory Usage": "메모리 사용량", "Memory Usage(%)": "메모리 사용(%)", "Memory Usages (GiB)": "메모리 사용량(GiB)", "Mesos": "메소스", "Message": "메시지", "Message Details": "메시지 세부 정보", "Message Queue Service": "", "Metadata": "메타데이터", "Metadata Definitions": "메타데이터 정의", "Metadata Detail": "메타데이터 세부 정보", "Mexico": "", "Micronesia": "", "Migrate": "이동", "Migrate Volume": "볼륨 마이그레이션", "Migrate volume": "볼륨 마이그레이션", "Migrating": "", "Migrating To": "", "Min Memory": "최소 메모리", "Min Memory (GiB)": "최소 메모리 (GiB)", "Min System Disk": "최소 시스템 디스크", "Min System Disk (GiB)": "최소 시스템 디스크 (GiB)", "Min size": "최소 용량", "Min. Disk": "최소 디스크 크기", "Min. RAM": "최소 메모리", "Minimum value is 68 for IPv4, and 1280 for IPv6.": "", "Miscellaneous": "", "Missing IP Address": "", "Missing Port": "포트 번호가 없습니다", "Missing Subnet": "", "Missing Weight": "", "Modification Times": "", "Modify Instance Tags": "인스턴스 태그 수정", "Modify Project Tags": "프로젝트 태그 수정", "Modify QoS": "QoS 수정", "Moldova": "", "Monaco": "", "Mongolia": "", "Monitor Center": "", "Monitor Overview": "", "Montenegro": "", "Montserrat": "", "More": "더", "More Actions": "추가 작업", "More than one label is required, such as: \"example.org.\"": "\"example.org.\"와 같이 두 개 이상의 라벨이 필요합니다.", "Morocco": "", "Mount ISO": "", "Mount snapshot support": "", "Mozambique": "", "Multiple filter tags are separated by enter": "여러 필터 탭을 엔터 키를 통해 구분합니다", "My Role": "", "MySQL Actions": "", "Myanmar": "", "N/A": "", "NET I/O(B)": "", "NFS": "", "NOOP": "", "NUMA Node": "", "NUMA Node Count": "", "NUMA Nodes": "", "Name": "이름", "Name Server": "", "Name can not be duplicated": "", "Name or ID og the container image": "", "Namespace": "", "Namibia": "", "Nauru": "", "Nepal": "", "Netherlands": "", "Netherlands Antilles": "", "Network": "네트워크", "Network Attaching": "", "Network Config": "네트워크 구성", "Network Detaching": "", "Network Detail": "네트워크 세부사항", "Network Driver": "네트워크 드라이버", "Network Dropped Packets": "", "Network Errors": "", "Network ID": "네트워크 ID", "Network ID/Name": "", "Network Info": "네트워크 정보", "Network Interface": "네트워크 인터페이스", "Network Line": "", "Network Name": "네트워크 이름", "Network Service": "네트워크 서비스", "Network Setting": "", "Network Traffic": "", "Network Type": "네트워크 타입", "Network topology page": "", "Networking": "", "Networking *": "", "Networks": "네트워크", "Neutron Agent Detail": "Neutron 에이전트 세부 정보", "Neutron Agents": "Neutron 에이전트", "Neutron Net": "", "Neutron Service": "", "Neutron Subnet": "", "New": "새", "New Availability Zone": "", "New Caledonia": "", "New Status": "새로 운 상태", "New Tag": "새 태그", "New Volume": "새 볼륨", "New Zealand": "", "Next": "다음", "Next Hop": "다음 홉", "Nicaragua": "", "Niger": "", "Nigeria": "", "No": "아니오", "No - Do not create a new system disk": "아니요 - 새 시스템 디스크를 생성하지 않습니다", "No Console": "", "No Logs...": "", "No Monitor": "", "No Outputs": "", "No Proxy": "", "No Raid": "", "No State": "", "No Task": "", "No Vender": "", "No default pool set": "기본 풀이 설정되지 않았습니다.", "Node": "노드", "Node Addresses": "노드 주소", "Node Driver": "노드 드라이버", "Node Flavor": "노드 Flavor", "Node ID/Name": "노드 ID/이름", "Node Info": "노드 정보", "Node Name": "노드 이름", "Node Spec": "", "Nodes": "노드", "Nodes To Remove": "", "Norfolk Island": "", "Normal": "표준", "North Korea": "", "Northern Mariana Islands": "", "Norway": "", "Not Implemented (code: 501) ": "", "Not Open": "", "Not dealt with for the time being": "당분간 처리 안해요.", "Not deleted with the instance": "", "Not locked": "", "Not select": "", "Not yet bound": "", "Not yet selected": "", "Note that when using a share type with the driver_handles_share_servers extra spec as False, you should not provide a share network.": "", "Note: Are you sure you need to modify the volume type?": "", "Note: Please consider the container name carefully since it couldn't be changed after created.": "", "Note: The security group you use will act on all virtual adapters of the instance.": "", "Notification Detail": "", "Notifications": "", "Nova Service": "Nova 서비스", "Number of GPU": "", "Number of IPs used by all projects": "", "Number of Master Nodes": "", "Number of Nodes": "", "Number of Ports": "", "Number of Usb Controller": "", "OK": "", "OS": "", "OS Admin": "", "OS Disk": "OS 디스크", "OS Type": "OS 타입", "OS Version": "OS 버전", "OSDs": "", "OSPF": "", "Object": "", "Object Count": "", "Object Count ": "", "Object ID": "", "Object ID/Name": "", "Object Name": "", "Object Storage": "", "Object Type": "", "Off": "", "Offline": "", "Oman": "", "On": "", "On Maintenance": "", "On failure": "", "One entry per line(e.g. ***************)": "", "One entry per line(e.g. {ip})": "", "One-way authentication": "", "Online": "", "Online Resize": "", "Only a MAC address or an OpenFlow based datapath_id of the switch are accepted in this field": "", "Only libvirt driver is supported.": "Libvirt 드라이버만 지원합니다.", "Only subnets that are already connected to the router can be selected.": "", "Open External Gateway": "", "OpenID Connect": "", "Operating Status": "", "Operating System": "", "Operation Center": "Operation Center", "Operation Name": "", "Operation Time": "", "Optimized Parameters": "", "Optional list": "", "Options": "", "Orchestration": "", "Orchestration Services": "", "Orchestration information": "", "Origin File Name": "", "Original Password": "", "Other Protocol": "", "Other Service": "", "Other Services": "", "Others": "기타", "Out Cluster": "", "Out of Sync": "", "Outputs": "", "Overlapping allocation pools: {pools}": "", "Overlay": "", "Overlay2": "", "Overview": "", "Owned Network": "", "Owned Network ID": "", "Owned Network ID/Name": "소유한 네트워크 ID/이름", "Owned Project": "", "Owned Subnet": "", "Owner": "소유자", "Ownership of a volume can be transferred from one project to another. The transfer process of the volume needs to perform the transfer operation in the original owner's project, and complete the \"accept\" operation in the receiver's project.": "", "PEM encoding": "", "PFS": "", "PG Count": "", "PGM": "", "PING": "", "PTR Domain Name": "", "PXE": "", "PXE Enabled": "", "Pakistan": "", "Palau": "", "Palestine": "", "Panama": "", "Papua New Guinea": "", "Paraguay": "", "Parameter": "", "Params Setting": "", "Password": "암호", "Password Type": "", "Password changed successfully, please log in again.": "", "Password must be the same with confirm password.": "", "Paste": "", "Paste File": "", "Path": "", "Pause": "", "Pause Container": "", "Pause Instance": "인스턴스 정지", "Paused": "", "Pausing": "", "Payload": "", "Peer": "", "Peer Address": "", "Peer Cidrs": "", "Peer Endpoint Group": "", "Peer Endpoint Group ID": "", "Peer Gateway Public Address": "", "Peer ID": "", "Peer Network": "", "Peer Network Segment": "", "Peer gateway public address for the IPsec site connection": "", "Pending": "", "Pending Create": "", "Pending Delete": "", "Pending Update": "", "Perform a consistent hash operation on the source IP address of the request to obtain a specific value. At the same time, the back-end server is numbered, and the request is distributed to the server with the corresponding number according to the calculation result. This can enable load distribution of visits from different source IPs, and at the same time enable requests from the same client IP to always be dispatched to a specific server. This method is suitable for load balancing TCP protocol without cookie function.": "", "Permanent": "", "Persistent": "", "Peru": "", "Phase1 Negotiation Mode": "", "Philippines": "", "Phone": "", "Physical CPU Usage": "", "Physical Network": "", "Physical Node": "", "Physical Nodes": "", "Physical Storage Usage": "", "Pitcairn": "", "Platform Info": "", "Please confirm your password!": "", "Please enter JSON in the correct format!": "", "Please enter URL!": "", "Please enter a correct certificate content, format is refer to the left tip!": "", "Please enter a correct domain, format is refer to the left tip!": "", "Please enter a correct private key, format is refer to the left tip!": "", "Please enter a file link starting with \"http://\" or \"https://\"!": "", "Please enter a memory page size, such as: 1024, 1024MiB": "", "Please enter a valid ASCII code": "", "Please enter a valid Email Address!": "", "Please enter a valid IPv4 value.": "", "Please enter a valid IPv6 value.": "", "Please enter a valid Phone Number": "", "Please enter complete key value!": "", "Please enter right format custom trait!": "", "Please enter right format key value!": "", "Please enter right format memory page value!": "", "Please enter right format trait!": "", "Please enter the correct id": "", "Please enter the server id to be reduced, and separate different id with \",\"": "", "Please fill in the peer network segment and subnet mask of CIDR format, the written subnets should be under the same router, one per line.": "", "Please input": "", "Please input <username> or <username>@<domain name>!": "<사용자 이름> 또는 <사용자 이름>@<도메인 이름>을 입력해주세요!", "Please input ICMP code(0-255)": "", "Please input ICMP type(0-255)": "", "Please input IPv4 or IPv6 cidr": "", "Please input IPv4 or IPv6 cidr, (e.g. ***********/24, 2001:DB8::/48)": "", "Please input a number": "숫자를 입력해주세요", "Please input a parameter": "매개변수를 입력하세요.", "Please input a valid ip!": "", "Please input a value": "값을 입력해주세요", "Please input at least 2 characters.": "", "Please input at least one record": "", "Please input auth key": "", "Please input cipher": "", "Please input cluster name": "", "Please input cluster template name": "", "Please input complete data": "", "Please input container name": "", "Please input file name": "", "Please input image": "", "Please input ip address": "IP 주소를 입력해주세요", "Please input ipv4": "", "Please input ipv6": "", "Please input key": "", "Please input key and value": "", "Please input key size": "", "Please input metadata": "메타데이터를 입력해 주세요", "Please input name": "", "Please input or load Template from a file": "", "Please input port and protocol": "포트와 프로토콜을 입력하세요.", "Please input prefix": "접두사를 입력하세요.", "Please input protocol number if it absent in select list.": "선택 목록에 없으면 프로토콜 번호를 입력하세요.", "Please input provider": "제공자를 입력하세요.", "Please input snapshot name": "snapshot 이름을 입력하세요.", "Please input the correct format:  <username> or <username>@<domain name>.": "올바른 형식(<사용자 이름> 또는 <사용자 이름>@<도메인 이름>)을 입력하십시오.", "Please input transfer id": "전송 ID를 입력하세요.", "Please input user name": "사용자 이름을 입력하세요.", "Please input value": "값을 입력하세요.", "Please input your Password!": "암호를 입력하세요!", "Please input your Username!": "사용자 이름을 입력하세요!", "Please input your current password!": "현재 암호를 입력하세요!", "Please input your password!": "암호를 입력하세요!", "Please input {label}": "{label}을(를) 입력하세요.", "Please input {label}!": "{label}을(를) 입력하세요!", "Please make sure this IP address be available to avoid creating VM failure.": "VM 생성 실패를 피하기 위해 IP 주소가 사용 가능한지 확인하세요.", "Please make sure this IP address be available.": "IP 주소가 사용 가능한지 확인하세요.", "Please note that when deleting a domain, all projects, users, and user groups under the domain will be deleted directly!": "도메인을 삭제할 때 해당 도메인 하위의 모든 프로젝트, 사용자 및 사용자 그룹이 모두 직접 삭제됩니다!", "Please reasonably plan the network and subnet to which the virtual network card belongs.": "가상 네트워크 카드가 속한 네트워크와 서브넷을 합리적으로 계획하세요.", "Please save your token properly and it will be valid for {left}.": "토큰을 적절히 저장하고 {left} 동안 유효합니다.", "Please select": "선택하세요.", "Please select a file": "파일을 선택하세요.", "Please select a file with the suffix {types}": "{types} 확장자를 가진 파일을 선택하세요.", "Please select a network!": "네트워크를 선택하세요!", "Please select a parameter": "매개변수를 선택하세요.", "Please select a subnet!": "서브넷을 선택하세요!", "Please select a type!": "유형을 선택하세요!", "Please select availability zone": "가용 영역을 선택하세요.", "Please select image driver": "이미지 드라이버를 선택하세요.", "Please select item!": "항목을 선택하세요!", "Please select login type!": "로그인 유형을 선택하세요!", "Please select policy": "정책을 선택하세요.", "Please select source": "소스를 선택하세요.", "Please select type": "유형을 선택하세요.", "Please select volume type": "볼륨 유형을 선택하세요.", "Please select your Region!": "지역을 선택하세요!", "Please select {label}!": "{label}을(를) 선택하세요!", "Please select {name} first": "{name}을(를) 먼저 선택하세요.", "Please select: {name} or an image file that is the same as it": "{name} 또는 그것과 같은 이미지 파일을 선택하세요.", "Please set CPU && Ram first.": "먼저 CPU와 RAM을 설정하세요.", "Please set MUNA": "MUNA를 설정하세요.", "Please set a size no less than {minSize} GiB!": "크기를 {minSize}GiB 이상으로 설정하세요!", "Please set at least one role!": "역할을 하나 이상 설정하세요!", "Please set the system disk size!": "시스템 디스크 크기를 설정해주세요!", "Please upload files smaller than { size }GiB on the page. It is recommended to upload files over { size }GiB using API.": "페이지에서 { size }GiB보다 작은 파일을 업로드하세요. { size }GiB 이상의 파일은 API를 사용하여 업로드하는 것이 권장됩니다.", "Pointer Record": "포인터 레코드", "Poland": "", "Policy": "정책", "Policy Detail": "", "Policy Edit": "", "Policy Name": "정책 이름", "Policy Rules": "", "Pool Algorithm": "풀 알고리즘", "Pool Description": "풀 설명", "Pool Detail": "풀 상세 정보", "Pool ID": "풀 ID", "Pool Info": "풀 정보", "Pool Name": "풀 이름", "Pool Protocol": "풀 프로토콜", "Pools": "풀", "Port": "포트", "Port Count": "포트 개수", "Port Detail": "포트 상세 정보", "Port Forwardings": "포트 포워딩", "Port Group": "포트 그룹", "Port Groups": "포트 그룹", "Port ID": "포트 ID", "Port Info": "포트 정보", "Port Range": "포트 범위", "Port Security": "포트 보안", "Port Security Enabled": "포트 보안 활성화됨", "Port Type": "포트 유형", "Ports": "포트", "Ports are either single values or ranges": "포트는 단일 값 또는 범위일 수 있습니다.", "Ports provide extra communication channels to your containers. You can select ports instead of networks or a mix of both, If the terminal port and the network are selected at the same time, note that the terminal port is not a terminal port of the selected network, and the container under the same network will only be assigned one IP address (The port executes its own security group rules by default).": "포트는 컨테이너에 추가 통신 채널을 제공합니다. 네트워크 대신 포트를 선택하거나 둘을 혼합하여 선택할 수 있습니다. 동시에 터미널 포트와 네트워크를 선택하면, 선택된 네트워크의 터미널 포트는 해당 네트워크의 터미널 포트가 아니며, 동일한 네트워크에 속한 컨테이너에는 하나의 IP 주소만 할당됩니다 (포트는 기본적으로 자체 보안 그룹 규칙을 실행합니다).", "Ports provide extra communication channels to your instances. You can select ports instead of networks or a mix of both (The port executes its own security group rules by default).": "포트는 인스턴스에 추가 통신 채널을 제공합니다. 네트워크 대신 포트를 선택하거나 둘을 혼합하여 선택할 수 있습니다 (포트는 기본적으로 자체 보안 그룹 규칙을 실행합니다).", "Portugal": "", "Power Off": "전원 끄기", "Power On": "전원 켜기", "Power State": "전원 상태", "Powering Off": "전원 끄는 중", "Powering On": "전원 켜는 중", "Pre Live Migration": "실시간 migration", "Pre-Shared Key must be the same with Confirm Shared Key.": "사전 공유 키는 확인된 공유 키와 동일해야 합니다.", "Pre-Shared Key(PSK) String": "사전 공유 키(PSK) 문자열", "Prefer": "선호", "Prefer(Thread siblings are preferred)": "선호(동일 스레드 그룹이 선호됩니다)", "Preferred": "선호됨", "Prefix": "접두사", "Prep Resize": "크기조정 준비", "Prepare Template": "템플릿 준비", "Previous": "이전", "Primary": "기본", "Primary is controlled by Designate, Secondary zones are slaved from another DNS Server.": "기본 영역은 Designate에 의해 제어되고, 보조 영역은 다른 DNS 서버에서 슬레이브됩니다.", "Private": "사설", "Private Key": "개인 키", "Profile": "프로필", "Progress": "진행", "Project": "프로젝트", "Project Console": "프로젝트 콘솔", "Project Detail": "프로젝트 상세 정보", "Project ID": "프로젝트 ID", "Project ID/Name": "프로젝트 ID/이름", "Project Name": "프로젝트 이름", "Project Num": "프로젝트 수", "Project Quota": "프로젝트 할당량", "Project Range": "프로젝트 범위", "Project Scope": "소속 프로젝트", "Project Scope (Project Name: Role Names)": "소속 프로젝트 (프로젝트 이름: 역할 이름)", "Project User Groups": "프로젝트 사용자 그룹", "Project Users": "프로젝트 사용자", "Projects": "프로젝트", "Promote": "승격", "Properties": "속성", "Protected": "보호됨", "Protocol": "프로토콜", "Protocol Type": "프로토콜 유형", "Provider": "제공자", "Provider Network Type": "제공자 네트워크 유형", "Provider Physical Network": "제공자 물리 네트워크", "Provision State": "프로비저닝 상태", "Provisioning Status": "프로비저닝 상태", "Public": "공용", "Public Access": "공개 접근", "Public Address": "공용 주소", "Public Images": "공용 이미지", "Public Key": "공개 키", "Published In": "발행 위치", "Published Out": "발행 외부", "Puerto Rico": "", "QCOW2 - QEMU image format": "QCOW2 - QEMU 이미지 형식", "Qatar": "", "QoS Bandwidth Egress Limit": "QoS 출력 방향 대역폭 제한", "QoS Bandwidth Ingress Limit": "QoS 진입 방향 대역폭 제한", "QoS Bandwidth Limit": "QoS 대역폭 제한", "QoS Detail": "QoS 상세 정보", "QoS Policies": "QoS 정책", "QoS Policy": "QoS 정책", "QoS Policy Detail": "QoS 정책 상세 정보", "QoS Policy ID": "QoS 정책 ID", "QoS Policy ID/Name": "QoS 정책 ID/이름", "QoS Spec": "QoS 스펙", "QoS Spec ID": "QoS 스펙 ID", "QoS Specs": "QoS 스펙", "QoS policies": "QoS 정책", "Qos Policy": "QoS 정책", "Queued": "대기 중", "Queued To Apply": "적용 대기 중", "Queued To Deny": "거부 대기 중", "Quota": "할당량", "Quota Overview": "할당량 개요", "Quota exceeded": "할당량 초과", "Quota is not enough for extend share.": "공유 확장에 충분한 할당량이 없습니다.", "Quota is not enough for extend volume.": "볼륨 확장에 충분한 할당량이 없습니다.", "Quota of key pair means: the number of allowed key pairs for each user.": "키 쌍의 할당량은 각 사용자에게 허용되는 키 쌍의 수를 의미합니다.", "Quota: Insufficient quota to create resources, please adjust resource quantity or quota(left { quota }, input { input }).": "리소스를 생성하기에 충분한 할당량이 없습니다. 리소스 수량이나 할당량을 조정해주세요 (남은 할당량: { quota }, 입력: { input }).", "Quota: Insufficient { name } quota to create resources, please adjust resource quantity or quota(left { left }, input { input }).": "{ name } 할당량이 부족하여 리소스를 생성할 수 없습니다. 리소스 수량이나 할당량을 조정해주세요 (남은 할당량: { left }, 입력: { input }).", "Quota: Insufficient { name } quota to create resources.": "{ name } 할당량이 부족하여 리소스를 생성할 수 없습니다.", "Quota: Project quotas sufficient resources can be created": "프로젝트 할당량으로는 충분한 리소스를 생성할 수 있습니다.", "RAM": "", "RAM (MiB)": "", "RAW - Raw disk image format": "RAW - 원본 디스크 이미지 형식", "RBAC Policies": "", "RBAC Policy Detail": "", "REJECT": "", "RESTORE COMPLETE": "복원 완료", "RESUME COMPLETE": "재개 완료", "RESUME FAILED": "재개 실패", "ROLLBACK COMPLETE": "롤백 완료", "ROLLBACK FAILED": "롤백 실패", "ROLLBACK IN PROGRESS": "롤백 진행 중", "ROUND_ROBIN": "", "RSVP": "", "Raid Interface": "RAID 인터페이스", "Ram Size (GiB)": "RAM 크기 (GiB)", "Ram value is { ram }, NUMA RAM value is { totalRam }, need to be equal. ": "Ram 값은 { ram }이며, NUMA RAM 값은 { totalRam }이어야 합니다. ", "Ramdisk ID": "램디스크 ID", "Ramdisk Image": "램디스크 이미지", "Rbac Policy": "", "Read and write": "읽기 및 쓰기", "Read only": "읽기 전용", "Real Name": "실제 이름", "Reason": "이유", "Reason: ": "이유: ", "Reboot": "재부팅", "Reboot Container": "컨테이너 재부팅", "Reboot Database Instance": "데이터베이스 인스턴스 재부팅", "Reboot Instance": "인스턴스 재부팅", "Rebooting": "재부팅 중", "Rebuild": "재구성", "Rebuild Block Device Mapping": "블록 디바이스 매핑 재구성", "Rebuild Container": "컨테이너 재구성", "Rebuild Instance": "인스턴스 재구성", "Rebuild Spawning": "재구성 생성 중", "Rebuilding": "재구성 중", "Rebuilt": "재구성됨", "Recently a day": "최근 1일", "Record Sets": "레코드 세트", "Records": "레코드", "Recordset Detail": "레코드 세트 상세 정보", "Recordsets Detail": "레코드 세트 상세 정보", "Recover": "복구", "Recovering": "복구 중", "Recovery Method": "", "Recycle Bin": "휴지통", "Region": "지역", "Registry Enabled": "레지스트리 활성화됨", "Related Policy": "", "Related Resources": "관련 리소스", "Release": "릴리스", "Release Fixed IP": "고정 IP 해제", "Remote Group Id": "원격 그룹 ID", "Remote IP Prefix": "원격 IP 접두사", "Remote Security Group": "원격 보안 그룹", "Remote Type": "원격 유형", "Remove": "제거", "Remove Default Project": "기본 프로젝트 제거", "Remove Network": "네트워크 제거", "Remove Router": "라우터 제거", "Remove Rule": "", "Remove default project for user": "사용자의 기본 프로젝트 제거", "Rename": "이름 변경", "Rename is to copy the current file to the new file address and delete the current file, which will affect the creation time of the file.": "이름 변경은 현재 파일을 새 파일 주소로 복사한 후 현재 파일을 삭제하는 것을 의미하며, 파일의 생성 시간에 영향을 줍니다.", "Replication Change": "복제 변경", "Report Count": "보고서 수", "Republic of the Congo": "", "Request ID": "요청 ID", "Require": "필요", "Require(Need multithreading)": "필요 (다중 스레딩 필요)", "Required Data Disk": "필수 데이터 디스크", "Rescue": "복구", "Rescued": "복구됨", "Rescuing": "복구 중", "Reserved": "예약됨", "Reset Status": "상태 재설정", "Reset To Initial Value": "초기 값으로 재설정", "Reset failed, please retry": "재설정 실패, 다시 시도하세요", "Resize": "크기 조정", "Resize Cluster": "클러스터 크기 조정", "Resize Instance": "인스턴스 크기 조정", "Resize Volume": "볼륨 크기 조정", "Resized": "크기 조정됨", "Resizing or Migrating": "크기 조정 또는 마이그레이션 중", "Resource": "리소스", "Resource Class": "리소스 클래스", "Resource Class Properties": "리소스 클래스 속성", "Resource Id": "리소스 ID", "Resource Not Found": "리소스를 찾을 수 없음", "Resource Pool": "리소스 풀", "Resource Status": "리소스 상태", "Resource Status Reason": "리소스 상태 이유", "Resource Type": "리소스 유형", "Resource Types": "리소스 유형", "Resources Synced": "동기화된 리소스", "Resource Monitor": "리소스 모니터", "Restart": "재시작", "Restart Container": "컨테이너 재시작", "Restart Database Service": "데이터베이스 서비스 재시작", "Restarting": "재시작 중", "Restore Backup": "백업 복원", "Restore From Snapshot": "스냅샷에서 복원", "Restore backup": "백업 복원", "Restore from snapshot": "스냅샷에서 복원", "Restoring": "복원 중", "Restoring Backup": "백업 복원 중", "Restricted": "제한된", "Restricted Situation": "제한된 상황", "Resume": "재개", "Resume Complete": "재개 완료", "Resume Failed": "재개 실패", "Resume In Progress": "재개 진행 중", "Resume Instance": "인스턴스 재개", "Resuming": "재개 중", "Retry times for restart on failure policy": "실패 정책에 따른 재시작 시도 횟수", "Retyping": "재입력", "Reunion": "재통합", "Reverse DNS Detail": "역 DNS 상세", "Reverse Detail": "확인필요", "Reverse Dns": "역 DNS", "Revert Resize or Migrate": "크기 조정 또는 마이그레이션 되돌리기", "Revert Resize/Migrate": "크기 조정 또는 마이그레이션 되돌리기", "Reverting": "되돌리는 중", "Reverting Error": "되돌리기 오류", "Reverting Resize or Migrate": "크기 조정 또는 마이그레이션 되돌리는 중", "Role": "역할", "Role Detail": "역할 상세 정보", "Role Name": "역할 이름", "Roles": "역할", "Rollback Complete": "롤백 완료", "Rollback Failed": "롤백 실패", "Rollback In Progress": "롤백 진행 중", "Romania": "", "Root Disk": "루트 디스크", "Root Disk (GiB)": "루트 디스크 (GiB)", "Root directory": "루트 디렉터리", "Router": "라우터", "Router Advertisements Mode": "", "Router Detail": "라우터 상세 정보", "Router External": "라우터 외부", "Router ID": "라우터 ID", "Router Port": "", "Routers": "라우터", "Rule": "", "Rule Action": "", "Rule Detail": "", "Rule Edit": "", "Rule Numbers": "규칙 수", "Rules": "규칙", "Rules Number": "규칙 수", "Running": "실행 중", "Running Threads": "실행 중인 스레드", "Running Time": "실행 시간", "Runtime": "런타임", "Russia": "", "Rwanda": "", "SCTP": "", "SNAPSHOT COMPLETE": "스냅샷 완료", "SNAT Enabled": "SNAT 활성화됨", "SNI Certificate": "SNI 인증서", "SNI Enabled": "SNI 활성화됨", "SOURCE_IP": "소스 IP", "SSH Public Key Fingerprint": "SSH 공개 키 지문", "SSL Parsing Method": "SSL 구문 분석 방법", "Saint Vincent and the Grenadines": "", "Same subnet with LB": "로드 밸런서와 동일한 서브넷", "Samoa": "", "San Marino": "", "Sao Tome and Principe": "", "Saudi Arabia": "", "Saving": "", "Scheduler Hints": "", "Scheduling": "", "Search": "", "Sec for DPD delay, > 0": "", "Sec for DPD timeout, > 0 & > DPD Interval": "", "Secondary": "", "Security Group": "보안 그룹", "Security Group Detail": "보안 그룹 세부 정보", "Security Group Info": "보안 그룹 정보", "Security Group Num:": "보안 그룹 수:", "Security Group Rule": "보안 그룹 규칙", "Security Group Rules": "보안 그룹 규칙", "Security Groups": "보안 그룹", "Security Groups Adding": "", "Security Groups Removing": "", "Security Info": "보안 정보", "Segment Detail": "", "Segment ID": "", "Segment Name": "", "Segmentation ID": "", "Segmentation Id": "", "Segments": "", "Select File": "", "Select Project": "", "Select Project Role": "", "Select User Group": "", "Select Volume Snapshot": "", "Select a QoS Policy": "", "Select a login type": "로그인 유형 선택", "Select a network": "", "Select a project": "", "Select a region": "지역 선택", "Select an object type": "", "Selected": "", "Selected Members": "", "Selected list": "", "Sender Policy Framework": "", "Senegal": "", "Serbia": "", "Serial": "", "Server Certificate": "", "Server Certificates": "", "Server Group": "서버 그룹", "Server Group Detail": "서버 그룹 세부 정보", "Server Group Member": "서버 그룹 멤버", "Server Groups": "서버 그룹", "Server Status": "", "Server Type": "", "Service": "서비스", "Service List": "", "Service Locator": "", "Service Port ID": "", "Service State": "", "Service Status": "", "Service Status Updated": "", "Service Type": "", "Service Unavailable (code: 503) ": "", "Services": "서비스", "Set": "", "Set Admin Password": "", "Set Boot Device": "", "Set Default Project": "기본 프로젝트 설정", "Set Domain Name PTR": "", "Set IP": "", "Set default project for user": "사용자의 기본 프로젝트 설정", "Seychelles": "", "Share": "", "Share Capacity (GiB)": "", "Share Detail": "", "Share File Storage": "", "Share Group": "", "Share Group Detail": "", "Share Group Type": "", "Share Group Type Detail": "", "Share Group Types": "", "Share Groups": "", "Share Id": "", "Share Instance": "", "Share Instance Detail": "", "Share Instances": "", "Share Network": "", "Share Network Detail": "", "Share Network Subnet": "", "Share Network Subnets": "", "Share Networks": "", "Share Protocol": "", "Share Replica ID": "", "Share Server": "", "Share Server Detail": "", "Share Servers": "", "Share Type": "", "Share Type Detail": "", "Share Type ID": "", "Share Type Name": "", "Share Types": "", "Shared": "공유", "Shared Images": "", "Shared Network": "공유 네트워크", "Shared Networks": "공유 네트워크", "Shared QoS Policies": "공유 QoS 정책", "Shared QoS Policy": "", "Shared policy only can insert shared rules.": "", "Shares": "", "Shelve": "", "Shelve Instance": "", "Shelved": "", "Shelved Offloaded": "", "Shelving": "", "Shelving Image Pending Upload": "", "Shelving Image Uploading": "", "Shelving Offloading": "", "Show All Domain": "", "Show Instance": "", "Show all Data": "", "Shrinking": "", "Shrinking Error": "", "Shrinking Possible Data Loss Error": "", "Shut Down": "", "Shut Off": "", "Shutoff": "", "Sierra Leone": "", "Sign Out": "", "Sign up": "", "Signal to send to the container: integer or string like SIGINT. When not set, SIGKILL is set as default value and the container will exit. The supported signals varies between platform. Besides, you can omit \"SIG\" prefix.": "", "Singapore": "", "Size": "크기", "Size (GiB)": "크기 (GiB)", "Slovakia (Slovak Republic)": "", "Slovenia": "", "Slow Query": "", "Small": "", "Small(Not recommended)": "", "Smart Scheduling": "", "Snapshot Complete": "", "Snapshot Failed": "", "Snapshot In Progress": "", "Snapshot Instance": "", "Snapshot Source": "", "Snapshots can be converted into volume and used to create an instance from the volume.": "", "Snapshotting": "", "Soft Delete Instance": "", "Soft Deleted": "", "Soft Deleting": "", "Soft Power Off": "", "Soft Reboot": "소프트 다시 시작", "Soft Reboot Instance": "인스턴스 소프트 다시 시작", "Soft Rebooting": "", "Soft-Affinity": "", "Soft-Anti-Affinity": "", "Solomon Islands": "", "Somalia": "", "Sorry, the page you visited does not exist.": "", "Source": "", "Source IP": "", "Source IP Address/Subnet": "", "Source Path: {path}": "", "Source Port": "", "Source Port/Port Range": "", "South Africa": "", "South Korea": "", "Spain": "", "Spawning": "", "Spec": "", "Specification": "", "Specify Physical Node": "", "Specify mount point.": "", "Specify the client IP address": "클라이언트 IP 주소 지정", "Specify the listener port": "리스너 포트 지정", "Specify whether future replicated instances will be created on the same hypervisor (affinity) or on different hypervisors (anti-affinity). This value is ignored if the instance to be launched is a replica.": "", "Specs": "", "Sri Lanka": "", "Stack": "<PERSON><PERSON>", "Stack Detail": "Stack 세부 정보", "Stack Events": "Stack 이벤트", "Stack Faults": "", "Stack ID": "", "Stack Name": "Stack 이름", "Stack Resource": "Stack 리소스", "Stack Resource Type": "Stack 리소스 타입", "Stack Resources": "Stack 리소스", "Stack Status": "Stack 상태", "Stacks": "<PERSON><PERSON>", "Stand Alone Ports Supported": "", "Standard Trait": "", "Start": "시작", "Start Container": "", "Start Instance": "", "Start Of Authority": "", "Start Source": "", "Start Source Name": "", "Start Time": "", "Start auto refreshing data": "", "Start refreshing data every {num} seconds": "", "Started At": "", "Startup Parameters": "", "State": "", "Static Routes": "", "Stats Information": "", "Status": "상태", "Status Code": "", "Status Detail": "", "Status Reason": "상태 현황", "Stop": "중지", "Stop Container": "", "Stop Database Service": "", "Stop Instance": "", "Stop auto refreshing data": "", "Stop refreshing data every {num} seconds": "", "Stopped": "", "Storage": "스토리지", "Storage Backends": "", "Storage Capacity(GiB)": "", "Storage Cluster Bandwidth": "", "Storage Cluster IOPS": "", "Storage Cluster OSD Latency": "", "Storage Cluster Status": "", "Storage Cluster Usage": "", "Storage Clusters": "", "Storage IOPS": "스토리지 IOPS", "Storage Interface": "", "Storage Policy": "스토리지 정책", "Storage Pool Capacity Usage": "", "Storage Types": "", "Sub Users": "", "Subnet": "서브넷", "Subnet Count": "서브넷 수", "Subnet Detail": "서브넷 세부 정보", "Subnet ID": "서브넷 ID", "Subnet ID/Name": "서브넷 ID/이름", "Subnet Name": "서브넷 이름", "Subnets": "서브넷", "Subordinate Projects": "", "Subordinate User Groups": "", "Succeeded": "", "Success": "완료", "Sudan": "", "Supports resumable transfer (recommended when uploading a large image)": "단점 연속 전송 지원(대형 미러 업로드 시 사용 추천)", "Suriname": "", "Suspend": "", "Suspend Complete": "", "Suspend Failed": "", "Suspend In Progress": "", "Suspend Instance": "", "Suspended": "", "Suspending": "", "Swaziland": "", "Sweden": "", "Switch ID": "", "Switch Info": "", "Switch Language": "", "Switch Project": "", "Switzerland": "", "Syncing": "", "Syrian Arab Republic": "", "System": "", "System Config": "", "System Disk": "", "System Info": "", "System Load": "", "System Roles": "", "System Running Time": "", "System is error, please try again later.": "", "TCP": "", "TCP Connections": "", "TLS Disabled": "", "TTL": "", "TTL (Time to Live) for the zone.": "영역의 TTL(Time to Live)입니다.", "Tag is no longer than 60 characters": "태그는 60자(영문 기준) 이하여야 합니다.", "Tags": "태그", "Tags Info": "태그 정보", "Tags are not case sensitive": "", "Taiwan": "", "Tajikistan": "", "Take effect after restart": "", "Tanzania": "", "Target Compute Host": "", "Target IP Address": "", "Target Port": "", "Target Project": "", "Target Project ID": "", "Target Project ID/Name": "", "Target Project Name": "", "Target Storage Backend": "", "Target Tenant": "", "Task State": "", "Template Content": "템플릿 내용", "Template Name": "템플릿 이름", "Text Record": "", "Thailand": "", "That is, after how many consecutive failures of the health check, the health check status of the back-end cloud server is changed from normal to abnormal": "", "The DNS nameserver to use for this cluster template": "", "The Federation of Saint Kitts and Nevis": "", "The Provider is the encryption provider format (e.g. \"luks\")": "", "The Republic of Macedonia": "", "The Republic of South Sudan": "", "The SSH key is a way to remotely log in to the cluster instance. If it’s not set, the value of this in the template will be used.": "", "The SSH key is a way to remotely log in to the cluster instance. The cloud platform only helps to keep the public key. Please keep your private key properly.": "", "The SSH key is a way to remotely log in to the instance. The cloud platform only helps to keep the public key. Please keep your private key properly.": "", "The amphora instance is required for load balancing service setup and is not recommended": "", "The associated floating IP, virtual adapter, volume and other resources will be automatically disassociated.": "", "The certificate contains information such as the public key and signature of the certificate. The extension of the certificate is \"pem\" or \"crt\", you can directly enter certificate content or upload certificate file.": "", "The changed node count can not be equal to the current value": "", "The command to execute": "", "The container memory size in MiB": "", "The container runtime tool to create container with": "", "The creation instruction has been issued, please refresh to see the actual situation in the list.": "", "The creation instruction was issued successfully, instance: {name}. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.": "", "The current operation requires the instance to be shut down:": "", "The current platform has not yet enabled the {name} management module. Please contact the administrator to enable it": "현재 플랫폼은 아직 {name} 관리 모듈을 활성화하지 않았습니다. 당신은 당신의 관리자가 됩니다", "The description can be up to 255 characters long.": "", "The disk size in GiB for per container": "", "The domain name can only be composed of letters, numbers, dashes, in A dash cannot be at the beginning or end, and a single string cannot exceed more than 63 characters, separated by dots; At most can support 30 domain names, separated by commas;The length of a single domain name does not exceed 100 characters, and the total length degree does not exceed 1024 characters.": "", "The entire inspection process takes 5 to 10 minutes, so you need to be patient. After the registration is completed, the node configuration status will return to the manageable status.": "", "The entrypoint which overwrites the default ENTRYPOINT of the image": "", "The feasible configuration of cloud-init or cloudbase-init service in the image is not synced to image's properties, so the Login Name is unknown.": "", "The file with the same name will be overwritten.": "", "The floating IP configured with port forwardings cannot be bound": "", "The format of the certificate content is: by \"----BEGIN CERTIFICATE-----\" as the beginning,\"-----END CERTIFICATE----\" as the end, 64 characters per line, the last line does not exceed 64 characters, and there cannot be blank lines.": "", "The host name of this container": "", "The http_proxy address to use for nodes in cluster": "", "The https_proxy address to use for nodes in cluster": "", "The image is not existed": "", "The instance architecture diagram mainly shows the overall architecture composition of the instance. If you need to view the network topology of the instance, please go to: ": "인스턴스 아키텍처 다이어그램은 주로 인스턴스의 전체 아키텍처 구성을 보여줍니다. 인스턴스의 네트워크 토폴로지를 보려면 다음으로 이동하십시오: ", "The instance deleted immediately cannot be restored": "즉시 삭제된 인스턴스는 복구할 수 없습니다", "The instance has been locked. If you want to do more, please unlock it first.": "인스턴스가 잠겼습니다. 추가적인 작업을 위해 먼저 잠금을 해제 해 주세요.", "The instance is not shut down, unable to restore.": "인스턴스가 종료되지 않아, 복구할 수 없습니다.", "The instance which is boot from volume will create snapshots for each mounted volumes.": "볼륨에서 부팅되는 인스턴스는 마운트된 각 볼륨에 대한 스냅샷을 생성합니다.", "The instances in the affinity group are allocated to the same physical machine as much as possible, and when there are no more physical machines to allocate, the normal allocation strategy is returned.": "\"affinity group\"의 인스턴스는 가능한 한 동일한 물리적 시스템에 할당되며, 할당할 물리적 시스템이 더 이상 없을 경우 일반적인 할당 전략을 따르게 됩니다.", "The instances in the affinity group are strictly allocated to the same physical machine. When there are no more physical machines to allocate, the allocation fails.": "\"affinity group\"의 인스턴스는 동일한 물리적 시스템에만 할당됩니다. 할당할 물리적 시스템이 더 이상 없으면 할당이 실패합니다.", "The instances in the anti-affinity group are allocated to different physical machines as much as possible. When there are no more physical machines to allocate, the normal allocation strategy is returned.": "\"anti-affinity group\"의 인스턴스는 가능한 한 서로 다른 물리적 시스템에 할당됩니다. 할당할 물리적 시스템이 더 이상 없으면 일반적인 할당 전략을 따르게 됩니다.", "The instances in the anti-affinity group are strictly allocated to different physical machines. When there are no more physical machines to allocate, the allocation fails.": "\"anti-affinity group\"의 인스턴스는 서로 다른 물리적 시스템에만 할당됩니다. 할당할 물리적 시스템이 더 이상 없으면 할당이 실패합니다.", "The ip address {ip} is duplicated, please modify it.": "IP 주소 {ip}이(가) 중복되었습니다. 수정해 주세요.", "The ip is not within the allocated pool!": "해당 ip는 할당된 pool 안에 없습니다!", "The ip of external members can be any, including the public network ip.": "외부 구성원의 IP 주소는 공용 네트워크 IP를 포함하여 어떤 것이든 가능합니다.", "The key pair allows you to SSH into your newly created instance. You can select an existing key pair, import a key pair, or generate a new key pair.": "키페어는 새로 생성한 인스턴스에 SSH로 접속할 수 있도록 해줍니다. 기존의 키페어를 선택하거나, 키페어를 가져오거나, 새로운 키페어를 생성할 수 있습니다.", "The kill signal to send": "전송 할 Kill 신호", "The limit of cluster instance greater than or equal to 1.": "클러스터 인스턴스의 한도는 하나 이상입니다.", "The maximum batch size is {size}, that is, the size of the port range cannot exceed {size}.": "최대 batch 크기는 {size}입니다. 즉, 포트 범위의 크기는 {size}를 초과할 수 없습니다.", "The maximum transmission unit (MTU) value to address fragmentation. Minimum value is 68 for IPv4, and 1280 for IPv6.": "분할을 처리하기 위한 최대 전송 단위(MTU) 값입니다. IPv4의 경우 최소값은 68이며, IPv6의 경우 최소값은 1280입니다.", "The min size is {size} GiB": "최소 크기는 {size} GiB입니다.", "The name of the physical network to which a port is connected": "포트가 연결된 물리 네트워크의 이름", "The name should be end with \".\"": "", "The name should contain letter or number, the length is 1 to 16, characters can only contain \"0-9, a-z, A-Z, -, _.\"": "이름은 문자나 숫자를 포함해야 하고, 길이는 1~16자 사이여야 하며, 문자는 \"0-9, a-z, A-Z, -, _.\"만 포함할 수 있습니다.", "The name should contain letter or number, the length is 2 to 64, characters can only contain \"0-9, a-z, A-Z, -, _.\"": "이름은 문자나 숫자를 포함해야 하고, 길이는 2~64자 사이여야 하며, 문자는 \"0-9, a-z, A-Z, -, _.\"만 포함할 수 있습니다.", "The name should start with letter or number, and be a string of 2 to 255, characters can only contain \"0-9, a-z, A-Z, -, _, .\"": "이름은 문자나 숫자로 시작해야 하고, 2~255자 사이의 문자열이어야 하며, 문자는 \"0-9, a-z, A-Z, -, _, .\"만 포함할 수 있습니다.", "The name should start with upper letter or lower letter, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].:^\".": "이름은 대문자나 소문자로 시작해야 하고, 1~128자 사이의 문자열이어야 하며, 문자는 \"0-9, a-z, A-Z, \"-'_()[].:^\"만 포함할 수 있습니다.", "The name should start with upper letter or lower letter, characters can only contain \"0-9, a-z, A-Z, -, _, .\"": "이름은 대문자나 소문자로 시작해야 하며, 문자는 \"0-9, a-z, A-Z, \"-'_()[].:^\"만 포함할 수 있습니다.", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].\".": "이름은 대문자, 소문자 또는 chinese로 시작해야하며, 1~128자의 문자열이어야합니다. 이름에 사용되는 문자는 \"0-9, a-z, A-Z, -, _, .\"만 포함할 수 있습니다.", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].:^\".": "이름은 대문자, 소문자 또는 chinese로 시작해야하며, 1~128자의 문자열이어야합니다. 이름에 사용되는 문자는 \"0-9, a-z, A-Z, \"-'_()[].:^\"만 포함할 수 있습니다.", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_.\".": "이름은 대문자, 소문자 또는 chinese로 시작해야하며, 1~128자의 문자열이어야합니다. 이름에 사용되는 문자는 \"0-9, a-z, A-Z, \"-'_.\"만 포함할 수 있습니다.", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 64, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].^\".": "이름은 chinese, 대문자, 소문자로 시작해야 하고, 1~64자 사이의 문자열이어야 하며, 문자는 \"0-9, a-z, A-Z, \"-'_()[].^\"만 포함할 수 있습니다.", "The name should start with upper letter, lower letter or chinese, and be a string of 3 to 63, characters can only contain \"0-9, a-z, A-Z, chinese, -, .\".": "이름은 대문자, 소문자, 또는 chinese로 시작해야 하며, 3~63자 사이의 문자열이어야 하며, 문자는 \"0-9, a-z, A-Z, chinese, -, .\"만 포함할 수 있습니다.", "The name should start with upper letter, lower letter, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, -, _\".": "이름은 대문자 또는 소문자로 시작해야 하며, 1~128자의 문자열이어야 합니다. 이름에 사용되는 문자는 \"0-9, a-z, A-Z, -, _\"만 가능합니다.", "The name should start with upper letter, lower letter, and be a string of 2 to 255, characters can only contain \"0-9, a-z, A-Z, -, ., _\".": "이름은 대문자 또는 소문자로 시작해야 하며, 2~255자의 문자열이어야 합니다. 이름에 사용되는 문자는 \"0-9, a-z, A-Z, -, ., _\"만 가능합니다.", "The name should start with upper letter, lower letter, and be a string of 3 to 63, characters can only contain \"0-9, a-z, A-Z, -\".": "이름은 대문자 또는 소문자로 시작해야 하며, 3~63자의 문자열이어야 합니다. 이름에 사용되는 문자는 \"0-9, a-z, A-Z, -\"만 가능합니다.", "The new password cannot be identical to the current password.": "새 비밀번호는 현재 비밀번호와 동일할 수 없습니다.", "The no_proxy address to use for nodes in cluster": "클러스터의 노드에서 사용할 no_proxy 주소입니다.", "The number of allowed key pairs for each user.": "각 사용자별 허용되는 키 쌍(key pair) 수입니다.", "The number of vCPU cores should not exceed the maximum number of CPU cores of the physical node. Otherwise it will cause fail to schedule to any physical node when creating instance.": "vCPU 코어의 수는 물리적 노드의 최대 CPU 코어 수를 초과해서는 안 됩니다. 그렇지 않으면 인스턴스 생성 시 어떤 물리적 노드에도 스케줄링할 수 없는 문제가 발생합니다.", "The number of virtual cpu for this container": "이 컨테이너의 가상 CPU 수", "The optional headers to insert into the request before it is sent to the backend member.": "백엔드 멤버에게 전송되기 전에 요청에 삽입할 선택적 헤더입니다.", "The password must not be the same as the previous": "이전 비밀번호와 중복되지 않아야 합니다.", "The password must not be the same as the previous two": "이전 두 개의 비밀번호와 중복되지 않아야 합니다.", "The password must not be the same as the previous {num}": "이전 {num}개의 비밀번호와 중복되지 않아야 합니다.", "The port created here will be automatically deleted when detach. If you need a reusable port, please go to the Virtual Adapter page to create and attach the port to instance.": "여기서 생성된 포트는 분리될 때 자동으로 삭제됩니다. 재사용 가능한 포트가 필요한 경우 가상 어댑터 페이지로 이동하여 인스턴스에 연결하여 생성하십시오.", "The private key content format is: with \"-----BEGIN RSA PRIVATE KEY-----\" as the beginning,\"-----END RSA PRIVATE KEY-----\" as the end, 64 characters per line, the last line does not exceed 64 characters, and there cannot be blank lines.": "", "The private key of the certificate, the extension of the private key is \"key\", you can directly enter the content of the private key file or upload a private key that conforms to the format document.": "인증서의 개인 키는 확장자가 \"key\"인 개인 키입니다. 개인 키 파일의 내용을 직접 입력하거나 형식 문서에 맞는 개인 키를 업로드할 수 있습니다.", "The resource class of the scheduled node needs to correspond to the resource class name of the flavor used by the ironic instance (for example, the resource class name of the scheduling node is baremetal.with-GPU, and the custom resource class name of the flavor is CUSTOM_BAREMETAL_WITH_GPU=1).": "예약된 노드의 자원 클래스는 ironic 인스턴스에서 사용하는 flavor의 자원 클래스 이름과 일치해야 합니다 (예: 예약된 노드의 자원 클래스 이름이 baremetal.with-GPU이고 플레이버의 사용자 정의 자원 클래스 이름이 CUSTOM_BAREMETAL_WITH_GPU=1인 경우).", "The resource has been deleted": "이 리소스는 삭제되었습니다.", "The root and os_admin are default users and cannot be created!": "root와 os_admin은 기본 사용자로, 생성할 수 없습니다!", "The root disk of the instance has snapshots": "", "The security group is similar to the firewall function and is used to set up network access control. ": "security group은 방화벽 기능과 유사하며 네트워크 액세스 제어를 설정하는 데 사용됩니다.", "The security group is similar to the firewall function for setting up network access control, or you can go to the console and create a new security group. (Note: The security group you selected will work on all virtual LANs on the instances.)": "security group은 방화벽 기능과 유사하며 네트워크 액세스 제어를 설정하는 데 사용됩니다. 또는 콘솔로 이동하여 새 security group을 만들 수 있습니다. (참고: 선택한 security group은 인스턴스의 모든 가상 LAN에서 작동합니다.)", "The selected VPC/subnet does not have IPv6 enabled.": "선택한 VPC/subnet에는 IPv6가 활성화되어 있지 않습니다.", "The selected network has no subnet": "선택한 네트워크엔 서브넷이 존재하지 않습니다.", "The selected project is different from the project to which the network belongs. That is, the subnet to be created is not under the same project as the network. Please do not continue unless you are quite sure what you are doing.": "선택한 프로젝트가 네트워크가 속한 프로젝트와 다릅니다. 즉, 생성할 서브넷이 네트워크와 동일한 프로젝트에 속하지 않습니다. 확실하지 않은 경우 진행하지 마십시오.", "The session has expired, please log in again.": "세션이 만료되었습니다. 다시 로그인해주세요.", "The shelved offloaded instance only supports immediate deletion": "저장된 인스턴스는 즉시 삭제만 지원합니다.", "The size of the external port range is required to be the same as the size of the internal port range": "내부 포트 범위의 크기와 외부 포트 범위의 크기는 동일해야 합니다.", "The start source is a template used to create an instance. You can choose an image or a bootable volume.": "start source는 인스턴스를 생성하는 데 사용되는 템플릿입니다. 이미지나 부팅 가능한 볼륨을 선택할 수 있습니다.", "The starting number must be less than the ending number": "시작 번호는 끝 번호보다 작아야 합니다.", "The timeout for cluster creation in minutes.": "클러스터 생성 제한 시간(분)입니다.", "The timeout period of waiting for the return of the health check request, the check timeout will be judged as a check failure": "health check 요청의 반환을 기다리는 제한 시간. 점검 시간 초과는 점검 실패로 간주됩니다.", "The total amount of data is { total }, and the interface can support downloading { totalMax } pieces of data. If you need to download all the data, please contact the administrator.": "모든 데이터의 총량은 { total }이며, 인터페이스는 { totalMax }개의 데이터 다운로드를 지원합니다. 모든 데이터를 다운로드해야 하는 경우 관리자에게 문의하십시오.", "The trait name of the flavor needs to correspond to the trait of the scheduling node; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all necessary traits (for example: the trait of the scheduling node has HW_CPU_X86_VMX trait, and the flavor adds HW_CPU_X86_VMX, it can be scheduled to this node for necessary traits).": "스케줄링 노드의 특성 이름과 flavor의 특성 이름이 일치해야 합니다. Ironic 인스턴스에 필요한 특성을 삽입함으로써, 컴퓨팅 서비스는 모든 필요한 특성을 갖춘 베어메탈 노드로 인스턴스를 스케줄링합니다. (예: 스케줄링 노드의 특성에는 HW_CPU_X86_VMX 특성이 있고, flavor에 HW_CPU_X86_VMX를 추가하면 이 노드로 필요한 특성을 갖춘 인스턴스를 스케줄링할 수 있습니다).", "The trait of the scheduled node needs to correspond to the trait of the flavor used by the ironic instance; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all the necessary traits (for example, the ironic instance which use the flavor that has HW_CPU_X86_VMX as a necessary trait, can be scheduled to the node which has the trait of HW_CPU_X86_VMX).": "예약된 노드의 특성은 Ironic 인스턴스에서 사용하는 flavor의 특성과 일치해야 합니다. 필요한 특성을 Ironic 인스턴스에 삽입함으로써, 컴퓨팅 서비스는 필요한 모든 특성을 갖춘 베어메탈 노드로만 인스턴스를 스케줄링합니다. 예를 들어, HW_CPU_X86_VMX를 필수 특성으로 가진 flavor를 사용하는 Ironic 인스턴스는 HW_CPU_X86_VMX 특성을 가진 노드로 스케줄링될 수 있습니다.", "The unit suffix must be one of the following: Kb(it), Kib(it), Mb(it), Mib(it), Gb(it), Gib(it), Tb(it), Tib(it), KB, KiB, MB, MiB, GB, GiB, TB, TiB. If the unit suffix is not provided, it is assumed to be KB.": "단위는 다음 중 하나여야 합니다: <PERSON>b(it), <PERSON><PERSON>(it), <PERSON>b(it), <PERSON><PERSON>(it), <PERSON>b(it), <PERSON><PERSON>(it), Tb(it), Tib(it), KB, KiB, MB, MiB, GB, GiB, TB, TiB. 단위가 제공되지 않으면 KB로 간주됩니다.", "The user has been disabled, please contact the administrator": "사용자가 비활성화되었습니다. 관리자에게 문의하십시오.", "The user needs to ensure that the input is a shell script that can run completely and normally.": "사용자는 정상적으로 실행될 수 있는 셸 스크립트임을 보장해야 합니다.", "The value of the upper limit of the range must be greater than the value of the lower limit of the range.": "범위 상한값은 범위 하한값보다 커야합니다.", "The volume associated with the backup is not available, unable to restore.": "해당 백업과 연결된 볼륨을 사용할 수 없어 복원할 수 없습니다.", "The volume status can be reset to in-use only when the previous status is in-use.": "이전 상태가 in-use일 때에만, 볼륨 상태를 in-use로 재설정할 수 있습니다.", "The volume type needs to be consistent with the volume type when the snapshot is created.": "볼륨 타입은 스냅샷 생성 시의 볼륨 타입과 일치해야 합니다.", "The volume type needs to set \"multiattach\" in the metadata to support shared volume attributes.": "공유 볼륨 속성을 지원하려면 메타데이터에 \"multiattach\"를 설정해야 합니다.", "The working directory for commands to run in": "실행할 명령의 작업 디렉토리", "The zone name should end with \".\"": "영역 이름은 \".\"로 끝나야 합니다.", "The {action} instruction has been issued, instance: {name}. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.": "{action} 명령이 {name} 인스턴스에 대해 실행되었습니다. 목록 데이터의 변경 사항을 확인하기 위해 몇 초를 기다리거나 수동으로 데이터를 새로 고쳐 최종 결과를 얻을 수 있습니다.", "The {action} instruction has been issued. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.": "{action} 명령이 실행되었습니다. 목록 데이터의 변경 사항을 확인하려면 몇 초 기다리거나 수동으로 데이터를 새로 고쳐 최종 표시 결과를 얻을 수 있습니다.", "The {name} has already been used by other {resource}({content}), please change.": "{name}은(는) 이미 다른 {resource}({content})에서 사용 중입니다. 다른 이름으로 변경해주세요.", "The {name} {ports} have already been used, please change.": "해당 {name} {ports}는 이미 사용 중입니다. 변경해주세요.", "There are resources that cannot {action} in the selected resources, such as:": "선택한 자원 중에서 {action}할 수 없는 자원이 있습니다. 예):", "There are resources that cannot {action} in the selected resources.": "선택한 리소스 중에서 {action} 할 수 없는 리소스가 있습니다.", "There are resources under the project and cannot be deleted.": "프로젝트에 속한 리소스가 있어서 삭제할 수 없습니다.", "There is currently a image that needs to continue uploading. Please refresh page and upload the image. The new image cannot be used for the time being.": "현재 계속 업로드해야 하는 미러링이 있습니다. 새로고침하거나 돌아가서 해당 미러링 업로드를 완료하세요. 새 미러링은 당분간 중단점 연속 전송 기능을 사용할 수 없습니다.", "There is currently no file to paste.": "현재 파일을 붙여넣을 대상이 없습니다.", "This operation creates a security group with default security group rules for the IPv4 and IPv6 ether types.": "이 작업은 IPv4 및 IPv6 이더넷 유형에 대한 기본 보안 그룹 규칙이 포함된 보안 그룹을 만듭니다.", "This service will automatically query the configuration (CPU, memory, etc.) and MAC address of the physical machine, and the ironic-inspector service will automatically register this information in the node information.": "이 서비스는 자동으로 물리적 머신의 구성(CPU, 메모리 등)과 MAC 주소를 쿼리하며, ironic-inspector 서비스는 이 정보를 노드 정보에 자동으로 등록합니다.", "This will delete all child objects of the load balancer.": "이 작업은 로드 밸런서의 모든 하위 객체를 삭제합니다.", "Threads Activity Trends": "Thread Activity Trends", "Time Interval: ": "Time Interval", "Time To Live": "Time To Live", "Time To Live in seconds.": "Time To Live(초).", "Time between running the check in seconds": "검사 간격(초)", "Timeout(Minute)": "타임아웃(분)", "Timeout(s)": "타임아웃(초)", "Tips: without domain means \"Default\" domain.": "팁: 도메인이 없으면 \"Default\" 도메인을 의미합니다.", "To open": "열기", "Today CPU usage > 80% alert": "오늘 CPU 사용량 > 80% 경고", "Today Memory usage > 80% alert": "오늘 메모리 사용량 > 80% 경고", "Togo": "Togo", "Tokelau": "Tokelau", "Tonga": "Tonga", "Too many disks mounted on the instance will affect the read and write performance. It is recommended not to exceed 16 disks.": "인스턴스에 마운트된 디스크가 많을수록 읽기/쓰기 성능에 영향을 미칩니다. 16개 이상의 디스크를 사용하지 않는 것이 좋습니다.", "Topic": "Topic", "Topology": "토폴로지", "Total": "전체", "Total Capacity": "전체 용량", "Total Connections": "전체 연결", "Total Consumers": "전체 고객", "Total Containers": "전체 컨테이너", "Total Exchanges": "전체 교환", "Total IPs": "전체 IP", "Total Queues": "전체 큐", "Total Ram": "전체 RAM", "Total {total} items": "전체 {total} 항목", "Trait Properties": "Trait Properties", "Traits": "Trai<PERSON>", "Transfer ID": "이전 ID", "Transfer Name": "이전 이름", "Transferred": "전송됨", "Transform Protocol": "프로토콜 변환", "Trinidad and Tobago": "Trinidad and Tobago", "True": "True", "Tunisia": "Tunisia", "Turkey": "Turkey", "Turkmenistan": "Turkmenistan", "Turks and Caicos Islands": "Turks and Caicos Islands", "Tuvalu": "Tuvalu", "Two-way authentication": "Two-way authentication", "Type": "유형", "UDP": "UDP", "UDPLite": "UDPLite", "UNHEALTHY": "UNHEALTHY", "UNKNOWN": "UNKNOWN", "UOS": "UOS", "UPDATE COMPLETE": "UPDATE COMPLETE", "UPDATE FAILED": "UPDATE FAILED", "UPDATE IN PROGRESS": "UPDATE IN PROGRESS", "USB Info": "USB Info", "USB Parameters": "USB Parameters", "USB model, used when configuring instance flavor": "", "USER": "USER", "UStack": "UStack", "UStack Reports": "UStack 보고서", "UStack Services": "UStack 서비스", "UUID": "UUID", "Ubuntu": "Ubuntu", "Uccps": "Uccps", "Uganda": "Uganda", "Ukraine": "Ukraine", "Unable to create instance: batch creation is not supported when specifying IP.": "인스턴스를 생성하지 못했습니다: IP를 지정할 때 일괄 생성은 지원되지 않습니다.", "Unable to create instance: insufficient quota to create resources.": "인스턴스를 생성하지 못했습니다: 생성할 자원의 할당량이 충분하지 않습니다.", "Unable to create volume: insufficient quota to create resources.": "볼륨을 생성하지 못했습니다: 생성할 자원의 할당량이 충분하지 않습니다.", "Unable to delete router \"{ name }\". External gateway is opened, please clear external gateway first.": "\"{ name }\" 라우터를 삭제할 수 없습니다. 외부 게이트웨이가 열려 있으므로, 먼저 외부 게이트웨이를 삭제해야 합니다.", "Unable to get {name} detail.": "{name}의 세부 정보를 가져올 수 없습니다.", "Unable to get {name}.": "{name}을 가져올 수 없습니다.", "Unable to get {title}, please go back to ": "{title}을 가져올 수 없습니다,  로 돌아가십시오.", "Unable to get {title}, please go to ": "{title}을 가져올 수 없습니다,  로 가십시오.", "Unable to paste into the same folder.": "같은 폴더에 붙여넣을 수 없습니다.", "Unable to render form": "양식을 렌더링할 수 없습니다", "Unable to {action} {name}.": "{name}을(를) {action}할 수 없습니다.", "Unable to {action}, because : {reason}, instance: {name}.": "{reason}때문에 {action}할 수 없습니다, 인스턴스: {instance}", "Unable to {action}, instance: {name}.": "{action}할 수 없습니다, 인스턴스: {instance}", "Unable to {action}.": "{action} 할 수 없습니다.", "Unable to {title}, please go back to ": "{title} 할 수 없습니다,  로 돌아가십시오", "Unattached": "연결되지 않음", "Unavailable": "사용할 수 없음", "Unbootable": "부팅 불가능", "Unbounded": "제한 없음", "United Arab Emirates": "United Arab Emirates", "United Kingdom": "United Kingdom", "United States": "United States", "Unknown": "알 수 없음", "Unless Stopped": "중지되지 않음", "Unless you know clearly which AZ to create the volume in, you don not need to fill in here.": "만약 볼륨을 생성할 Availability Zone을 명확히 알고 있다면, 여기에 입력하셔야 합니다.", "Unlimit": "무제한", "Unlock": "잠금 해제", "Unlock Instance": "인스턴스 잠금 해제", "Unmanage Error": "관리 중지 오류", "Unmanage Starting": "관리 중지 시작", "Unmanaged": "관리되지 않음", "Unpause": "일시 중지 해제", "Unpause Container": "컨테이너 일시 중지 해제", "Unpause Instance": "인스턴스 일시 중지 해제", "Unrescuing": "인스턴스 구조", "Unrestricted": "무제한", "Unset": "해제", "Unshelve": "복원", "Unshelve Instance": "복원 인스턴스", "Unshelving": "복원 대기 중", "Unused": "미사용", "Up": "위", "Update": "", "Update Access": "액세스 업데이트", "Update At": "업데이트 시간", "Update Cluster Template": "클러스터 템플릿 업데이트", "Update Complete": "업데이트 완료", "Update Failed": "업데이트 실패", "Update In Progress": "업데이트 진행 중", "Update Record Set": "레코드 셋 업데이트", "Update Segment": "", "Update Status": "상태 업데이트", "Update Template": "템플릿 업데이트", "Update User Password": "사용자 암호 업데이트", "Update user password": "사용자 암호 업데이트", "Updated": "", "Updated At": "갱신 시점", "Updating": "업데이트 중", "Updating Password": "비밀번호 업데이트 중", "Upgrade Cluster": "클러스터 업그레이드", "Upload File": "업로드 파일", "Upload Type": "업로드 유형", "Upload progress": "업로드 진행", "Uploading": "업로드", "Uploads with mirrors are interrupted. The name of the mirror file is: {name}. Do you want to continue the image? Please select the mirror file in the button below and click the \"Continue\" button; Tips: If the wrong file is selected, it will not be interrupted. Clicking the \"Delete\" button will delete the task of uploading the image.": "미러링된 업로드가 중단되었습니다. 이 이미지 파일의 이름은 {name} 입니다. 이 미러링을 계속 진행하시겠습니까? 아래 버튼에서 해당 미러링 파일을 선택하고 \"계속\" 버튼을 클릭하십시오; 훈훈한 알림: 파일을 잘못 선택하면 중단하여 계속 전송할 수 없습니다. '삭제' 버튼을 클릭하면 해당 이미지를 업로드하는 작업이 삭제됩니다.", "Uruguay": "Uruguay", "Usage": "사용", "Usage Type": "사용 유형", "Usb Controller": "USB 컨트롤러", "Use Type": "사용 형식", "Used": "사용중", "Used IPs": "사용중인 IP", "Used by tunnel(s): {names}. ID(s): {ids}": "Tunnel(s) {names}에 의해 사용 중입니다. ID(s): {ids}", "Used to restrict whether the application credential may be used for the creation or destruction of other application credentials or trusts.": "다른 응용 프로그램 자격 증명이나 트러스트를 생성하거나 파기하는 데 응용 프로그램 자격 증명을 사용할 수 있는지 여부를 제한하는 데 사용됩니다.", "User": "사용자", "User Account": "사용자 계정", "User Center": "사용자 센터", "User Data": "사용자 데이터", "User Detail": "사용자 세부 정보", "User Edit": "사용자 편집", "User Group": "사용자 그룹", "User Group Detail": "사용자 그룹 세부 정보", "User Group ID/Name": "사용자 그룹 ID/이름", "User Group Name": "사용자 그룹 이름", "User Group Num": "사용자 그룹 수", "User Group Num: ": "사용자 그룹 수: ", "User Groups": "사용자 그룹", "User ID": "사용자 ID", "User ID/Name": "사용자 ID/이름", "User Name": "사용자 이름", "User Num": "사용자 수", "User Num: ": "사용자 수: ", "User name can not be duplicated": "사용자 이름은 중복될 수 없습니다", "User need to change password": "사용자 암호를 변경해야 합니다", "Username": "사용자 이름", "Username or password is incorrect": "사용자 이름 또는 암호가 잘못되었습니다", "Users": "사용자", "Using cascading deletion, when the volume has snapshots, the associated snapshot will be automatically deleted first, and then the volume will be deleted, thereby improving the success rate of deleting the volume.": "cascading 삭제 기능을 사용하면, 볼륨에 스냅샷이 있을 경우 연관된 스냅샷이 먼저 자동으로 삭제되고, 그 다음에 볼륨이 삭제됩니다. 이를 통해 볼륨 삭제의 성공률을 높일 수 있습니다.", "Using server groups, you can create cloud hosts on the same/different physical nodes as much as possible to meet the affinity/non-affinity requirements of business applications.": "서버 그룹을 사용하면 비즈니스 애플리케이션의 선호도/비선호도 요구 사항을 충족하기 위해 최대한 동일하거나 다른 물리적 노드에 클라우드 호스트를 생성할 수 있습니다.", "Uzbekistan": "Uzbekistan", "VCPU (Core)": "VCPU (Core)", "VCPUs": "VCPUs", "VDI - VirtualBox compatible image format": "VDI - VirtualBox 호환 이미지 포맷", "VGPU": "VGPU", "VGPU (Core)": "VGPU (Core)", "VHD - VirtualPC compatible image format": "VHD - VirtualPC 호환 이미지 포맷", "VIF Details": "VIF 상세", "VIF Type": "VIF 타입", "VIR Domain Event": "", "VMDK - Hyper-V compatible image format": "VMDK - Hyper-V 호환 이미지 포맷", "VNC": "VNC", "VNIC Type": "VNIC 타입", "VPN": "VPN", "VPN EndPoint Groups": "VPN 엔드포인트 그룹", "VPN Gateways": "VPN 게이트웨이", "VPN Service": "VPN 서비스", "VPN Service ID": "VPN 서비스 ID", "VPNs": "VPN", "VRRP": "VRRP", "Valid": "유효", "Value": "값", "Values": "값", "Vanuatu": "Vanuatu", "Vatican City State (Holy See)": "Vatican City State (Holy See)", "Vendor Interface": "벤더 인터페이스", "Venezuela": "Venezuela", "Verifying": "확인 중", "Version": "버전", "Vietnam": "Vietnam", "View": "보기", "View Detail": "세부 정보 보기", "View Full Log": "전체 로그 보기", "View Rules": "규칙 보기", "View virtual adapters": "가상 어댑터 보기", "Virgin Islands (U.S.)": "Virgin Islands (U.S.)", "Virtual Adapter": "가상 어댑터", "Virtual Adapter ID": "가상 어댑터 ID", "Virtual LAN": "가상 LAN", "Virtual LANs": "가상 LAN", "Virtual Resource Overview": "가상 리소스 개요", "Virtual Resources Used": "가상 리소스", "Virtual adapter mainly used for binding instance and other operations, occupying the quota of the port.": "주로 바인딩 인스턴스 및 기타 작업에 사용되는 가상 어댑터로, 포트의 할당량을 차지합니다.", "Visibility": "가시성", "Visualization Compute Optimized Type with GPU": "GPU를 사용한 시각화 컴퓨팅 최적화 유형", "Volume": "볼륨", "Volume Backup": "볼륨 백업", "Volume Backup Capacity (GiB)": "", "Volume Backup Detail": "볼륨 백업 세부 정보", "Volume Backup Name": "볼륨 백업 이름", "Volume Backups": "볼륨 백업", "Volume Capacity (GiB)": "볼륨 용량 (GiB)", "Volume Detail": "볼륨 세부 정보", "Volume Driver": "볼륨 드라이버", "Volume ID": "볼륨 ID", "Volume ID/Name": "볼륨 ID/이름", "Volume Info": "볼륨 정보", "Volume Name": "볼륨 이름", "Volume Size": "볼륨 크기", "Volume Snapshot": "볼륨 스냅샷", "Volume Snapshot Detail": "볼륨 스냅샷 세부 정보", "Volume Snapshot Name": "볼륨 스냅샷 이름", "Volume Snapshots": "볼륨 스냅샷", "Volume Source": "볼륨 소스", "Volume Transfer": "볼륨 이전", "Volume Type": "볼륨 타입", "Volume Type Detail": "볼륨 타입 세부 정보", "Volume Types": "볼륨 타입", "Volumes": "볼륨", "Wallis And Futuna Islands": "Wallis And Futuna Islands", "Warn": "", "Warning": "", "Weight": "", "Weights": "", "Welcome": "안녕", "Western Sahara": "", "When auto-expand/close is enabled, if there is no operation in the pop-up window, the pop-up window will be closed automatically after { seconds } seconds, and it will be automatically expanded when the displayed content changes.": "", "When the computing service starts the recycling instance interval, the instance will be stored in the recycling bin after deletion, and will be retained according to the corresponding time interval. You can choose to restore it within this period. After successful recovery, the status of the instance is running and related resources remain unchanged.": "", "When the volume is \"bootable\" and the status is \"available\", it can be used as a startup source to create an instance.": "", "When you do online backup of the volume that has been bound, you need to pay attention to the following points:": "", "When you restore a backup, you need to meet one of the following conditions:": "", "When your Yaml file is a fixed template, variable variables can be stored in an environment variable file to implement template deployment. The parameters in the environment variable file need to match the parameters defined in the template file.": "", "Whether enable or not using the floating IP of cloud provider.": "", "Whether the Login Name can be used is up to the feasible configuration of cloud-init or cloudbase-init service in the image.": "", "Whether the boot device should be set only for the next reboot, or persistently.": "", "Which Network Interface provider to use when plumbing the network connections for this Node": "", "Windows": "", "Workdir": "", "Working Directory": "", "X86 Architecture": "X86 아키텍쳐", "YAML File": "YAML 파일", "Yemen": "", "Yes": "예", "Yes - Create a new system disk": "예 - 새 시스템 디스크를 만듭니다", "You are not allowed to delete policy \"{ name }\" used by firewalls: { firewalls }.": "", "You are not allowed to delete policy \"{ name }\".": "", "You are not allowed to delete router \"{ name }\".": "", "You are not allowed to delete rule \"{ name }\" in use.": "", "You are not allowed to delete rule \"{ name }\".": "", "You are not allowed to delete snapshot \"{ name }\", which is used by creating volume \"{volumes}\".": "", "You are not allowed to delete snapshot \"{ name }\".": "", "You are not allowed to jump to the console.": "", "You are not allowed to { action } \"{ name }\".": "", "You are not allowed to { action } {name}.": "", "You are not allowed to {action}, instance: {name}.": "", "You are not allowed to {action}.": "", "You can manually specify a physical node to create an instance.": "", "You don't have access to get {name}.": "", "You may update the editable properties of the RBAC policy here.": "", "Yugoslavia": "", "Zambia": "", "Zimbabwe": "", "Zone": "", "Zone ID": "", "Zone ID/Name": "영역 ID/이름", "Zone Name": "", "Zones Detail": "", "abandon stack": "", "add access rule": "", "add network": "네트워크 추가", "add router": "라우터 추가", "all": "모든", "an optional string field to be used to store any vendor-specific information": "", "application credential": "", "associate floating ip": "유동 IP 연결", "attach interface": "인터페이스 연결", "authorized by group ": "", "auto": "", "auto_priority": "", "availability zones": "", "available": "사용 가능", "bare metal node": "", "bare metal nodes": "", "be copied": "", "be cut": "", "be deleted": "", "be rebooted": "", "be recovered": "", "be released": "", "be soft rebooted": "", "be started": "", "be stopped": "", "capsules": "", "certificate": "", "cidr": "", "cinder services": "", "clusters": "", "clustertemplates": "", "compute hosts": "compute 호스트", "compute services": "compute 서비스", "configurations": "", "confirm resize or migrate": "", "connect subnet": "", "container objects": "", "containers": "컨테이너", "create DSCP marking rule": "", "create a new network/subnet": "", "create a new security group": "보안 그룹 생성", "create allowed address pair": "", "create bandwidth limit rule": "", "create baremetal node": "", "create default pool": "", "create encryption": "암호화 생성", "create firewall policy": "", "create flavor": "flavor 생성", "create instance snapshot": "인스턴스 스냅샷 생성", "create ipsec site connection": "", "create network": "네트워크 생성", "create router": "라우터 생성", "create share": "", "create share group": "", "create share group type": "", "create share network": "", "create share type": "", "create stack": "", "create volume": "볼륨 생성", "create volume snapshot": "볼륨 스냅샷 생성", "create volume type": "볼륨 타입 생성", "create vpn": "vpn 생성", "create vpn endpoint group": "", "create vpn ike policy": "vpn ike 정책 생성", "create vpn ipsec policy": "vpn ipsec 정책 생성", "data": "데이터", "database backups": "", "database instances": "", "delete": "삭제", "delete allowed address pair": "", "delete application credential": "어플리케이션 증명서 삭제", "delete bandwidth egress rules": "", "delete bandwidth ingress rules": "", "delete certificate": "", "delete container": "", "delete default pool": "", "delete domain": "도메인 삭제", "delete dscp marking rules": "", "delete firewall": "", "delete flavor": "flavor 삭제", "delete group": "그룹 삭제", "delete host": "", "delete image": "이미지 삭제", "delete instance": "인스턴스 삭제", "delete instance snapshot": "인스턴스 스냅샷 삭제", "delete ipsec site connection": "", "delete ironic instance": "", "delete keypair": "키 페어 삭제", "delete listener": "", "delete load balancer": "로드 밸런서 삭제", "delete member": "멤버 삭제", "delete network": "네트워크 삭제", "delete policy": "", "delete port forwarding": "", "delete project": "프로젝트 삭제", "delete qos policy": "QoS 정책 삭제", "delete role": "역할 삭제", "delete router": "라우터 삭제", "delete rule": "", "delete segments": "", "delete stack": "", "delete static route": "정적 경로 삭제", "delete subnet": "서브넷 삭제", "delete user": "사용자 삭제", "delete virtual adapter": "", "delete volume": "볼륨 삭제", "delete volume backup": "볼륨 백업 삭제", "delete volume snapshot": "볼륨 스냅샷 삭제", "delete vpn": "vpn 삭제", "delete vpn IKE policy": "", "delete vpn IPsec policy": "", "delete vpn endpoint groups": "", "description": "설명", "detach instance": "인스턴스 바인딩 해제", "detach security group": "보안 그룹 연결 해제", "disable cinder service": "", "disable compute service": "", "disable neutron agent": "", "disassociate floating ip": "유동 IP 연결 해제", "disconnect subnet": "", "dns zones": "", "domain": "도메인", "domains": "도메인", "download image": "이미지 다운로드", "e.g. 2001:Db8::/48": "", "edit baremetal node": "", "edit default pool": "", "edit health monitor": "", "edit image": "이미지 편집", "edit instance snapshot": "인스턴스 스냅샷 편집", "edit member": "멤버 편집", "edit system permission": "", "egress": "", "enable cinder service": "", "enable compute service": "", "enable neutron agent": "", "external port": "외부 포트", "external ports": "외부 포트", "extra specs": "추가 사양", "firewall": "", "firewall policies": "", "firewall rule": "", "firewall rules": "", "firewalls": "", "flavor": "flavor", "floating ip": "유동 ip", "floating ips": "유동 ip", "heat services": "", "host aggregates": "호스트 집합", "hosts": "", "hypervisor": "", "image": "", "images": "", "in": "", "ingress": "", "insert": "", "insert rule": "", "instance": "", "instance snapshot": "인스턴스 스냅샷", "instance snapshots": "인스턴스 스냅샷", "instance: {name}.": "인스턴스: {name}.", "instances": "인스턴스", "internal port": "", "internal ports": "", "ipsec site connection": "", "jump to the console": "", "keypair": "캐 페어", "keypairs": "캐 페어", "labels": "", "list page": "", "listener": "", "listeners": "", "live migrate": "", "load balancer": "로드 밸런서", "lock instance": "", "manage ports": "", "manage qos spec": "QOS 스펙 관리", "manage resource types": "리소스 타입 관리", "message": "메시지", "message.reason": "", "metadata": "메타데이터", "migrate": "이동", "modify instance tags": "인스턴스 태그 수정", "modify project tags": "프로젝트 태그 수정", "network": "네트워크", "networks": "네트워크", "neutron agent": "", "neutron agents": "", "ns1.example.com admin.example.com 2013022001 86400 7200 604800 300 <ul><li>The primary name server for the domain, which is ns1.example.com or the first name server in the vanity name server list.</li><li>The responsible party for the domain: admin.example.com.</li><li>A timestamp that changes whenever you update your domain.</li><li>The number of seconds before the zone should be refreshed.</li><li>The number of seconds before a failed refresh should be retried.</li><li>The upper limit in seconds before a zone is considered no longer authoritative.</li><li>The negative result TTL (for example, how long a resolver should consider a negative result for a subdomain to be valid before retrying).</li></ul>": "", "open external gateway": "", "out": "", "paste files to folder": "", "pause instance": "", "phone": "", "please select network": "네트워크 선택", "please select subnet": "서브넷 선택", "policy": "", "port": "포트", "port forwarding": "", "port forwardings": "", "port groups": "", "ports": "포트", "project": "프로젝트", "projects": "프로젝트", "qemu_guest_agent enabled": "", "qoS policy": "QoS 정책", "qos specs": "QOS 스펙", "quota set to -1 means there is no quota limit on the current resource": "", "read": "", "reboot instance": "인스턴스 다시 시작", "rebuild instance": "", "receive": "", "recordsets": "", "recover instance": "", "recycle bins": "", "release fixed ip": "", "remove network": "네트워크 삭제", "remove router": "라우터 삭제", "remove rule": "", "reserved_host": "", "resize": "크기 변경", "resume instance": "인스턴스 재시작", "revert resize or migrate": "", "rh_priority": "", "role": "역할", "roles": "역할", "router": "라우터", "routers": "라우터", "security group": "보안 그룹", "security group rules": "보안 그룹 규칙", "security groups": "보안 그룹", "segments": "", "select an existing port": "", "server group": "보안 그룹", "server groups": "보안 그룹", "services": "서비스", "settings": "설정", "share": "", "share access rules": "", "share group": "", "share group type": "", "share groups": "", "share instance": "", "share instances": "", "share metadata": "", "share network": "", "share server": "", "share servers": "", "share type": "", "share types": "", "shelve instance": "", "smtp.example.com": "", "soft reboot instance": "인스턴스 소프트 다시 시작", "stack": "", "stack events": "Stack 이벤트", "stack resources": "Stack 리소스", "stacks": "stack", "start instance": "", "static routers": "", "stop instance": "", "storage backend": "", "subnet": "서브넷", "subnets": "서브넷", "suspend instance": "", "the Republic of Abkhazia": "", "the folder is not empty": "", "the policy is in use": "", "the router has connected subnet": "", "the vpn gateway is in use": "", "time / 24h": "", "to delete": "", "transmit": "", "unlock instance": "인스턴스 잠금 해제", "unpause instance": "", "unshelve instance": "", "update": "업데이트", "update status": "업데이트 상태", "update template": "템플릿 업데이트", "used": "사용됨", "user": "사용자", "user group": "사용자 그룹", "user groups": "사용자 그룹", "users": "사용자", "vCPUs": "", "vCPUs and ram are not used for bare metal scheduling": "", "volume": "볼륨", "volume backup": "볼륨 백업", "volume backups": "볼륨 백업", "volume capacity": "볼륨 용량", "volume snapshot": "볼륨 스냅샷", "volume snapshots": "볼륨 스냅샷", "volume type": "볼륨 타입", "volume type qos": "볼륨 타입 qos", "volume type {type}": "볼륨 타입 {type}", "volume type {type} capacity": "볼륨 타입 {type} 용량", "volume types": "볼륨 타입", "volumes": "볼륨", "vpn IKE policy": "vpn IKE 정책", "vpn IPsec policy": "vpn IPsec 정책", "vpn endpoint groups": "", "vpn services": "", "write": "", "{ name } Format Error (e.g. *********** or ***********/24)": "", "{ name } Format Error (e.g. FE80:0:0:0:0:0:0:1 or FE80:0:0:0:0:0:0:1/10)": "", "{ size } GiB": "", "{ size } KiB": "", "{ size } MiB": "", "{ size } TiB": "", "{ size } bytes": "", "{action} successfully, instance: {name}.": "", "{action} successfully.": "성공적으로 {action}.", "{action} {name} successfully.": "{name} 성공적으로 {action}.", "{hours} hours {leftMinutes} minutes {leftSeconds} seconds": "", "{interval, plural, =1 {one day} other {# days} } later delete": "", "{interval, plural, =1 {one hour} other {# hours} } later delete": "", "{interval, plural, =1 {one minute} other {# minutes} } later delete": "", "{interval, plural, =1 {one week} other {# weeks} } later delete": "", "{minutes} minutes {leftSeconds} seconds": "", "{name} type": "{name} 타입", "{name} type capacity": "", "{name} type capacity (GiB)": "", "{name} type snapshots": "", "{name} {id} could not be found.": "", "{number} {resource}": "", "{pageSize} items/page": "", "{seconds} seconds": ""}