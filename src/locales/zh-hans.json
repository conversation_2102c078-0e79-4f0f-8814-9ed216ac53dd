{"3600": "", " You can go to the console to ": "您可以前往控制台 ", "\"Shared\" volume can be mounted on multiple instances": "“共享”云硬盘类型的云硬盘，可挂载到多台云主机上", "\"v=spf1 ipv4=********* include:examplesender.email +all\" <ul><li><b>v=spf1:</b> Tells the server that this contains an SPF record. Every SPF record must begin with this string.</li> <li><b>Guest List:</b> Then comes the “guest list” portion of the SPF record or the list of authorized IP addresses. In this example, the SPF record is telling the server that ipv4=********* is authorized to send emails on behalf of the domain.</li> <li><b>include:examplesender.net:</b> is an example of the include tag, which tells the server what third-party organizations are authorized to send emails on behalf of the domain. This tag signals that the content of the SPF record for the included domain (examplesender.net) should be checked and the IP addresses it contains should also be considered authorized. Multiple domains can be included within an SPF record but this tag will only work for valid domains.</li><li><b>-all:</b> Tells, the server that addresses not listed in the SPF record are not authorized to send emails and should be rejected.</li></ul>": "\"v=spf1 ipv4=********* include:examplesender.email +all\" <ul><li><b>v=spf1:</b> 告诉服务器这包含SPF记录。每个SPF记录必须以该字符串开头。</li> <li><b>访客列表：</b> 是 SPF 记录的“访客列表”或授权 IP 地址列表。 在此示例中，SPF 记录告诉服务器 ipv4=********* 被授权代表域发送电子邮件。</li> <li><b>include:examplesender.net: </b> 是 include 标记的示例，它告诉服务器哪些第三方组织有权代表域发送电子邮件。 此标记表示应检查所包含域 (examplesender.net) 的 SPF 记录内容，并且它包含的 IP 地址也应被视为已授权。 SPF 记录中可以包含多个域，但此标记仅适用于有效域。</li><li><b>-all：</b> SPF 记录中未列出的地址的服务器无权发送电子邮件，应被拒绝。</li></ul>", "'ip' rule represents IPv4 or IPv6 address, 'cert' rule represents TLS certificate, 'user' rule represents username or usergroup, 'cephx' rule represents ceph auth ID.": "'IP' 规则代表 IPv4 或 IPv6 地址，'Cert' 规则代表TLS 证书，'用户' 规则代表用户名或用户组，'Cephx' 规则代表 ceph auth ID。", "-1 means no connection limit": "-1表示无连接限制", ".": "。", "0 iodef mailto:<EMAIL> <ul><li><b>0:</b> is flag. An unsigned integer between 0-255.</li> <li><b>iodef:</b> An ASCII string that represents the identifier of the property represented by the record.<br />Available Tags: \"issue\", \"issuewild\", \"iodef\"</li><li><b>mailto:<EMAIL>:</b> The value associated with the tag.</li></ul>": "0 iodef mailto:<EMAIL> <ul><li><b>0:</b> 是标志。 0-255 之间的无符号整数。</li> <li><b>iodef：</b> 一个 ASCII 字符串，表示记录所表示的属性的标识符。<br />可用标签：\"issue\", \"issuewild\", \"iodef\"</li><li><b>mailto:<EMAIL>:</b> 与标记关联的值。</li></ul>", "1. The backup can only capture the data that has been written to the volume at the beginning of the backup task, excluding the data in the cache at that time.": "1. 备份只能捕获在备份任务开始时已经写入磁盘的数据，不包括当时位于缓存的数据。", "1. The name of the custom resource class property should start with CUSTOM_, can only contain uppercase letters A ~ Z, numbers 0 ~ 9 or underscores, and the length should not exceed 255 characters (for example: CUSTOM_BAREMETAL_SMALL).": "1. 自定义资源属性的命名应该以 CUSTOM_ 开头、只能包含大写字母A ~ Z、数字0 ~ 9或下划线、长度不超过255个字符（比如：CUSTOM_BAREMETAL_SMALL）。", "1. The name of the trait should start with CUSTOM_, can only contain uppercase letters A ~ Z, numbers 0 ~ 9 or underscores, and the length should not exceed 255 characters (for example: CUSTOM_TRAIT1).": "1. 特性的命名应该以 CUSTOM_ 开头、只能包含大写字母A ~ Z、数字0 ~ 9或下划线、长度不超过255个字符（比如：CUSTOM_TRAIT1）。", "1. The volume associated with the backup is available.": "1. 备份关联的云硬盘处于可用状态。", "1. You can create {resources} using ports or port ranges.": "1. 可以使用端口或端口范围创建{resources}。", "10 0 5060 server1.example.com. <ul><li>\"10\" is the priority of the record. The lower the value, the higher the priority.</li><li>0 is the weight of the record. This is the weight of which this record has a chance to be used when there are multiple matching SRV records of the same priority.</li><li>5060 is the port of the record. This specifies the port on which the application or service is running.</li> <li>server1.example.com is the target of the record. This specifies the domain of the application or service the record is for. SRV records must specify a target which is either an A record or AAAA record, and may not use CNAME records.</li></ul>": "10 0 5060 server1.example.com。 <ul><li>\"10\" 是记录的优先级。值越小，优先级越高。</li><li>0是记录的权重。这是当存在多个相同优先级的匹配SRV记录时，该记录有机会被使用的权重。</li><li>5060是该记录的端口。这指定了应用程序或服务正在运行的端口。</li> <li>server1.example.com 是记录的目标。这指定了记录所针对的应用程序或服务的域。SRV 记录必须指定目标，该目标可以是 A 记录或 AAAA 记录，但不能使用 CNAME 记录。</li></ul>", "10 mail.example.com <ul><li><b>10:</b> Priority</li> <li><b>mail.example.com:</b> Value</li></ul>": "10 mail.example.com <ul><li><b>10：</b> 优先级</li> <li><b>mail.example.com：</b> 值</li></ul >", "10s": "10秒", "1D": "1天", "1H": "1小时", "1min": "1分钟", "2. In the same protocol, you cannot create multiple {resources} for the same source port or source port range.": "2. 相同协议下，同一个源端口或源端口范围不可创建多个{resources}。", "2. The trait of the scheduled node needs to correspond to the trait of the flavor used by the ironic instance; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all the necessary traits (for example, the ironic instance which use the flavor that has CUSTOM_TRAIT1 as a necessary trait, can be scheduled to the node which has the trait of CUSTOM_TRAIT1).": "2. 被调度节点的特性需要与裸机实例使用的云主机类型的特性对应；通过给裸机实例注入必需特性，计算服务将只调度实例到具有所有必需特性的裸金属节点（比如：调度节点的有 CUSTOM_TRAIT1 特性， 云主机类型添加CUSTOM_TRAIT1为必要特性，可以调度到此节点）。", "2. The volume associated with the backup has been mounted, and the instance is shut down.": "2. 备份关联的云硬盘已被挂载，且云主机处于关机状态。", "2. To ensure the integrity of the data, it is recommended that you suspend the write operation of all files when creating a backup.": "2. 为了保证数据的完整性，建议您在创建备份时暂停所有文件的写操作。", "2. You can customize the resource class name of the flavor, but it needs to correspond to the resource class of the scheduled node (for example, the resource class name of the scheduling node is baremetal.with-GPU, and the custom resource class name of the flavor is CUSTOM_BAREMETAL_WITH_GPU=1).": "2. 你可以自定义云主机类型的资源类名称，但需要与被调度节点的资源类对应；（比如：调度节点的资源类名称为 baremetal.with-GPU，云主机类型的自定义资源类名称为CUSTOM_BAREMETAL_WITH_GPU）。", "3. When using a port range to create a port mapping, the size of the external port range is required to be the same as the size of the internal port range. For example, the external port range is 80:90 and the internal port range is 8080:8090.": "3. 使用端口范围创建端口映射时，要求源端口范围大小与目标端口范围大小一致，如：源端口范围为80:90，目标端口范围为8080:8090。", "4 2 123456789abcdef67890123456789abcdef67890123456789abcdef123456789 <ul> <li><b>4 is Algorithm:</b> Algorithm (0: reserved; 1: RSA; 2: DSA, 3: ECDSA; 4: Ed25519; 6:Ed448)</li> <li><b>2 is Type:</b> Algorithm used to hash the public key (0: reserved; 1: SHA-1; 2: SHA-256)</li> <li><b>Last parameter is Fingerprint:</b> Hexadecimal representation of the hash result, as text</li> </ul>": "4 2 123456789abcdef67890123456789abcdef67890123456789abcdef123456789 <ul> <li><b>4 是算法：</b> 算法（0：保留；1：RSA；2：DSA；3：ECDSA；4：Ed25519；6：Ed第448章）</li> <li><b>2 是类型：</b>用于散列公钥的算法（0：保留；1：SHA-1；2：SHA-256）</li> <li><b>最后一个参数是指纹：</b>哈希结果的十六进制表示，作为文本</li></ul>", "4. When you use a port range to create {resources}, multiple {resources} will be created in batches. ": "4. 使用端口范围创建{resources}时，会批量创建多个{resources}。", "5min": "5分钟", "8 to 16 characters, at least one uppercase letter, one lowercase letter, one number.": "8个到16个字符，至少一个大写字母，一个小写字母，一个数字。", "8 to 32 characters, at least one uppercase letter, one lowercase letter, one number and one special character.": "8个到32个字符，至少一个大写字母，一个小写字母，一个数字和一个特殊字符。", "<username> or <username>@<domain>": "<用户名> 或 <用户名>@<用户域名>", "A command that will be sent to the container": "将发送到容器的命令", "A container with the same name already exists": "已存在同名容器", "A dynamic scheduling algorithm that estimates the server load based on the number of currently active connections. The system allocates new connection requests to the server with the least number of current connections. Commonly used for long connection services, such as database connections and other services.": "通过当前活跃的连接数来估计服务器负载情况的一种动态调度算法，系统把新的连接请求分配给当前连接数目最少的服务器。常用于长连接服务，例如数据库连接等服务。", "A host aggregate can be associated with at most one AZ. Once the association is established, the AZ cannot be disassociated.": "一个主机集合最多可以与一个AZ建立关联，一旦建立了关联，无法再取消关联AZ。", "A public container will allow anyone to use the objects in your container through a public URL.": "一个公有容器会允许任何人通过公共 URL 去使用您容器里面的对象。", "A rule specified before insertion or after insertion a rule. If both are not specified, the new rule is inserted as the first rule of the policy.": "指定在某条规则之前插入，或在某条规则之后插入。如果二者均未被指定，则插入到策略规则首位。", "A snapshot is an image which preserves the disk state of a running instance, which can be used to start a new instance.": "云主机当前状态的磁盘数据保存，创建镜像文件，以备将来启动新的云主机使用。", "A template is a YAML file that contains configuration information, please enter the correct format.": "模板是包含配置信息的YAML文件， 请输入正确的格式。", "A template is a YAML file that contains configuration information.": "模板是包含配置信息的YAML文件。", "ADMINISTRATOR": "管理员", "ADOPT COMPLETE": "采用完成", "AH": "", "AKI - Amazon kernel image format": "AKI - Amazon 内核图像格式", "ALLOW": "允许", "AMI - Amazon server image format": "AMI - Amazon 服务器图像格式", "ANY": "任何", "API Address": "API地址", "ARI - Amazon ramdisk image format": "ARI - Amazon ramdisk 映像格式", "ARM Architecture": "ARM架构", "Abandon Stack": "废弃堆栈", "Abandoning this stack will preserve the resources deployed by the stack.": "废弃此堆栈将保留堆栈部署的资源。", "Abort Upload": "取消上传", "Accept Volume Transfer": "接受云硬盘转让", "Access Control": "访问控制", "Access Key": "访问密钥", "Access Level": "访问级别", "Access Rules": "访问规则", "Access Rules Status": "访问规则状态", "Access To": "访问", "Access Type": "访问类型", "Access Type Setting": "访问类型设置", "Action": "操作", "Action Logs": "操作日志", "Active": "运行中", "Active Status": "运行", "Add": "添加", "Add Access Rule": "添加访问规则", "Add Custom Metadata": "添加自定义元数据", "Add Data Disks": "添加数据盘", "Add Environment Variable": "添加环境变量", "Add Exposed Ports": "添加服务端口", "Add External Members": "添加外部成员", "Add Extra Info": "添加额外信息", "Add Extra Spec": "添加额外规格", "Add Host": "添加主机", "Add IP": "增加IP", "Add Label": "添加标签", "Add Member": "添加成员", "Add Metadata": "添加元数据", "Add NUMA Node": "添加NUMA节点", "Add Network": "添加网络", "Add Policy": "增加策略", "Add Property": "添加属性", "Add Router": "添加路由器", "Add Virtual LAN": "添加虚拟网卡", "Add hosts to the aggregate or remove hosts from it. Hosts can be in multiple aggregates.": "增加主机到这个集合。主机可以加入多个集合。", "Add network": "添加网络", "Add scheduler hints": "添加调度程序提示", "Additional Labels": "附加标签", "Additional routes announced to the instance, one entry per line(e.g. *************/24,***********)": "云主机额外路由，每行一条(例如: *************/24,***********)", "Additional routes announced to the instance, one entry per line(e.g. {ip})": "云主机额外路由，每行一条(例如: {ip})", "Address": "地址", "Address Record": "地址记录", "Addresses": "地址", "Admin State": "管理状态", "Admin State Up": "管理员状态", "Admin Status": "管理状态", "Administrator": "管理平台", "Adopt Complete": "配置完成", "Adopt Failed": "配置失败", "Adopt In Progress": "正在启用配置", "Advanced": "高级", "Advanced Options": "高级选项", "Advanced Params": "高级参数", "Affiliated Domain": "所属域", "Affiliated Domain ID/Name": "所属域ID/名称", "Affinity": "亲和组", "Affinity (mandatory):": "亲和(强制)：", "Affinity (not mandatory):": "亲和 (非强制)：", "Afghanistan": "阿富汗", "After attaching interface, you may need to login the instance to update the network interface configuration and restart the network service.": "挂载网卡后，您可能需要登录到云主机更新网卡配置并且重启网络服务。", "After disable the compute service, the new instance will not schedule to the compute node.": "禁用计算服务之后，新的云主机不会调度到该计算节点。", "After shelving, the instance will be shut down, resources will be released, and the snapshot will be saved to Glance. This will take about a few minutes, please be patient. You also can choose to unshelve to restore the instance.": "归档后会关闭云主机，释放资源，并将快照保存到 Glance ，这大约需要数分钟时间，请耐心等待。在归档之后您也可以选择取消归档来恢复这台云主机。", "After the share is expanded, the share cannot be reduced.": "扩容后，该共享不可再缩小。", "After the volume is expanded, the volume cannot be reduced.": "扩容云硬盘后，云硬盘不可再缩小。", "Agent": "", "Agree to force shutdown": "同意强制关机", "Albania": "阿尔巴尼亚", "Algeria": "阿尔及利亚", "All": "全部", "All Flavors": "全部架构", "All ICMP": "所有ICMP协议", "All Images": "全部镜像", "All Networks": "所有网络", "All Port": "所有端口", "All Proto": "所有协议", "All QoS Policies": "所有QoS策略", "All TCP": "所有TCP协议", "All UDP": "所有UDP协议", "All data downloaded.": "所有数据已完成下载。", "All network segments are indicated by \"*\", not \"0.0.0.0/0\"": "所有网络段用 “*” 表示，协议不支持 “0.0.0.0/0” 表示所有网段", "Allocate IP": "申请IP", "Allocation Pools": "分配地址池", "Allowed Address Pairs": "可用地址对", "Allowed Host": "可用主机", "Always": "总是", "American Samoa": "萨摩亚", "An object with the same name already exists": "已存在同名对象", "Andorra": "安道尔共和国", "Angola": "安哥拉", "Anguilla": "安圭拉岛", "Anti-Affinity": "反亲和组", "Anti-affinity (mandatory):": "反亲和(强制)：", "Anti-affinity (not mandatory):": "反亲和 (非强制)：", "Antigua and Barbuda": "安提瓜和巴布达", "Any": "任意", "Any(Random)": "任意（随机）", "Application Credentials": "应用凭证", "Application Template": "应用模板", "Apply Latency(ms)": "应用延迟(毫秒)", "Applying": "申请中", "Arch": "", "Architecture": "架构", "Are you sure set the project { project } as the default project? User login is automatically logged into the default project.": "确认设置项目 { project } 为默认项目吗？用户登录会自动登录到默认项目。", "Are you sure to cancel transfer volume { name }? ": "确认要取消{ name }云硬盘转让？", "Are you sure to delete instance { name }? ": "确认要删除云主机{ name } ？", "Are you sure to delete volume { name }? ": "确认要删除云硬盘{ name } ？", "Are you sure to download data?": "确认要下载数据？", "Are you sure to forbidden domain { name }? Forbidden the domain will have negative effect, and users associated with the domain will not be able to log in if they are only assigned to the domain": "确认要禁用域{name}？禁用域后，该域下面的项目和用户都会被禁止，用户将无法登陆", "Are you sure to forbidden project { name }? Forbidden the project will have negative effect, and users associated with the project will not be able to log in if they are only assigned to the project": "确认要禁用项目{name}？禁用项目后将会产生负面影响，项目关联的用户如果只分配给该项目，将无法登陆", "Are you sure to forbidden user { name }? Forbidden the user will not allow login in ": "确定禁用用户{name}? 禁用后用户将无法登陆", "Are you sure to jump directly to the console? The console will open in a new page later.": "您确定要直接跳转到控制台吗？控制台稍后会在新页面中打开。", "Are you sure to remove the default project?": "确认移除默认项目吗？", "Are you sure to shelve instance { name }? ": "确认要归档云主机{ name }？", "Are you sure to { action } {name}?": "确认{ action }{name}？", "Are you sure to {action} (Host: {name})?": "确认{action} (主机: {name})?", "Are you sure to {action} (Segment: {name})?": "确认{action} (分组: {name})?", "Are you sure to {action} (instance: {name})?": "确认{ action }（实例名称：{name}）？", "Are you sure to {action}?": "确认{action}？", "Are you sure to {action}? (Record Set: {name} - {id})": "确定要{action}吗？ （记录集：{name} - {id}）", "Are you sure to {action}? (Zone: {name})": "确定要{action}吗？ （区域：{名称}）", "Argentina": "阿根廷", "Armenia": "亚美尼亚", "Aruba": "阿鲁巴", "Associate": "关联", "Associate Floating IP": "绑定浮动IP", "Associate IP": "关联IP", "Associate Network": "关联网络", "Associated Ports": "关联端口", "Associated QoS Spec ID": "关联QoS规格ID", "Associated QoS Spec ID/Name": "关联QoS规格ID/名称", "Associated Resource": "关联资源", "Associated Resource Types": "关联资源类型", "Associated Resources": "关联资源", "Associations": "关联", "Attach": "挂载", "Attach Instance": "绑定云主机", "Attach Interface": "挂载网卡", "Attach Network": "绑定网络", "Attach Security Group": "绑定安全组", "Attach USB": "挂载USB", "Attach Volume": "挂载云硬盘", "Attach volume": "挂载云硬盘", "Attached Device": "连接设备", "Attached To": "挂载到", "Attaching": "挂载中", "Attachments Info": "挂载信息", "Attributes": "属性", "Audited": "已审计", "Australia": "澳大利亚", "Austria": "奥地利", "Auth Algorithm": "授权算法", "Auth Key": "密钥", "Auto": "自动", "Auto Healing": "自动修复", "Auto Inspect": "自动检测", "Auto Scaling": "自动缩放", "Auto allocate mac address": "自动分配MAC地址", "Auto scaling feature will be enabled": "将启用自动缩放功能", "Automatically Assigned Address": "自动分配地址", "Automatically repair unhealhty nodes": "自动修复不健康的节点", "Availability Zone": "可用域", "Availability Zone Hints": "预期可用域", "Availability Zone Info": "可用域信息", "Availability Zone Name": "可用域名称", "Availability Zones": "可用域", "Availability zone refers to a physical area where power and network are independent of each other in the same area. In the same region, the availability zone and the availability zone can communicate with each other in the intranet, and the available zones can achieve fault isolation.": "可用区是指在同一地域内，电力和网络互相独立的物理区域。在同一地域内可用区与可用区之间内网互通，可用区之间能做到故障隔离。", "Available": "可用", "Available Zone": "可用域", "Average PGs per OSD": "每个OSD平均PG数量", "Awaiting Transfer": "等待转让", "Azerbaijan": "阿塞拜疆", "BLOCK I/O(B)": "块 I/O(B)", "Back": "返回", "Back End": "后端", "Back to Home": "返回首页", "Back to login page": "返回登录页", "Backend": "后端", "Backend Name": "后端名称", "Backing Up": "备份中", "Backup": "备份", "Backup Detail": "备份详情", "Backup File": "备份文件", "Backup File Location": "备份文件地址", "Backup Mode": "备份方式", "Backups": "备份", "Backups & Snapshots": "备份和快照", "Bad Gateway (code: 502) ": "无效网关（错误码：502）", "Bahamas": "巴哈马", "Bahrain": "巴林", "BandWidth Limit Egress": "出方向带宽限制", "BandWidth Limit Ingress": "入方向带宽限制", "Bandwidth limit": "带宽限制", "Bangladesh": "孟加拉国", "Barbados": "巴巴多斯", "Bare Metal": "裸机", "Bare Metal Enroll": "裸机注册", "Bare Metal Node Detail": "裸机节点详情", "Bare Metal Nodes": "裸机节点", "BareMetal Parameters": "裸机参数", "Base Config": "基础配置", "Base Info": "基本信息", "Basic Parameters": "基本参数", "Batch Allocate": "批量申请", "Before deleting the project, it is recommended to clean up the resources under the project.": "删除项目前，建议先清理项目下的资源！", "Belarus": "白俄罗斯", "Belgium": "比利时", "Belize": "伯利兹城", "Benin": "贝宁", "Bermuda": "百慕大", "Bhutan": "不丹", "Big Data": "大数据型", "Bind Device": "绑定设备", "Bind Device Type": "绑定设备类型", "Bind Resource": "绑定资源", "Bind Resource Name": "绑定资源名称", "Binding": "绑定", "Binding Groups": "绑定的用户组", "Binding Instance": "绑定云主机", "Binding Profile": "绑定配置文件", "Binding Users": "绑定的用户", "Blank Volume": "空白盘", "Block Device Mapping": "创建块设备中", "Block Migrate": "块迁移", "Block Storage Services": "块存储服务", "Blocked": "", "Bolivia": "玻利维亚", "Boot Device": "引导设备", "Boot From Volume": "从卷启动", "Boot Interface": "Boot接口", "Boot Mode of BIOS": "BIOS启动模式", "Bootable": "可启动", "Bootable Volume": "可启动云硬盘", "Bosnia and Herzegovina": "波斯尼亚和黑塞哥维那", "Both of Frontend and Backend": "前后端", "Botswana": "博茨瓦纳", "Brazil": "巴西", "British Indian Ocean Territory": "英属印度洋领地", "Brunei Darussalam": "文莱达鲁萨兰国", "Build": "创建中", "Building": "创建中", "Bulgaria": "保加利亚", "Burkina Faso": "布基纳法索", "Burst limit": "突发限制", "Burundi": "布隆迪", "By default, for security reasons, application credentials are forbidden from being used for creating or destructing additional application credentials or keystone trusts. If your application credential needs to be able to perform these actions, check unrestricted.": "默认情况下，出于安全原因，禁止将应用程序凭据用于创建或销毁其他应用程序凭据或 keystone 信任。如果您的应用程序凭据需要能够执行这些操作，请选中不受限制。", "CA Certificate": "CA证书", "CA Certificates": "CA证书", "CHECK COMPLETE": "检查完成", "CIDR": "网络地址", "CIDR Format Error(e.g. ***********/24, 2001:DB8::/48)": "CIDR格式错误（如：***********/24, 2001:DB8::/48）", "CIFS": "CIFS", "CMD": "运行命令(CMD)", "COE": "容器编排引擎", "COE Version": "容器编排引擎版本", "CPU": "CPU", "CPU %": "CPU使用率(%)", "CPU (Core)": "CPU (核)", "CPU Arch": "CPU架构", "CPU Cores": "CPU核数", "CPU Policy": "CPU策略", "CPU Thread Policy": "CPU线程策略", "CPU Usage(%)": "CPU使用率(%)", "CPU Usages (Core)": "CPU用量 (核)", "CPU value is { cpu }, NUMA CPU value is { totalCpu }, need to be equal. ": "CPU核数是 { cpu }，NUMA节点的CPU核数是{ totalCpu }，需要一致。", "CPU(Core)": "CPU（核数）", "CREATE COMPLETE": "创建完成", "CREATE FAILED": "创建失败", "CREATE IN PROGRESS": "创建中", "Cache Service": "缓存服务", "Cameroon": "喀麦隆", "Can add { number } {name}": "还可添加 { number } {name}", "Canada": "加拿大", "Cancel": "取消", "Cancel Download": "取消下载", "Cancel Select": "取消选择", "Cancel Transfer": "取消云硬盘转让", "Cancel download successfully.": "取消下载成功。", "Cancel upload successfully.": "取消上传成功。", "Canonical Name Record": "规范名称记录", "Capacity & Type": "容量和类型", "Capacity (GiB)": "容量 (GiB)", "Cape Verde": "佛得角", "Capsule Detail": "集合详情", "Capsule Type": "集合类型", "Capsules": "集合", "Cascading deletion": "联级删除", "Cast Rules To Read Only": "规则强制只读", "Category": "分类", "Cayman Islands": "开曼群岛", "CentOS": "", "Central African Republic": "中非共和国", "CephFS": "", "Cephx": "", "Cert": "", "Certificate Authority Authorization Record": "证书颁发机构授权记录", "Certificate Content": "证书内容", "Certificate Detail": "证书详情", "Certificate Name": "证书名称", "Certificate Type": "证书类型", "Certificates": "证书", "Chad": "乍得", "Change Password": "修改密码", "Change Type": "修改类型", "Change password": "修改密码", "Change type": "修改类型", "Changed Node Count": "修改后节点数量", "Channel": "信道", "Chassis ID": "机架ID", "Check Can Live Migrate Destination": "检查可以热迁移目标", "Check Can Live Migrate Source": "检查可以热迁移源", "Check Complete": "检查完成", "Check Failed": "检查失败", "Check In Progress": "正在检查", "Checksum": "校验和", "Chile": "智利", "China": "中国大陆", "Choose a Network Driver": "选择网络驱动程序", "Choose a host to live migrate instance to. If not selected, the scheduler will auto select target host.": "选择计算节点来热迁移云主机，如果没有选择，调度器会自动选择目标计算节点。", "Choose a host to migrate instance to. If not selected, the scheduler will auto select target host.": "选择计算节点来迁移云主机，如果没有选择，调度器会自动选择目标计算节点。", "Choosing a QoS policy can limit bandwidth and DSCP": "选择QoS策略可以限制带宽和DSCP", "Christmas Island": "圣延岛", "Cidr": "网段", "Cinder Service": "存储服务", "Cipher": "", "Clean Failed": "清除失败", "Clean Wait": "等待清除", "Cleaning": "清除中", "Clear Gateway": "清除网关", "Clear selected": "清空选中", "Click To View": "点击查看", "Click here for filters.": "筛选", "Click to Upload": "点击上传文件", "Click to show detail": "点击查看详情", "Clone Volume": "克隆云硬盘", "Clone volume": "克隆云硬盘", "Close": "关闭", "Close External Gateway": "关闭公网网关", "Close all notifications.": "关闭所有消息提示", "Close external gateway": "关闭公网网关", "Cloud": "云", "Cloud Container Engine": "容器云", "Cluster Detail": "集群详情", "Cluster Distro": "集群发行版", "Cluster Info": "集群信息", "Cluster Management": "集群管理", "Cluster Name": "集群名称", "Cluster Network": "集群网络", "Cluster Template": "集群模板", "Cluster Template Detail": "集群模板详情", "Cluster Template Name": "集群模板名称", "Cluster Templates": "集群模板", "Cluster Type": "集群类型", "Clusters": "集群", "Clusters Management": "集群管理", "Cocos (Keeling) Islands": "科科斯群岛", "Code": "编码", "Cold Migrate": "冷迁移", "Colombia": "哥伦比亚", "Command": "命令", "Command to run to check health": "运行以检查运行状况的命令", "Command was successfully executed at container {name}.": "命令已在容器 {name} 上成功执行。", "Commas ‘,’ are not allowed to be in a tag name in order to simplify requests that specify lists of tags": "标记名称中不允许使用英文逗号“,”，以简化指定标记列表的请求", "Commit Latency(ms)": "提交延迟(毫秒)", "Common Server": "云主机", "Comoros": "科摩罗", "Compute": "计算", "Compute Hosts": "计算节点", "Compute Live Migration": "计算热迁移", "Compute Live Resize Instance": "完成在线配置调整", "Compute Node status": "计算节点状态", "Compute Optimized": "计算型", "Compute Optimized Info": "计算优化信息", "Compute Optimized Type": "计算优化型", "Compute Optimized Type with GPU": "GPU计算型", "Compute Pause Instance": "暂停", "Compute Reboot Instance": "重启", "Compute Resume Instance": "恢复", "Compute Service": "计算服务", "Compute Services": "计算服务", "Compute Start Instance": "启动", "Compute Stop Instance": "关闭", "Compute Suspend Instance": "云主机挂起", "Compute Unpause Instance": "恢复", "Conductor Live Migrate Instance": "执行热迁移实例", "Conductor Live Resize Instance": "执行在线配置调整", "Conductor Migrate Server": "执行迁移服务器", "Config Overview": "配置概览", "Configuration": "配置", "Configuration Detail": "配置详情", "Configuration Group": "配置组", "Configuration Group ID/Name": "配置组ID/名称", "Configuration Groups": "配置组", "Configuration Update": "配置变更", "Configured Disk (GiB)": "已分配磁盘(GiB)", "Configured Memory (GiB)": "已分配内存(GiB)", "Confirm": "确定", "Confirm Config": "确认配置", "Confirm Password": "确认密码", "Confirm Resize or Migrate": "确认修改配置/迁移", "Confirm Shared Key": "确认共享密钥", "Confirming Resize or Migrate": "确认修改配置/迁移", "Connect Subnet": "连接子网", "Connect router": "连接路由", "Connected Threads": "连接的线程", "Connection Examples": "连接示例", "Connection Information": "连接信息", "Connection Limit": "连接限制", "Consecutive failures needed to report unhealthy": "报告不健康需要连续失败次数", "Console": "控制台", "Console Interface": "Console接口", "Console Log": "控制台日志", "Consumer": "消费者", "Container": "容器集群", "Container Creating": "容器创建中", "Container Deleting": "容器删除中", "Container Detail": "容器详情", "Container Format": "容器格式", "Container Killing": "容器终止中", "Container Name": "容器名称", "Container Pausing": "容器暂停中", "Container Rebooting": "容器重启中", "Container Rebuilding": "容器重建中", "Container Restarting": "容器重启中", "Container Starting": "容器启动中", "Container Status": "容器状态", "Container Stopping": "容器关闭中", "Container Unpausing": "容器恢复中", "Container Version": "容器版本", "Containers": "容器", "Containers CPU": "容器 CPU", "Containers Disk (GiB)": "容器硬盘 (GiB)", "Containers Info": "容器信息", "Containers Management": "容器管理", "Containers Memory (MiB)": "容器内存 (MiB)", "Content": "内容", "Content Type": "内容类型", "Continue": "继续", "Continue Upload": "继续上传", "Control Attribute": "控制属性", "Control Attributes": "控制属性", "Control Location": "控制端", "Cook Islands": "库克群岛", "Copy": "复制", "Copy File": "复制文件", "CoreOS": "", "Costa Rica": "哥斯达黎加", "Cote D'Ivoire": "科特迪瓦", "Count": "数量", "Crashed": "瘫痪", "Create": "创建", "Create Allowed Address Pair": "创建可用地址对", "Create Application Credentials": "创建应用凭证", "Create Backup": "创建备份", "Create Bandwidth Limit Rule": "创建带宽限制规则", "Create Bare Metal Node": "创建裸机节点", "Create Capsule": "创建集合", "Create Certificate": "创建证书", "Create Cluster": "创建集群", "Create Cluster Template": "创建集群模板", "Create Complete": "创建完成", "Create Configurations": "创建配置", "Create Container": "创建容器", "Create DSCP Marking Rule": "创建DSCP标记规则", "Create Database": "创建数据库", "Create Database Backup": "创建数据库备份", "Create Database Instance": "创建数据库实例", "Create Default Pool": "创建资源池", "Create Domain": "创建域", "Create Encryption": "创建加密", "Create Extra Spec": "创建额外规格", "Create Failed": "创建失败", "Create Firewall": "创建防火墙", "Create Firewall Policy": "创建防火墙策略", "Create Flavor": "创建云主机类型", "Create Folder": "创建文件夹", "Create Host Aggregate": "创建主机集合", "Create IPsec Site Connection": "创建IPsec站点连接", "Create Image": "创建镜像", "Create In Progress": "正在创建", "Create Instance": "创建云主机", "Create Instance Snapshot": "创建云主机快照", "Create Ironic Instance": "创建裸机", "Create Keypair": "创建密钥", "Create Listener": "创建监听器", "Create Loadbalancer": "创建负载均衡", "Create Network": "创建网络", "Create New Network": "创建新网络", "Create Node": "注册节点", "Create Policy": "创建策略", "Create Port": "创建端口", "Create Port Forwarding": "创建端口转发", "Create Port Group": "创建端口组", "Create Project": "创建项目", "Create QoS Policy": "创建QoS策略", "Create QoS Spec": "创建QoS规格", "Create RBAC Policy": "创建RBAC策略", "Create Record Set": "创建记录集", "Create Role": "创建角色", "Create Router": "创建路由器", "Create Rule": "创建规则", "Create Security Group": "创建安全组", "Create Segment": "创建分组", "Create Server Group": "创建云主机组", "Create Share": "创建共享", "Create Share Group": "创建共享组", "Create Share Group Type": "创建共享组类型", "Create Share Metadata": "创建共享元数据", "Create Share Network": "创建共享网络", "Create Share Type": "创建共享类型", "Create Snapshot": "创建快照", "Create Stack": "创建堆栈", "Create Static Route": "创建静态路由", "Create Subnet": "创建子网", "Create Time": "创建时间", "Create Transfer": "创建云硬盘转让", "Create Type": "创建方式", "Create User": "创建用户", "Create User Group": "创建用户组", "Create VPN": "创建VPN", "Create VPN Endpoint Group": "创建VPN端点组", "Create VPN IKE Policy": "创建VPN IKE策略", "Create VPN IPsec Policy": "创建VPN IPsec策略", "Create Virtual Adapter": "创建虚拟网卡", "Create Volume": "创建云硬盘", "Create Volume Backup": "创建云硬盘备份", "Create Volume Snapshot": "创建云硬盘快照", "Create Volume Type": "创建云硬盘类型", "Create Zone": "创建区域", "Create a full backup, the system will automatically create a new backup chain, the full backup name is the backup chain name; Create an incremental backup, the system will automatically create an incremental backup under the newly created backup chain.": "创建全量备份后，系统会自动创建新的备份链，全量备份名称为备份链名称; 创建增量备份，系统将在新创建的备份链下的对应备份点自动创建增量备份。", "Create firewall": "创建防火墙", "Create host aggregate": "创建主机集合", "Create image": "创建镜像", "Create instance": "创建云主机", "Create ironic instance": "创建裸机", "Create new AZ": "新建可用域", "Create rule": "创建规则", "Create security group": "创建安全组", "Create server group": "创建云主机组", "Create static route": "创建静态路由", "Create volume": "创建云硬盘", "Create volume backup": "创建云硬盘备份", "Created": "已创建", "Created At": "创建于", "Created Time": "创建时间", "Created Volumes": "创建的云硬盘", "Creating": "创建中", "Creating From Snapshot": "正在从快照创建共享", "Creation Timeout (Minutes)": "创建超时（分钟）", "Credential Type": "凭证类型", "Croatia (local name: Hrvatska)": "克罗地亚", "Cuba": "古巴", "Current Availability Zones": "当前可用域", "Current Compute Host": "当前计算节点", "Current Connections": "当前连接数", "Current Disk (GiB)": "当前硬盘 (GiB)", "Current Flavor": "当前配置", "Current Host": "当前主机", "Current Interface": "当前接口", "Current Master Node Count": "当前主节点数量", "Current Node Count": "当前节点数量", "Current Password": "原密码", "Current Path: ": "当前路径：", "Current Project": "当前项目", "Current Project Images": "当前项目镜像", "Current Project Networks": "当前项目网络", "Current Project QoS Policies": "当前项目QoS策略", "Current QoS policy name": "当前QoS策略名称", "Current Rules": "当前规则", "Current Status": "当前状态", "Current Storage Backend": "当前存储后端", "Current data downloaded.": "当前数据已完成下载。", "Custom": "自定义", "Custom Headers": "自定义标头", "Custom ICMP Rule": "定制ICMP规则", "Custom Metadata": "自定义元数据", "Custom Properties Info": "自定义属性", "Custom TCP Rule": "定制TCP规则", "Custom Trait": "自定义特性", "Custom UDP Rule": "定制UDP规则", "Cut": "剪切", "Cut File": "剪切文件", "Cyprus": "塞浦路斯", "Czech Republic": "捷克", "DC/OS": "DC/OS", "DCCP": "DCCP", "DEGRADED: One or more of the entity’s components are in ERROR": "降级：一个或多个实体的组件都处于错误状态", "DELETE COMPLETE": "删除完成", "DELETE FAILED": "删除失败", "DELETE_IN PROGRESS": "删除中", "DENY": "丢弃", "DHCP": "DHCP", "DHCP Agent": "DHCP服务", "DHCP Agents": "DHCP服务", "DISK IOPS": "硬盘IOPS", "DISK Usage(%)": "硬盘使用率(%)", "DNS": "DNS", "DNS Assignment": "DNS指派", "DNS Name": "DNS名称", "DNS Nameservers": "DNS服务器", "DNS Reverse": "DNS反向", "DNS Zones": "DNS区域", "DNS Zones Detail": "DNS区域详情", "DPD Action": "DPD动作", "DPD Interval (sec)": "DPD最大延迟（秒）", "DPD actions controls the use of Dead Peer Detection Protocol.": "DPD动作控制对失效对端协议的处理方式。", "DPD timeout (sec)": "DPD超时时间（秒）", "DRAINING: The member is not accepting new connections": "满载：该成员不接受新的连接", "DSCP Marking": "DSCP标记", "Danger": "危险", "Data Disk": "数据盘", "Data Disks": "块数据盘", "Data Protection": "数据保护", "Data Source Type": "数据源类型", "Database": "数据库", "Database Backup Detail": "数据库备份详情", "Database Disk (GiB)": "数据库硬盘 (GiB)", "Database Flavor": "数据库实例类型", "Database Instance": "数据库实例", "Database Instance Detail": "数据库实例详情", "Database Instance Name": "数据库实例名称", "Database Instance Status": "数据库实例状态", "Database Instances": "数据库实例", "Database Name": "数据库名称", "Database Port": "数据库端口", "Database Service": "数据库服务", "Databases": "数据库", "Datastore": "数据存储", "Datastore Type": "数据存储类型", "Datastore Version": "数据存储版本", "Deactivated": "已取消激活", "Debian": "", "Dedicated": "专用", "Default Policy": "默认策略", "Default Project": "默认项目", "Default Project ID/Name": "默认项目ID/名称", "Default is slaac, for details, see https://docs.openstack.org/neutron/latest/admin/config-ipv6.html": "默认使用slaac模式，详细配置方式请查看 https://docs.openstack.org/neutron/latest/admin/config-ipv6.html", "Defaults": "默认", "Defines the admin state of the health monitor.": "定义运行状况监视器的管理状态。", "Defines the admin state of the listener.": "定义侦听器的管理状态。", "Defines the admin state of the pool.": "定义池的管理状态。", "Defines the admin state of the port.": "定义端口的管理状态。", "Degraded": "降级", "Delay Interval(s)": "检查间隔(秒)", "Delete": "删除", "Delete Allowed Address Pair": "删除可用地址对", "Delete Application Credential": "删除应用凭证", "Delete Bandwidth Egress Rules": "删除带宽出方向限制", "Delete Bandwidth Ingress Rules": "删除带宽入方向限制", "Delete Capsule": "删除集合", "Delete Certificate": "删除证书", "Delete Cluster": "删除集群", "Delete Cluster Template": "删除集群模板", "Delete Complete": "删除完成", "Delete Configuration": "删除配置", "Delete Container": "删除容器", "Delete DSCP Marking Rules": "删除DSCP标记规则", "Delete Database": "删除数据库", "Delete Database Backup": "删除数据库备份", "Delete Default Pool": "删除资源池", "Delete Domain": "删除域", "Delete Encryption": "删除加密", "Delete Extra Specs": "删除额外规格", "Delete Failed": "删除失败", "Delete File": "删除文件", "Delete Firewall": "删除防火墙", "Delete Flavor": "删除云主机类型", "Delete Folder": "删除文件夹", "Delete Group": "删除用户组", "Delete Host Aggregate": "删除主机集合", "Delete IPsec Site Connection": "删除IPsec站点连接", "Delete Image": "删除镜像", "Delete In Progress": "正在删除", "Delete Instance": "删除云主机", "Delete Instance Snapshot": "删除云主机快照", "Delete Keypair": "删除密钥", "Delete Listener": "删除监听器", "Delete Load Balancer": "删除负载均衡", "Delete Member": "删除资源池成员", "Delete Metadata": "删除元数据", "Delete Network": "删除网络", "Delete Node": "删除节点", "Delete Policy": "删除策略", "Delete Port": "删除端口", "Delete Port Forwarding": "删除端口转发", "Delete Port Group": "删除端口组", "Delete Project": "删除项目", "Delete QoS Policy": "删除QoS策略", "Delete QoS Spec": "删除QoS规格", "Delete RBAC Policy": "删除RBAC策略", "Delete Record Set": "删除记录集", "Delete Role": "删除角色", "Delete Router": "删除路由器", "Delete Rule": "删除规则", "Delete Security Group": "删除安全组", "Delete Server Group": "删除云主机组", "Delete Share": "删除共享", "Delete Share Access Rule": "删除共享访问规则", "Delete Share Group": "删除共享组", "Delete Share Group Type": "删除共享组类型", "Delete Share Metadata": "删除共享元数据", "Delete Share Network": "删除共享网络", "Delete Share Server": "删除共享服务器", "Delete Share Type": "删除共享类型", "Delete Static Route": "删除静态路由", "Delete Subnet": "删除子网", "Delete User": "删除用户", "Delete VPN": "删除VPN", "Delete VPN EndPoint Groups": "删除VPN端点组", "Delete VPN IKE Policy": "删除VPN IKE策略", "Delete VPN IPsec Policy": "删除VPN IPsec策略", "Delete Virtual Adapter": "删除虚拟网卡", "Delete Volume": "删除云硬盘", "Delete Volume Backup": "删除云硬盘备份", "Delete Volume Snapshot": "删除云硬盘快照", "Delete Volume Type": "删除云硬盘类型", "Delete Volume on Instance Delete": "在实例删除时删除卷", "Delete Zone": "删除区域", "Delete metadata": "删除元数据", "Deleted": "已删除", "Deleted At": "删除于", "Deleted with the instance": "随云主机删除", "Deleting": "删除中", "Deleting this stack will delete all resources deployed by the stack.": "删除此堆栈将删除所有堆栈部署的资源。", "Democratic Republic of the Congo": "刚果民主共和国", "Denmark": "丹麦", "Denying": "删除中", "Deploy Failed": "部署失败", "Deploy Wait": "等待部署", "Deploying": "部署中", "Deployment Parameters": "部署参数", "Description": "描述", "Dest Folder": "目标文件夹", "Destination": "目的地", "Destination CIDR": "目的网络地址", "Destination IP": "目的IP", "Destination IP Address/Subnet": "目的IP地址/子网", "Destination Port": "目的端口", "Destination Port/Port Range": "目的端口/端口范围", "Detach": "解绑", "Detach Instance": "从云主机解绑", "Detach Interface": "卸载网卡", "Detach Network": "解绑网络", "Detach Security Group": "解绑安全组", "Detach Volume": "卸载云硬盘", "Detach interface": "卸载网卡", "Detaching": "卸载中", "Detail": "详情", "Detail Info": "详情信息", "Details": "详情", "Details *": "详情 *", "Details about the PTR record.": "有关 PTR 记录的详细信息。", "Device": "设备", "Device ID": "设备ID", "Device ID/Name": "设备ID/名称", "Device Owner": "设备所属者", "Devicemapper": "设备映射", "Direct": "方向", "Direction": "方向", "Disable": "禁用", "Disable Cinder Service": "禁用存储服务", "Disable Compute Host": "禁用计算节点", "Disable Compute Service": "禁用计算服务", "Disable Gateway": "禁用网关", "Disable Neutron Agent": "禁用网络服务", "Disable SNAT": "禁用 SNAT", "Disable TLS": "禁用TLS", "Disable compute host": "禁用计算节点", "Disabled": "已关闭", "Disabling port security will turn off the security group policy protection and anti-spoofing protection on the port. General applicable scenarios: NFV or operation and maintenance Debug.": "禁用端口安全会关闭该端口上的安全组策略保护和anti-spoofing保护。一般的适用场景：NFV或者运维Debug", "Disabling the project will have a negative impact. If the users associated with the project are only assigned to the project, they will not be able to log in": "禁用项目后将会产生负面影响，项目关联的用户如果只分配给该项目，将无法登陆", "Disassociate": "解绑", "Disassociate Floating IP": "解绑浮动IP", "Disassociate Floating Ip": "解绑浮动IP", "Disconnect Subnet": "断开子网", "Discovery URL": "发现网址", "Disk": "硬盘", "Disk (GiB)": "硬盘 (GiB)", "Disk Format": "硬盘格式", "Disk Info": "硬盘信息", "Disk Tag": "硬盘标签", "Disk allocation (GiB)": "云硬盘分配量 (GiB)", "Disk size is limited by the min disk of flavor, image, etc.": "根磁盘大小受云主机类型、镜像等的最小磁盘限制。", "Djibouti": "吉布提", "Do Build And Run Instance": "构建并运行实例", "Do HH:mm": "", "Do not reset the normally mounted volume to the \"available\"、\"maintenance\" or \"error\" status. The reset state does not remove the volume from the instance. If you need to remove the volume from the instance, please go to the console of the corresponding project and use the \"detach\" operation.": "请勿将正常的挂载中的云硬盘重置为“可用”、“维护”或”错误“状态。重置状态并不会将云硬盘从云主机上卸载下来。如果您需要将云硬盘从云主机上移除，请进入相应项目的控制台使用“解绑”操作。", "Do not set with a backend": "不设置后端", "Docker": "<PERSON>er", "Docker Hub": "<PERSON><PERSON>", "Docker Storage Driver": "Docker存储驱动", "Docker Swarm": "Docker Swarm", "Docker Swarm Mode": "Docker Swarm Mode", "Docker Volume Size (GiB)": "Docker硬盘大小(GiB)", "Domain": "域", "Domain Detail": "域详情", "Domain ID": "域ID", "Domain ID/Name": "域ID/名称", "Domain Manager": "域管理员", "Domain Name": "域名", "Domain name ending in.": "域名结尾", "Domains": "域", "Dominica": "多米尼克国", "Down": "停止", "Download": "导出", "Download File": "下载文件", "Download Image": "导出镜像", "Download all data": "下载所有数据", "Download canceled!": "下载已取消！", "Download current data": "下载当前数据", "Download progress": "下载进度", "Downloading": "下载中", "Draining": "满载", "Driver": "驱动", "Driver Handles Share Servers": "共享服务器", "Driver Info": "驱动信息", "Driver Interface": "驱动接口", "Duplicate tag name: {tag}": "重复的tag名称：{tag}", "EGP": "", "ENTRYPOINT": "运行命令(ENTRYPOINT)", "ESP": "", "Each instance belongs to at least one security group, which needs to be specified when it is created. Instances in the same security group can communicate with each other on the network, and instances in different security groups are disconnected from the internal network by default.": "每个云主机至少属于一个安全组，在创建的时候就需要指定。同一安全组内的云主机之间网络互通，不同安全组的云主机之间默认内网不通。", "Each new connection request is assigned to the next server in order, and all requests are finally divided equally among all servers. Commonly used for short connection services, such as HTTP services.": "按顺序把每个新的连接请求分配给下一个服务器，最终把所有请求平分给所有的服务器。常用于短连接服务，例如HTTP等服务。", "Each server can have up to 50 tags": "每台云主机最多绑定50个标签", "East Timor": "东帝汶", "Ecuador": "厄瓜多尔", "Edit": "编辑", "Edit Bandwidth Egress Limit Rule": "编辑带宽出方向限制", "Edit Bandwidth Ingress Limit Rule": "编辑带宽入方向限制", "Edit Bare Metal Node": "编辑裸机节点", "Edit Consumer": "编辑消费者", "Edit Container": "编辑容器", "Edit DSCP Marking Rule": "编辑DSCP标记规则", "Edit Default Pool": "编辑资源池", "Edit Domain": "编辑域", "Edit Domain Permission": "编辑域角色", "Edit Extra Spec": "编辑额外规格", "Edit Flavor": "编辑云主机类型", "Edit Health Monitor": "编辑健康检查器", "Edit Host Aggregate": "编辑主机集合", "Edit IPsec Site Connection": "编辑IPsec站点连接", "Edit Image": "编辑镜像", "Edit Instance": "编辑云主机", "Edit Instance Snapshot": "编辑云主机快照", "Edit Listener": "编辑监听器", "Edit Load Balancer": "编辑负载均衡", "Edit Member": "编辑成员", "Edit Metadata": "编辑元数据", "Edit Port": "编辑端口", "Edit Port Forwarding": "编辑端口转发", "Edit Port Group": "编辑端口组", "Edit Project": "编辑项目", "Edit QoS Policy": "编辑", "Edit Quota": "编辑配额", "Edit Role": "编辑角色", "Edit Router": "编辑路由器", "Edit Rule": "编辑规则", "Edit Share Metadata": "编辑共享元数据", "Edit Subnet": "编辑子网", "Edit System Permission": "编辑系统角色", "Edit User": "编辑用户", "Edit User Group": "编辑用户组", "Edit VPN": "编辑VPN", "Edit VPN EndPoint Groups": "编辑VPN端点组", "Edit VPN IKE Policy": "编辑VPN IKE策略", "Edit VPN IPsec Policy": "编辑VPN IPsec策略", "Edit Volume Backup": "编辑云硬盘备份", "Edit host aggregate": "编辑主机集合", "Edit metadata": "编辑元数据", "Edit quota": "编辑配额", "Edit rule": "编辑规则", "Editing only changes the content of the file, not the file name.": "编辑只改变文件内容，而不会改变文件名称。", "Effective Mode": "生效模式", "Effective mode after configuration changes": "配置变更后的生效模式", "Egress": "出口", "Egress Policy": "出口策略", "Egress Policy ID": "出口策略ID", "Egress Policy Name": "出口策略名称", "Egypt": "埃及", "Eject": "删除", "El Salvador": "萨尔瓦多", "Email": "邮箱", "Email Address": "邮箱地址", "Email for the zone. Used in SOA records for the zone.": "该区域的电子邮件。用于区域的 SOA 记录。", "Enable": "启用", "Enable Admin State": "启用管理状态", "Enable Compute Host": "启用计算节点", "Enable Compute Service": "启用计算服务", "Enable DHCP": "DHCP 已启用", "Enable Domain": "启用域", "Enable Floating IP": "使用浮动IP", "Enable Health Check": "启用健康检查", "Enable Health Monitor": "启用健康检查", "Enable Load Balancer": "启用负载均衡", "Enable Neutron Agent": "启用网络服务", "Enable Project": "启用项目", "Enable QoS Policy": "启用QoS策略", "Enable Registry": "启用注册表", "Enable SNAT": "启用 SNAT", "Enable Service": "启用服务", "Enable User": "启用用户", "Enable auto heal": "启用自动修复", "Enable auto remove": "启用自动删除", "Enable compute host": "启用计算节点", "Enable interactive mode": "启用交互模式", "Enabled": "启用", "Enabled Load Balancer for Master Nodes": "主节点开启负载均衡", "Enabled Network": "打开网络", "Encapsulation Mode": "封装模式", "Encrypted": "加密的", "Encryption": "加密", "Encryption Algorithm": "加密算法", "Encryption Info": "加密信息", "End Time": "结束时间", "Endpoint Counts": "端点数量", "Endpoints": "服务地址", "Engine ID": "引擎ID", "Enroll": "注册", "Enter Maintenance Mode": "进入维护模式", "Enter an integer value between 1 and 65535.": "端口必须是1到65535中的数字。", "Enter query conditions to filter": "输入查询条件进行筛选", "Entered: {length, plural, =1 {one character} other {# characters} }(maximum {maxCount} characters)": "已输入：{length}字符(最多{maxCount}字符)", "Environment": "环境", "Environment Variable": "环境变量", "Environment Variables": "环境变量", "Ephemeral Disk (GiB)": "本地临时卷 (GiB)", "Equatorial Guinea": "赤道几内亚", "Eritrea": "厄立特里亚国", "Error": "异常", "Error Deleting": "删除时出错", "Error Extending": "扩展错误", "Error Restoring": "恢复错误", "Estonia": "爱沙尼亚", "Ether Type": "以太网类型", "Ethiopia": "埃塞俄比亚", "Event": "事件", "Event Time": "发生时间", "Evictions": "驱逐", "Execute Command": "执行命令", "Execution Result": "执行结果", "Existing Volume": "已有硬盘", "Exit Policy": "退出策略", "Exp: ": "例如：", "Expand": "展开", "Expand Advanced Options": "展开高级选项", "Expired Time": "到期时间", "Expires At": "到期时间", "Export Location": "导入位置", "Export Locations": "导入位置", "Exposed Ports": "服务端口", "Extend Root Volume": "扩容根硬盘", "Extend Share": "扩容共享", "Extend Volume": "扩容云硬盘", "Extend volume": "扩容云硬盘", "Extending": "扩展中", "Extending Error": "扩展失败", "External": "外部网络", "External Fixed IP": "外部固定IP", "External Fixed IPs": "外部固定IP", "External Gateway": "外部网关", "External IP": "外网IP", "External IP(V4)": "外网IP(V4)", "External IP(V6)": "外网IP(V6)", "External Network": "外部网络", "External Network ID/Name": "外部网络ID/名称", "External Network Info": "外部网络信息", "External Networks": "外部网络", "External Port": "源端口", "External Port/Port Range": "源端口/端口范围", "Extra Infos": "额外信息", "Extra Specs": "额外规格", "FAKE": "FAKE", "FLAT": "FLAT", "Fail Rollback": "失败回滚", "Failed": "失败", "Failover Segment": "故障转移分组", "Falkland Islands (Malvinas)": "福克兰群岛", "Faroe Islands": "法罗群岛", "Fault": "错误", "Fedora": "", "Fiji": "斐济", "File": "文件", "File System Used Space": "文件系统已用空间", "File URL": "文件URL", "Filename": "文件名", "Files: {names}": "文件：{names}", "Fill In The Parameters": "参数填写", "Fingerprint": "指纹", "Finish Resize": "完成调整", "Finland": "芬兰", "Firewall": "防火墙", "Firewall Detail": "防火墙详情", "Firewall Policies": "防火墙策略", "Firewall Policy": "防火墙策略", "Firewall Port": "防火墙端口", "Firewall Rule": "防火墙规则", "Firewall Rules": "防火墙规则", "Firewalls": "防火墙", "Fixed IP": "内网IP", "Fixed IP Address": "内网IP地址", "Fixed IPs": "内网IP", "Fixed Network": "内网", "Fixed Subnet": "内网子网", "Flavor": "云主机类型", "Flavor Detail": "云主机类型详情", "Flavor Info": "配置信息", "Flavor Name": "云主机类型名称", "Flavor families, used to configure the instance flavor classification": "云主机类型规格族，用于配置云主机类型的分类", "Flavor of Master Nodes": "主节点类型", "Flavor of Nodes": "节点类型", "Flavors": "云主机类型", "Floating IP": "浮动IP", "Floating IP Address": "浮动IP地址", "Floating IP Enabled": "允许浮动IP", "Floating IPs": "浮动IP", "Floating Ip": "浮动IP", "Floating Ip Address": "浮动IP地址", "Floating Ip Detail": "浮动IP详情", "Floating ip has already been associate, Please check Force release": "浮动IP已经被关联使用，请选择强制释放", "Folder Detail": "文件夹详情", "Folder Name": "文件夹名称", "For GPU type, you need to install GPU drivers in the instance operating system.": "对于GPU类型的云主机，您需要在云主机操作系统中安装GPU驱动等。", "For GRE networks, valid segmentation IDs are 1 to 4294967295": "对于GRE网络，有效的段ID范围是从1到4294967295", "For VLAN networks, valid segmentation IDs are 1 to 4094": "对于VLAN网络，有效的段ID范围是从1到4094", "For VXLAN networks, valid segmentation IDs are 1 to 16777215": "对于VXLAN网络，有效的段ID范围是从1到16777215", "Forbidden": "禁用", "Forbidden Domain": "禁用域", "Forbidden Project": "禁用项目", "Forbidden User": "禁用用户", "Forbidden the domain will have a negative impact, all project and user in domain will be forbidden": "禁用域后，该域下面的项目和用户都会被禁止，用户将无法登陆", "Force Delete": "强制删除", "Force Delete Container": "强制删除容器", "Force Delete Share Instance": "强制删除共享实例", "Force release": "强制释放", "Force shutdown must be checked!": "必须勾选强制关机！", "Forced Down": "强制关闭", "Forced Shutdown": "强制关机", "Forced shutdown may result in data loss or file system damage. You can also take the initiative to shut down and perform operations.": "强制关机可能会导致数据丢失或文件系统损坏，您也可以主动关机后再进行操作。", "Forgot your password?": "忘记密码？", "Format": "规格", "Forward Slash ‘/’ is not allowed to be in a tag name": "标记名称中不允许使用正斜杠“ /”", "France": "法国", "Free": "空闲", "FreeBSD": "", "French Guiana": "法属圭亚那", "French Polynesia": "法属玻里尼西亚", "Frequent login failure will cause the account to be temporarily locked, please operate after 5 minutes": "频繁登陆失败会导致账户暂时锁定，请 5min 后再操作", "From port": "从端口自动获取", "Front End": "前端", "Frontend": "前端", "Full": "爆满", "Full Backup": "全量备份", "GPU Count": "GPU数量", "GPU Info": "GPU信息", "GPU Model": "GPU型号", "GPU Parameters": "GPU参数", "GPU Type": "GPU类型", "GPU model, used when configuring Compute Optimized Type with GPU": "GPU 型号，配置 GPU 计算型云主机类型时使用", "GPU pass-through will load GPU devices directly to the instance for use. VGPU is a GPU virtualization solution. GPU resources will be segmented and distributed to multiple instances for shared use.": "GPU直通将GPU设备直接加载给云主机进行使用。vGPU是GPU虚拟化方案，GPU资源将被切分后分配给多个云主机共享使用。", "GRE": "", "Gabon": "加蓬", "Gambia": "冈比亚", "Gateway": "网关", "Gateway IP": "网关IP", "Gateway Time-out (code: 504) ": "网关超时（错误码：504 ）", "Gateway ip {gateway_ip} conflicts with allocation pool {pool}": "网关地址 {gateway_ip} 和分配地址池 {pool} 冲突", "General Purpose": "通用型", "Generated Time": "生成时间", "Georgia": "格鲁吉亚", "Germany": "德国", "Get OpenRC file": "获取Openstack RC 文件", "Get Token": "获取Token", "Get {name} detail error.": "获取{name}详情失败。", "Get {name} error.": "获取{name}失败。", "Ghana": "加纳", "Gibraltar": "直布罗陀", "Given IP": "指定IP", "Glance": "", "Glance Image": "本地镜像", "Global Setting": "平台配置", "GlusterFS": "", "Grant Databases Access": "设置数据库访问", "Greece": "希腊", "Greenland": "格陵兰", "Grenada": "格林纳达", "Guadeloupe": "瓜德罗普岛", "Guam": "关岛", "Guatemala": "危地马拉", "Guinea": "几内亚", "Guinea Bissau": "几内亚比绍", "Guyana": "圭亚那", "HDFS": "HDFS", "HEALTHY": "健康", "HTTP Proxy": "HTTP代理", "HTTP Version not supported (code: 505) ": "HTTP版本不支持 (code: 505)", "HTTPS Proxy": "HTTPS代理", "Haiti": "海地", "Hard Reboot": "硬重启", "Hard Rebooting": "硬重启中", "Hash": "Hash", "Health Check CMD": "健康检查命令", "Health Check Interval": "健康检查间隔时间", "Health Check Retries": "健康检查重试次数", "Health Check Timeout": "健康检查超时时间", "Health Checking Log": "健康检查日志", "Health Inspection": "健康巡检", "New Health Inspection": "新健康巡检", "Health Monitor": "健康检查器", "Health Monitor Delay": "检查间隔(秒)", "Health Monitor Detail": "健康检查器详情", "Health Monitor Max Retries": "最大重试次数", "Health Monitor Name": "健康检查器名称", "Health Monitor Timeout": "检查超时时间(秒)", "Health Monitor Type": "健康检查器类型", "Health Status": "健康状况", "HealthMonitor Type": "健康检查类型", "Healthy": "健康", "Heartbeat Timestamp": "心跳时间戳", "Hello, {name}": "您好，{name}", "Heterogeneous Computing": "异构计算", "Hidden": "隐藏", "Hide Advanced Options": "隐藏高级选项", "Hide Default Firewalls": "隐藏默认防火墙", "Hide Default Policies": "隐藏默认策略", "Hide Default Rules": "隐藏默认规则", "High Clock Speed": "高主频型", "Home": "首页", "Home page": "首页", "Honduras": "洪都拉斯", "Hong Kong": "香港", "Host": "主机", "Host Aggregate": "主机集合", "Host Aggregates": "主机集合", "Host Average Network IO": "主机平均网络进出宽带", "Host CPU Usage": "主机CPU使用率", "Host Detail": "主机详情", "Host Disk Average IOPS": "主机平均硬盘IOPS", "Host Memory Usage": "主机内存使用率", "Host Name": "主机名称", "Host Routes": "主机路由", "Host Routes Format Error(e.g. *************/24,***********)": "无效：主机路由格式错误(例如: *************/24,***********)", "Host Routes Format Error(e.g. ::0a38:01fe/24,::0a38:01fe)": "无效：主机路由格式错误(例如: ::0a38:01fe/24,::0a38:01fe))", "Hostname": "主机名称", "Hosts": "主机", "Hosts Detail": "主机详情", "Hungary": "匈牙利", "Hypervisor Detail": "虚拟机管理器详情", "Hypervisors": "虚拟机管理器", "ICMP": "", "ICMP Code": "ICMP编码", "ICMP Type": "ICMP类型", "ICMP Type/ICMP Code": "类型值/编码值", "ID": "ID", "ID/Floating IP": "ID/浮动IP", "ID/Name": "ID/名称", "IGMP": "", "IKE Policies": "IKE策略", "IKE Policy": "IKE策略", "IKE Version": "IKE<PERSON>", "IP": "IP", "IP Address": "IP地址", "IP Distribution Mode": "IP分配模式", "IP Protocol": "IP协议", "IP Usage": "IP使用情况", "IP Version": "IP版本", "IP address allocation polls, one enter per line(e.g. ***********,***********00)": "IP地址分配池，每行一条(例如: ***********,***********00)", "IP address allocation polls, one enter per line(e.g. {ip})": "IP地址分配池，每行一条(例如: {ip})", "IPMI Address": "IPMI地址", "IPMI Bridge": "IPMI桥", "IPMI Password": "IPMI密码", "IPMI Port": "IPMI端口", "IPMI Privilege Level": "IPMI权限级别", "IPMI Protocol Version": "IPMI协议版本", "IPMI Username": "IPMI用户名", "IPMITool": "", "IPXE": "", "IPsec Policies": "IPsec策略", "IPsec Policy": "IPsec策略", "IPsec Site Connection": "IPsec站点连接", "IPsec Site Connections": "IPsec站点连接", "IPsec site connection Detail": "IPsec站点连接详情", "IPv4": "", "IPv4 Address": "IPv4 地址", "IPv6": "", "IPv6 Address": "IPv6 地址", "IPv6 Address Record": "IPv6 地址记录", "IPv6-Encap": "", "IPv6-Frag": "", "IPv6-ICMP": "", "IPv6-NoNxt": "", "IPv6-Opts": "", "IPv6-Route": "", "ISO - Optical disc image format": "ISO - 光盘映像格式", "Iceland": "冰岛", "Id": "", "Identifier of the physical port on the switch to which node’s port is connected to": "节点端口所连接的交换机物理端口ID", "Identity": "身份管理", "If \"Enable\" fails to roll back, the resource will be deleted after the creation fails; if \"Disable\" fails to roll back, the resource will be retained after the creation fails.": "若“启用”失败回滚，创建失败后会删除资源；若“禁用”失败回滚，创建失败后会保留资源。", "If OS is Linux, system will reset root password, if OS is Windows, system will reset Administrator password.": "如果操作系统是Linux，系统会修改root用户密码，如果是Windows，系统会修改Administrator用户密码。", "If an instance is using this flavor, deleting it will cause the instance's flavor data to be missing. Are you sure to delete {name}?": "若有云主机正在使用此 flavor，删除会导致云主机的 flavor 数据缺失，确定删除 {name} ？", "If checked, the network will be enable.": "如果选中，那么网络将被启用。", "If exposed port is specified, this parameter will be ignored.": "如果指定了服务端口，这个参数将被忽略。", "If it is an SNI type certificate, a domain name needs to be specified": "如果是 SNI 类型证书，需指定域名", "If it’s not set, the value of this in the template will be used.": "如果不设置，将使用模板的值", "If no gateway is specified, the first IP address will be defaulted.": "如果不指定网关IP，默认是第一个地址。", "If not provided, the roles assigned to the application credential will be the same as the roles in the current token.": "如果不选择，那么分配给应用凭证的角色将与当前用户的角色相同。", "If nova-compute on the host is disabled, it will be forbidden to be selected as the target host.": "如果计算节点上的nova-compute被禁用，将禁止其作为目标节点。", "If set then all tenants will be able to see this share.": "如果设置，则所有租户都将能够看到此共享。", "If the capacity of the disk is large, the type modify operation may take several hours. Please be cautious.": "如果云硬盘容量较大，修改云硬盘类型可能需要花费几个小时，请您谨慎操作。", "If the listener has an SNI certificate installed, it cannot be removed. Please delete the listener or replace the SNI certificate": "如果监听器安装了 SNI 证书，则无法将其删除。请删除监听器或更换 SNI 证书", "If the root disk has a snapshot, it will affect the deletion of the original disk during reconstruction or the recovery of the instance snapshot.": "如果根盘有快照，会影响重构时原盘的删除或云主机快照的恢复。", "If the value is set to 0, it means unlimited": "如果值为0，则表示无限制", "If the volume associated with the snapshot has changed the volume type, please modify this option manually; if the volume associated with the snapshot keeps the volume type unchanged, please ignore this option. (no need to change).": "若快照关联的云硬盘修改过云硬盘类型，请手动修改此选项；若快照关联的云硬盘保持云硬盘类型不变，请忽略此选项（不需要做变更）。", "If this parameter is selected, resumable uploads are supported, but the total upload time may be increased by a small amount. Images smaller than 200M are not recommended.": "勾选后支持断点续传，但可能增加少量总上传时长，小于200M的镜像不推荐使用", "If this parameter is specified, Zun will create a security group with a set of rules to open the ports that should be exposed, and associate the security group to the container.": "如果指定了这个参数，Zun 会创建一个安全组，里面有一组规则来开放应该暴露的端口，并将安全组关联到容器上。", "If you are not authorized to access any project, or if the project you are involved in has been deleted or disabled, contact the platform administrator to reassign the project": "您未被授权访问任何项目，或您参与中的项目已被删除或禁用，可联系平台管理员重新分配项目", "If you are not sure which authentication method to use, please contact your administrator.": "如果您不确定使用哪种认证方式，请联系管理员。", "If you choose a port which subnet is different from the subnet of LB, please ensure connectivity between the two.": "如果你选择了和LB子网不同的网卡，请确保两者的连通性。", "If you do not fill in parameters such as cpus, memory_mb, local_gb, cpu_arch, etc., you can automatically inject the configuration and Mac address of the physical machine by performing the \"Auto Inspect\" operation.": "如不填写cpus、memory_mb、local_gb、cpu_arch等参数，您可以通过执行“自动检测”操作来自动注入物理机的配置和 Mac 地址。", "If you still want to keep the disk data, it is recommended that you create a backup for the disk before deleting.": "如果您仍想保留云硬盘数据，建议您在删除之前为云硬盘创建备份。", "Illegal JSON scheme": "不合法的JSON格式", "Image": "镜像", "Image & OS": "镜像和操作系统", "Image Backup": "镜像备份", "Image Detail": "镜像详情", "Image Driver": "镜像来源", "Image Info": "镜像信息", "Image Name": "镜像名称", "Image Pending Upload": "镜像待上传", "Image Pulling": "镜像拉取中", "Image Size": "镜像大小", "Image Snapshot Pending": "镜像快照等待上传", "Image Uploading": "镜像上传中", "Images": "镜像", "Immediate effect": "即时生效", "Immediately delete": "立即删除", "Implied Roles": "隐含角色", "Import Keypair": "导入密钥", "Import Metadata": "导入元数据", "Import metadata": "导入元数据", "Importing": "导入中", "In Cluster": "集群中", "In Use": "使用中", "In general, administrator for Windows, root for Linux, please fill by image uploading.": "一般情况下Windows为administrator，Linux为root，请根据上传的镜像正确填写。", "In order to avoid data loss, the instance will shut down and interrupt your business. Please confirm carefully.": "为了避免数据丢失，云主机将关机中断您的业务，请仔细确认。", "In the last 30 days": "最近30天", "In the last 7 days": "最近7天", "In the last hour": "最近1小时", "In-use": "正在使用", "Inactive": "非活动状态", "Increment Backup": "增量备份", "Incremental": "增量", "Incremental Backup": "增量备份", "India": "印度", "Indicates whether this VPN can only respond to connections or both respond to and initiate connections.": "指示此VPN是仅响应连接还是同时响应和发起连接。", "Indonesia": "印度尼西亚", "Infinity": "无限制", "Info": "信息", "Ingress": "入口", "Ingress Policy": "入口策略", "Ingress Policy ID": "入口策略ID", "Ingress Policy Name": "入口策略名称", "Init Complete": "初始化完成", "Init Failed": "初始化失败", "Init In Progress": "正在初始化", "Initial Admin User": "初始管理员用户", "Initial Databases": "初始数据库", "Initial Volume Size": "初始硬盘容量", "Initialize Databases": "初始数据库", "Initiator Mode": "发起模式", "Input destination port or port range (example: 80 or 80:160)": "目的端口或端口范围（例如：80 或 80:160）", "Input external port or port range (example: 80 or 80:160)": "源端口或端口范围（例如：80 或 80:160）", "Input internal port or port range (example: 80 or 80:160)": "目标端口或端口范围（例如：80 或 80:160）", "Input source port or port range (example: 80 or 80:160)": "源端口或源端口范围(例如: 80 或 80:160)", "Insecure Registry": "不安全的注册表", "Insert": "插入", "Insert After": "晚于", "Insert Before": "早于", "Insert Rule": "插入规则", "Inspect Failed": "检查失败", "Inspecting": "巡检中", "Instance": "云主机", "Instance \"{ name }\" has already been locked.": "云主机\"{ name }\"已经锁定。", "Instance \"{ name }\" is ironic, can not soft reboot it.": "云主机\"{ name }\"是裸机，无法软重启。", "Instance \"{ name }\" is locked, can not delete it.": "云主机\"{ name }\"被锁定，无法删除。", "Instance \"{ name }\" is locked, can not pause it.": "云主机\"{ name }\"被锁定，无法暂停。", "Instance \"{ name }\" is locked, can not reboot it.": "云主机\"{ name }\"被锁定，无法重启。", "Instance \"{ name }\" is locked, can not resume it.": "云主机\"{ name }\"被锁定，无法恢复。", "Instance \"{ name }\" is locked, can not soft reboot it.": "云主机\"{ name }\"被锁定，无法软重启。", "Instance \"{ name }\" is locked, can not start it.": "云主机\"{ name }\"被锁定，无法启动。", "Instance \"{ name }\" is locked, can not stop it.": "云主机\"{ name }\"被锁定，无法关闭。", "Instance \"{ name }\" is locked, can not suspend it.": "云主机\"{ name }\"被锁定，无法挂起。", "Instance \"{ name }\" is locked, can not unpause it.": "云主机\"{ name }\"被锁定，无法取消暂停。", "Instance \"{ name }\" is not locked, can not unlock it.": "云主机\"{ name }\"不是锁定状态，无法解锁。", "Instance \"{ name }\" status is not active, can not soft reboot it.": "云主机\"{ name }\"状态不是运行中，无法软重启。", "Instance \"{ name }\" status is not in active or shutoff, can not reboot it.": "云主机\"{ name }\"状态不是运行中或关闭，无法重启。", "Instance \"{ name }\" status is not in active or suspended, can not stop it.": "云主机\"{ name }\"状态不是运行中，无法关闭。", "Instance \"{ name }\" status is not in active, can not pause it.": "云主机\"{ name }\"状态不是运行中，无法暂停。", "Instance \"{ name }\" status is not in active, can not suspend it.": "云主机\"{ name }\"状态不是运行中，无法挂起。", "Instance \"{ name }\" status is not in paused, can not unpause it.": "云主机\"{ name }\"状态不是暂停中，无法取消暂停。", "Instance \"{ name }\" status is not in suspended, can not resume it.": "云主机\"{ name }\"状态不是挂起中，无法恢复。", "Instance \"{ name }\" status is not shutoff, can not start it.": "云主机\"{ name }\"状态不是关闭，无法启动。", "Instance Addr": "所在主机", "Instance Architecture": "云主机架构图", "Instance Console Log": "云主机控制台日志", "Instance Detail": "云主机详情", "Instance ID": "实例ID", "Instance IP": "云主机IP", "Instance Info": "云主机信息", "Instance Port": "云主机端口", "Instance Related": "云主机相关", "Instance Snapshot": "云主机快照", "Instance Snapshot Detail": "云主机快照详情", "Instance Snapshot Name": "云主机快照名称", "Instance Snapshots": "云主机快照", "Instance Status": "云主机状态", "Instance UUID": "云主机UUID", "Instance-HA": "云主机HA", "Instances": "云主机", "Instances \"{ name }\" are locked, can not delete them.": "云主机\"{ name }\"被锁定，无法删除。", "Insufficient {name} quota to create resources (left { quota }, input { input }).": "{ name }配额不足，无法创建资源，请进行资源数量或配额的调整（剩余{ quota }，输入{ input }）。", "Interface Info": "接口信息", "Interface Name:": "网卡ID:", "Interface for vendor-specific functionality on this node": "用于在此节点上特定Vendor功能的接口", "Interface used for attaching and detaching volumes on this node": "用于在此节点上挂载与卸载云硬盘的接口", "Interface used for configuring RAID on this node": "用于在此节点上配置RAID的接口", "Interfaces": "网卡", "Internal Ip Address": "目标IP", "Internal Network Bandwidth (Gbps)": "内网带宽（Gbps）", "Internal Port": "目标端口", "Internal Port/Port Range": "目标端口/端口范围", "Internal Server Error (code: 500) ": "服务器错误（错误码：500）", "Invalid": "失效", "Invalid CIDR.": "无效的CIDR", "Invalid IP Address": "无效的IP地址", "Invalid IP Address and Port": "无效的IP地址和端口，成员已存在", "Invalid Mac Address. Please Use \":\" as separator.": "无效的Mac地址。请使用\":\"作为分隔符。", "Invalid Tag Value: {tag}": "非法的Tag值: {tag}", "Invalid combination": "无效的组合", "Invalid: ": "无效：", "Invalid: Allocation Pools Format Error(e.g. ***********,***********00) and start ip should be less than end ip": "无效：分配池格式错误(例如: ***********,***********00), 开始IP不能大于结束IP", "Invalid: Allocation Pools Format Error(e.g. fd00:dead:beef:58::9,fd00:dead:beef:58::13) and start ip should be less than end ip": "无效：分配池格式错误(例如: fd00:dead:beef:58::9,fd00:dead:beef:58::13), 开始IP不能大于结束IP", "Invalid: CIDR Format Error(e.g. **********/24)": "无效：CIDR格式错误（例如： **********/24）", "Invalid: DNS Format Error(e.g. 1001:1001::)": "无效：DNS格式错误(例如: 1001:1001::)", "Invalid: DNS Format Error(e.g. ***************)": "无效：DNS格式错误(例如: ***************)", "Invalid: Domain name cannot be duplicated": "无效，域名不可重复", "Invalid: Password must be the same with confirm password.": "无效：密码和确认密码必须一致。", "Invalid: Please input a valid ip": "无效：请输入有效的IP", "Invalid: Please input a valid ipv4": "无效：请输入有效的IPV4", "Invalid: Please input a valid ipv6.": "无效：请输入有效的IPV6", "Invalid: Project name can not be chinese": "无效：项目名称不可使用中文", "Invalid: Project names in the domain can not be repeated": "无效：域下的项目名称不能重复", "Invalid: Quota value(s) cannot be less than the current usage value(s): { used } used.": "无效：配额必须大于已使用数量{ used }且为整数", "Invalid: User Group names in the domain can not be repeated": "无效：域下的用户组名称不能重复", "Invalid: User names in the domain can not be repeated": "无效：域下的用户名称不能重复", "Ip Address": "IP地址", "Iran (Islamic Republic of)": "伊朗", "Iraq": "伊拉克", "Ireland": "爱尔兰", "Ironic Instance": "裸机", "Ironic Instance Name": "裸机名称", "Is Current Project": "属于当前项目", "Is Public": "公有", "Is admin only": "仅限管理员", "Is associate to floating ip: ": "已绑定浮动IP：", "Is external network port": "是外部网络的网卡", "Isolate": "", "Isolate(No multithreading)": "Isolate（不允许有多线程）", "Israel": "以色列", "It is IPv6 type.": "这是IPv6格式的。", "It is recommended that the { instanceType } instance simultaneously set large page memory to large. { instanceType } instances also require faster memory addressing capabilities.": "推荐{ instanceType }云主机同时设置大页内存为“大”。{ instanceType }云主机同时需要更快速的内存寻址能力配合。", "It is recommended that you perform this cloning operation on a disk without any reading/writing": "建议在云硬盘无读写任务时执行此项克隆操作。", "It is recommended that you use the private network address 10.0.0.0/8, **********/12, ***********/16": "推荐您使用私网网段10.0.0.0/8, **********/12, ***********/16作为子网地址", "It is recommended that { instanceType } instance simultaneously set NUMA affinity policy for PCIE device to force or priority matching. This configuration can further improve PCIE computing performance.": "推荐{ instanceType }云主机同时设置PCIE设备NUMA亲和策略为强制或优先匹配。此配置可进一步提升GPU计算性能。", "It is recommended to install and use this agent. The instance created with this image can be used to modify the password (qemu_guest_agent needs to be installed when creating the image).": "推荐安装并使用此agent，使用此镜像创建的云主机才能实现修改密码（需要在制作镜像时安装qemu_guest_agent）。", "It is recommended to refer to the following description format, otherwise it may not be effective": "建议参考下面描述的格式，否则可能不生效", "It is recommended to set CPU binding strategy as binding on { instanceType } instance. This configuration further improves the performance of the instance CPU.": "推荐{ instanceType }云主机同时设置CPU策略为专用。此配置可进一步提升云主机CPU性能。", "It is recommended to set the CPU thread binding policy as thread binding in { instanceType } instance, which can further improve the CPU performance of instance.": "推荐{ instanceType }云主机同时设置CPU线程策略为多线程，此配置可进一步提升云主机CPU性能。", "It is suggested to use the marked AZ directly, too much AZ will lead to the fragmentation of available resources": "建议直接使用已划好的 AZ，过多的 AZ 会导致可用资源碎片化。", "It is unreachable for all floating ips.": "对所有浮动IP来说这是不可达的。", "It is unreachable for this floating ip.": "对于此浮动IP而言，这是不可达的。", "Italy": "意大利", "Items in Cache": "缓存中的项目", "Jamaica": "牙买加", "Japan": "日本", "Jordan": "约旦", "Jump to Console": "跳转到控制台", "Kampuchea": "柬埔寨", "Kazakhstan": "哈萨克", "Kenya": "肯尼亚", "Kernel ID": "内核ID", "Kernel Image": "Kernel镜像", "Kernel Version": "内核版本", "Key": "键", "Key Pair": "密钥", "Key Pairs": "密钥", "Key Size (bits)": "密钥大小(比特)", "Keypair": "SSH密钥对", "Keypair Detail": "密钥详情", "Keypair Info": "密钥信息", "Keystone Credentials": "Keystone认证", "Keystone token is expired.": "token已过期，请检查服务器时间是否正确，确认token是否有效", "Kill": "终止", "Kill Container": "终止容器", "Kill Signal": "终止信号", "Killed": "终止", "Kubernetes": "Kubernetes", "Kuwait": "科威特", "Kyrgyzstan": "吉尔吉斯", "LB Algorithm": "算法", "LEAST_CONNECTIONS": "最少连接", "Labels": "标签", "Lao People's Democratic Republic": "老挝", "Large": "大", "Large Screen": "大屏", "Large(Optimal performance)": "大（性能最优）", "Last 2 Weeks": "近两周", "Last 24H Status": "最近24H", "Last 7 Days": "近7天", "Last Day": "最近1天", "Last Hour": "最近1小时", "Last Updated": "最近更新", "Last week alarm trend": "最近一周告警趋势", "Latvia": "拉脱维亚", "Leave Maintenance Mode": "退出维护模式", "Lebanon": "黎巴嫩", "Left": "剩余", "Lesotho": "莱索托", "Liberia": "利比里亚", "Libyan Arab Jamahiriya": "利比亚", "Liechtenstein": "列支敦士登", "Lifetime": "生存期", "Lifetime Value": "生存期值", "Listener": "监听器", "Listener Connection Limit": "监听器连接限制", "Listener Description": "监听器描述", "Listener Detail": "监听器详情", "Listener Name": "监听器名称", "Listener Number": "监听器数量", "Listener Protocol": "监听器协议", "Listener Protocol Port": "监听器协议端口", "Listeners": "监听器", "Lithuania": "立陶宛", "Live Migrate": "热迁移", "Live Migration At Destination": "热迁移至目标地址", "Load Balancer": "负载均衡", "Load Balancer Detail": "负载均衡详情", "Load Balancer Name": "负载均衡名称", "Load Balancers": "负载均衡", "Load Template from a file": "从文件加载模板", "Load from local files": "从本地文件读取", "LoadBalancers Instances": "负载均衡", "Local": "本端", "Local Endpoint Group": "本端端点组", "Local Endpoint Group ID": "本端端点组ID", "Local Link Connection": "本地链接连接", "Local Network": "本端网络", "Local SSD": "本地SSD", "Local Subnet": "本端子网", "Locality": "本地", "Lock": "锁定", "Lock Instance": "锁定云主机", "Lock Status": "锁定状态", "Lock instance will lock the operations that have a direct impact on the operation of the instance, such as: shutdown, restart, delete, the mounting and unmounting of volume, etc. It does not involve the capacity expansion and change type of volume.": "云主机锁定操作会锁定对云主机运行有直接影响的操作, 例如: 关机、重启、删除，云硬盘的挂载、卸载等，不涉及到云硬盘的扩容与变更类型。", "Locked": "锁定", "Log": "日志", "Log Length": "日志行数", "Log in": "登录", "Login Name": "登录名", "Login Password": "登录密码", "Login Type": "登录凭证", "Login Captcha Verification": "登录验证码校验", "Login Failure Check": "登录失败次数校验", "Login Failure Count": "登录失败次数", "Show UCCPS Button": "显示容器云平台按钮", "Show Large Screen Button": "显示大屏按钮", "Show Health Inspection Button": "显示健康巡检按钮", "Show New Health Inspection Button": "显示新健康巡检按钮", "Start Inspection": "开始巡检", "Start Detection": "开始检测", "Inspection Records": "巡检记录", "Inspection Configuration": "巡检配置", "Configure platform-wide health inspection work items to ensure normal operation of various health indicators of the platform": "可配置全平台健康巡检工作项目，确保平台各项健康指标工作的正常运行", "HostOS Inspection Items": "HostOS巡检项目", "Cloud Platform Inspection Items": "云平台巡检项目", "GuestOS Inspection Items": "GuestOS巡检项目", "Storage Inspection Items": "存储巡检项目", "HostOS Disk Usage Check": "HostOS磁盘利用率检查", "HostOS CPU Usage Check": "HostOS CPU利用率检查", "HostOS Memory Usage Check": "HostOS内存利用率检查", "HostOS Capacity Monitoring Check": "HostOS容量监控检查", "Physical Network Card Monitoring Security Check": "物理网卡监控安全检查", "Platform Virtual Machine Status Check": "平台虚拟机状态检查", "CinderVolume Service": "CinderVolume服务", "NovaCompute Service": "NovaCompute服务", "NeutronOpenvswitch Service": "NeutronOpenvswitch服务", "Keepalived Service": "Keepalived服务", "NeutronDhcp Service": "NeutronDhcp服务", "NeutronL3 Service": "NeutronL3服务", "Glance Service": "Glance服务", "Platform Status Security Check": "平台状态安全检查", "Keystone Service": "Keystone服务", "GuestOS Usage Check": "GuestOS利用率检查", "GuestOS CPU Usage Check": "GuestOS CPU利用率检查", "Various Security Checks": "各类安全检查", "Cluster Status": "集群状态", "Storage Status Security Check": "存储状态安全检查", "Storage Capacity Status": "存储容量状态", "PG Status": "PG状态", "OSD Status": "OSD状态", "Please select at least one inspection item": "请至少选择一个巡检项目", "Inspection started, please wait...": "巡检已开始，请稍候...", "Inspection completed successfully": "巡检完成", "Network Resource Detection": "网络资源检测", "Virtual Router Status": "虚拟路由状态", "Virtual Gateway Status": "虚拟网关状态", "Port Status": "端口状态", "Host Packet Loss Detection": "主机丢包检测", "Response Content Based Health Check": "基于响应内容的健康检查", "HostOS巡检检测": "HostOS巡检检测", "HostOS磁盘利用率检查": "HostOS磁盘利用率检查", "HostOS CPU利用率检查": "HostOS CPU利用率检查", "HostOS内存利用率检查": "HostOS内存利用率检查", "HostOS容量监控检查": "HostOS容量监控检查", "物理网卡监控安全检查": "物理网卡监控安全检查", "云平台巡检项目": "云平台巡检项目", "平台虚拟机状态检查": "平台虚拟机状态检查", "CinderVolume服务": "CinderVolume服务", "NovaCompute服务": "NovaCompute服务", "NeutronOpenvswitch服务": "NeutronOpenvswitch服务", "Keepalived服务": "Keepalived服务", "NeutronDhcp服务": "NeutronDhcp服务", "NeutronL3服务": "NeutronL3服务", "Glance服务": "Glance服务", "平台状态安全检查": "平台状态安全检查", "Keystone服务": "Keystone服务", "GuestOS巡检检测": "GuestOS巡检检测", "GuestOS利用率检查": "GuestOS利用率检查", "GuestOS CPU利用率检查": "GuestOS CPU利用率检查", "各类安全检查": "各类安全检查", "存储巡检项目": "存储巡检项目", "集群状态": "集群状态", "存储状态安全检查": "存储状态安全检查", "存储容量状态": "存储容量状态", "PG状态": "PG状态", "OSD状态": "OSD状态", "网络资源检测": "网络资源检测", "虚拟路由状态": "虚拟路由状态", "虚拟网关状态": "虚拟网关状态", "端口状态": "端口状态", "主机丢包检测": "主机丢包检测", "基于响应内容的健康检查": "基于响应内容的健康检查", "HostOS资源检测": "HostOS资源检测", "HostOS分区利用率检测": "HostOS分区利用率检测", "HostOS CPU利用率检测": "HostOS CPU利用率检测", "HostOS内存利用率检测": "HostOS内存利用率检测", "HostOS告警事件检测": "HostOS告警事件检测", "物理网卡连通状态检测": "物理网卡连通状态检测", "云平台资源检测": "云平台资源检测", "平台数据库状态检测": "平台数据库状态检测", "Neutron服务": "Neutron服务", "NeutronServer服务": "NeutronServer服务", "Nova服务": "Nova服务", "Cinder服务": "Cinder服务", "平台HA状态检测": "平台HA状态检测", "Select All": "全选", "Unselect All": "取消勾选", "Collapse All": "收起所有", "Expand All": "展开所有", "Stop Detection": "停止检测", "Inspection stopped": "巡检已停止", "Elapsed Time": "耗时", "Inspection Items": "巡检项目", "GuestOS资源检测": "GuestOS资源检测", "GuestOS内存利用率检测": "GuestOS内存利用率检测", "GuestOS CPU利用率检测": "GuestOS CPU利用率检测", "卷状态": "卷状态", "存储资源检测": "存储资源检测", "存储池状态检测": "存储池状态检测", "存储对象状态": "存储对象状态", "We are inspecting your cloud platform, please wait": "正在对您的云平台进行巡检，请稍候", "Export Report": "导出报告", "Export Details": "导出详情", "Send Log": "发送日志", "Normal": "正常", "Warning": "警告", "Detection Item": "检测项", "Status": "状态", "Detection Result Details": "检测结果详情", "Repair Suggestions": "修复建议", "Detection Results": "检测结果", "Please select a detection item from the left to view details": "请选择左侧检测项目查看详细信息", "Detection Result": "检测结果", "Detailed Description": "详细说明", "Field": "字段", "Value": "值", "Step": "步骤", "Suggestion": "建议", "Inspection Results": "巡检结果", "View Inspection Results": "查看巡检结果", "Failed to start inspection: ": "启动巡检失败：", "Show Ticket Management Menu": "显示工单管理菜单", "Ticket Management": "工单管理", "My Tickets": "我的工单", "Pending Tickets": "待办工单", "Completed Tickets": "已办工单", "Settings saved successfully!": "设置已经成功保存！", "Other Setting": "其他配置", "Save": "保存", "Logs": "日志", "Luxembourg": "卢森堡", "MAC Address": "MAC地址", "MAC Learning State": "MAC学习状态", "MAPRFS": "", "MEM %": "内存使用率(%)", "MEM LIMIT (MiB)": "总内存 (MiB)", "MEM USAGE (MiB)": "已用内存 (MiB)", "MTU": "", "Mac Address": "Mac地址", "MacVTap": "", "Macau": "澳门", "Madagascar": "马达加斯加", "Mail Exchange Record": "邮件往来记录", "Maintained": "维护", "Maintenance": "运维管理", "Malawi": "马拉维", "Malaysia": "马来西亚", "Maldives": "马尔代夫", "Mali": "马里", "Malta": "马尔他", "Manage Access": "访问管理", "Manage Access Rule": "管理访问规则", "Manage Error": "管理失败", "Manage Host": "管理主机", "Manage Metadata": "管理元数据", "Manage Ports": "管理端口", "Manage QoS Spec": "管理QoS规格", "Manage Resource Types": "管理资源类型", "Manage Security Group": "管理安全组", "Manage Starting": "管理开始", "Manage State": "管理状态", "Manage User": "管理用户", "Manage User Group": "管理用户组", "Manage host": "管理主机", "Manage user": "管理用户", "Manage user group": "管理用户组", "Manageable": "可管理", "Management": "维护", "Management Reason": "维护原因", "Mandatory for secondary zones. The servers to slave from to get DNS information.": "对于次要区域是必填的。从服务器获取 DNS 信息。", "Manu": "手动", "Manual input": "手动输入", "Manually Assigned Address": "手动分配地址", "Manually Specify": "手动指定", "Marshall Islands": "马绍尔群岛", "Martinique": "马提尼克岛", "Master Node Addresses": "主节点地址", "Master Node Flavor": "主节点类型", "Master Node LB Enabled": "启用主节点负载均衡", "Masters": "主服务器", "Mauritania": "毛里塔尼亚", "Mauritius": "毛里求斯", "Max Avail": "最大可用量", "Max BandWidth": "最大带宽", "Max Burst": "最大突发", "Max Retries": "最大重试次数", "Max Retry": "最大重试次数", "Max connect": "最大连接数", "Maximum interval time for each health check response": "每个健康检查响应的最大间隔时间", "Maximum time to allow one check to run in seconds": "允许一次检查运行的最长时间（以秒为单位）", "Mayotte": "马约特", "Mem": "内存", "Member Count": "成员数量", "Member Detail": "成员详情", "Member Num": "成员数量", "Members": "成员", "Members of Each Group": "每个组内成员", "Members of Each Server Group": "每个云主机组内成员", "Memory": "内存", "Memory (GiB)": "内存 (GiB)", "Memory (MiB)": "内存 (MiB)", "Memory Optimized": "内存型", "Memory Page": "内存页", "Memory Page Size": "内存页大小", "Memory Usage": "内存使用量", "Memory Usage(%)": "内存使用率(%)", "Memory Usages (GiB)": "内存用量 (GiB)", "Mesos": "Mesos", "Message": "消息", "Message Details": "消息详情", "Message Queue Service": "消息队列服务", "Metadata": "元数据", "Metadata Definitions": "元数据定义", "Metadata Detail": "元数据详情", "Mexico": "墨西哥", "Micronesia": "密克罗尼西亚", "Migrate": "迁移", "Migrate Volume": "迁移云硬盘", "Migrate volume": "迁移云硬盘", "Migrating": "迁移", "Migrating To": "迁移目标", "Min Memory": "最小内存", "Min Memory (GiB)": "最小内存 (GiB)", "Min System Disk": "最小系统盘", "Min System Disk (GiB)": "最小系统盘 (GiB)", "Min size": "最小容量", "Min. Disk": "最小硬盘大小", "Min. RAM": "最小内存", "Minimum value is 68 for IPv4, and 1280 for IPv6.": "对于IPv4，最小值是68，对于IPv6，最小值是1280。", "Miscellaneous": "杂项", "Missing IP Address": "缺少IP地址", "Missing Port": "未填写端口号", "Missing Subnet": "未填写子网", "Missing Weight": "未填写权重", "Modification Times": "修改时间", "Modify Instance Tags": "修改云主机标签", "Modify Project Tags": "修改项目标签", "Modify QoS": "修改QoS", "Moldova": "摩尔多瓦", "Monaco": "摩纳哥", "Mongolia": "外蒙古", "Monitor Center": "监控中心", "Monitor Overview": "监控概览", "Montenegro": "黑山共和国", "Montserrat": "蒙特塞拉特", "More": "更多", "More Actions": "更多操作", "More than one label is required, such as: \"example.org.\"": "至少需要 2 个以上标签，如：\"example.org.\"", "Morocco": "摩洛哥", "Mount ISO": "挂载ISO", "Mount snapshot support": "支持挂载快照", "Mozambique": "莫桑比克", "Multiple filter tags are separated by enter": "多个过滤标签用回车键分隔", "My Role": "我的角色", "MySQL Actions": "MySQL 操作", "Myanmar": "缅甸", "N/A": "", "NET I/O(B)": "网络 I/O(B)", "NFS": "", "NOOP": "", "NUMA Node": "NUMA节点", "NUMA Node Count": "NUMA节点数量", "NUMA Nodes": "NUMA节点", "Name": "名称", "Name Server": "名称服务器", "Name can not be duplicated": "名称不可重复", "Name or ID og the container image": "容器镜像的名称或 ID", "Namespace": "命名空间", "Namibia": "那米比亚", "Nauru": "瑙鲁", "Nepal": "尼泊尔", "Netherlands": "荷兰", "Netherlands Antilles": "荷兰安的列斯群岛", "Network": "网络", "Network Attaching": "网络绑定中", "Network Config": "网络配置", "Network Detaching": "网络解绑中", "Network Detail": "网络详情", "Network Driver": "网络驱动程序", "Network Dropped Packets": "网络丢包率", "Network Errors": "网络错误", "Network ID": "网络ID", "Network ID/Name": "网络ID/名称", "Network Info": "网络信息", "Network Interface": "网卡", "Network Line": "网络线路", "Network Name": "网络名称", "Network Service": "网络服务", "Network Setting": "网络设置", "Network Traffic": "网络流量", "Network Type": "网络类型", "Network topology page": "网络拓扑页面", "Networking": "创建网络中", "Networking *": "网络 *", "Networks": "网络", "Neutron Agent Detail": "网络服务详情", "Neutron Agents": "网络服务", "Neutron Net": "Neutron网络", "Neutron Service": "网络服务", "Neutron Subnet": "Neutron子网", "New": "新建", "New Availability Zone": "新可用域", "New Caledonia": "新喀里多尼亚", "New Status": "新状态", "New Tag": "新标签", "New Volume": "新的硬盘", "New Zealand": "新西兰", "Next": "下一步", "Next Hop": "下一跳", "Nicaragua": "尼加拉瓜", "Niger": "尼日尔", "Nigeria": "尼日利亚", "No": "否", "No - Do not create a new system disk": "否 - 不创建新的系统盘", "No Console": "无控制台", "No Logs...": "暂无日志...", "No Monitor": "无监控", "No Outputs": "无输出", "No Proxy": "非代理", "No Raid": "", "No State": "无状态", "No Task": "空闲", "No Vender": "", "No default pool set": "未设置默认池", "Node": "节点", "Node Addresses": "节点地址", "Node Driver": "节点驱动", "Node Flavor": "节点类型", "Node ID/Name": "节点ID/名称", "Node Info": "节点信息", "Node Name": "节点名称", "Node Spec": "节点规格", "Nodes": "节点", "Nodes To Remove": "指定要缩减的节点", "Norfolk Island": "诺福克岛", "North Korea": "朝鲜", "Northern Mariana Islands": "北马里亚纳群岛", "Norway": "挪威", "Not Implemented (code: 501) ": "服务器不支持请求（错误码：501）", "Not Open": "未开放", "Not dealt with for the time being": "暂不处理", "Not deleted with the instance": "不随云主机删除", "Not locked": "未锁定", "Not select": "不选择", "Not yet bound": "尚未绑定", "Not yet selected": "尚未选择", "Note that when using a share type with the driver_handles_share_servers extra spec as False, you should not provide a share network.": "请注意，当使用额外规范 driver_handles_share_servers为 False 的共享类型时，您无法设置共享网络。", "Note: Are you sure you need to modify the volume type?": "注意：确定需要修改云硬盘类型？", "Note: Please consider the container name carefully since it couldn't be changed after created.": "注意：为容器取名需谨慎，因为创建后不可修改。", "Note: The security group you use will act on all virtual adapters of the instance.": "注：您所用的安全组将作用于云主机的全部虚拟网卡。", "Notification Detail": "通知详情", "Notifications": "通知", "Nova Service": "计算服务", "Number of GPU": "GPU数量", "Number of IPs used by all projects": "所有项目使用的IP数量", "Number of Master Nodes": "主节点数量", "Number of Nodes": "节点数量", "Number of Ports": "端口数量", "Number of Usb Controller": "USB控制器数量", "OK": "", "OS": "操作系统", "OS Admin": "镜像默认用户", "OS Disk": "系统盘", "OS Type": "操作系统类型", "OS Version": "系统版本", "OSDs": "", "OSPF": "", "Object": "对象", "Object Count": "对象数量", "Object Count ": "Object数量", "Object ID": "对象ID", "Object ID/Name": "对象ID/名称", "Object Name": "对象名称", "Object Storage": "对象存储", "Object Type": "对象类型", "Off": "关", "Offline": "离线", "Oman": "阿曼", "On": "开", "On Maintenance": "开启维护", "On failure": "失败时", "One entry per line(e.g. ***************)": "每行一条(例如: ***************)", "One entry per line(e.g. {ip})": "每行一条(例如: {ip})", "One-way authentication": "单向认证", "Online": "在线", "Online Resize": "在线修改配置", "Only a MAC address or an OpenFlow based datapath_id of the switch are accepted in this field": "只可填写交换机的Mac地址或者交换机基于openflow的数据路径ID", "Only libvirt driver is supported.": "只支持libvirt驱动。", "Only subnets that are already connected to the router can be selected.": "仅可选择已经连接过路由器的子网。", "Open External Gateway": "开启公网网关", "OpenID Connect": "OpenID连接", "Operating Status": "操作状态", "Operating System": "操作系统", "Operation Center": "运营中心", "Operation Name": "操作名称", "Operation Time": "操作时间", "Optimized Parameters": "优化参数", "Optional list": "可选列表", "Options": "选项", "Orchestration": "资源编排", "Orchestration Services": "编排服务", "Orchestration information": "编排信息", "Origin File Name": "原始文件名称", "Original Password": "原始密码", "Other Protocol": "其他协议", "Other Service": "其他服务", "Other Services": "其他服务", "Others": "其他", "Out Cluster": "集群外", "Out of Sync": "不同步", "Outputs": "输出", "Overlapping allocation pools: {pools}": "重叠的分配地址池： {pools}", "Overlay": "覆盖", "Overlay2": "覆盖2", "Overview": "预览", "Owned Network": "所属网络", "Owned Network ID": "所属网络ID", "Owned Network ID/Name": "所属网络ID/名称", "Owned Project": "所属项目", "Owned Subnet": "所属子网", "Owner": "所有者", "Ownership of a volume can be transferred from one project to another. The transfer process of the volume needs to perform the transfer operation in the original owner's project, and complete the \"accept\" operation in the receiver's project.": "卷的拥有权可以从一个项目转给另外一个。卷的转让过程需要在原拥有者的项目中执行转让操作，在接收者项目中完成“接受”操作。", "PEM encoding": "PEM编码", "PFS": "完全向前保密", "PG Count": "PG数量", "PGM": "", "PING": "", "PTR Domain Name": "PTR 域名", "PXE": "", "PXE Enabled": "PXE启用", "Pakistan": "巴基斯坦", "Palau": "帛琉", "Palestine": "巴勒斯坦", "Panama": "巴拿马", "Papua New Guinea": "巴布亚新几内亚", "Paraguay": "巴拉圭", "Parameter": "参数", "Params Setting": "参数设置", "Password": "密码", "Password Type": "密码类型", "Password changed successfully, please log in again.": "密码修改成功，请重新登录。", "Password must be the same with confirm password.": "密码和确认密码必须一致。", "Paste": "粘贴", "Paste File": "粘贴文件", "Path": "路径", "Pause": "暂停", "Pause Container": "暂停容器", "Pause Instance": "暂停云主机", "Paused": "已暂停", "Pausing": "暂停中", "Payload": "载体", "Peer": "对端", "Peer Address": "对端网关", "Peer Cidrs": "对端CIDR", "Peer Endpoint Group": "对端端点组", "Peer Endpoint Group ID": "对端端点组ID", "Peer Gateway Public Address": "对端网络IP地址", "Peer ID": "对端标识", "Peer Network": "对端网络", "Peer Network Segment": "对端网络", "Peer gateway public address for the IPsec site connection": "对端网络的地址", "Pending": "等待", "Pending Create": "等待创建", "Pending Delete": "等待删除", "Pending Update": "等待更新", "Perform a consistent hash operation on the source IP address of the request to obtain a specific value. At the same time, the back-end server is numbered, and the request is distributed to the server with the corresponding number according to the calculation result. This can enable load distribution of visits from different source IPs, and at the same time enable requests from the same client IP to always be dispatched to a specific server. This method is suitable for load balancing TCP protocol without cookie function.": "将请求的源IP地址进行一致性Hash运算，得到一个具体的数值，同时对后端服务器进行编号，按照运算结果将请求分发到对应编号的服务器上。这可以使得对不同源IP的访问进行负载分发，同时使得同一个客户端IP的请求始终被派发至某特定的服务器。该方式适合负载均衡无cookie功能的TCP协议。", "Permanent": "长期保留", "Persistent": "持久性", "Peru": "秘鲁", "Phase1 Negotiation Mode": "Phase1协商模式", "Philippines": "菲律宾", "Phone": "手机", "Physical CPU Usage": "物理CPU使用量", "Physical Network": "物理网络", "Physical Node": "物理节点", "Physical Nodes": "物理节点", "Physical Storage Usage": "物理存储使用量", "Pitcairn": "皮特凯恩岛", "Platform Info": "平台概况", "Please confirm your password!": "请确认您的密码", "Please enter JSON in the correct format!": "请输入正确格式的JSON！", "Please enter URL!": "请输入URL！", "Please enter a correct certificate content, format is refer to the left tip!": "请输入正确的证书内容，格式参考左边提示！", "Please enter a correct domain, format is refer to the left tip!": "请输入正确的域名，格式参考左边提示！", "Please enter a correct private key, format is refer to the left tip!": "请输入正确的密钥，格式参考左边提示！", "Please enter a file link starting with \"http://\" or \"https://\"!": "请输入以“http://”或“https://”开头的文件链接！", "Please enter a memory page size, such as: 1024, 1024MiB": "请输入内存页大小，如：1024, 1024MiB", "Please enter a valid ASCII code": "请输入有效的ASCII码", "Please enter a valid Email Address!": "请输入一个有效的邮箱地址", "Please enter a valid IPv4 value.": "请输入有效的 IPv4 值。", "Please enter a valid IPv6 value.": "请输入有效的 IPv6 值。", "Please enter a valid Phone Number": "请输入一个有效的手机号", "Please enter complete key value!": "请输入完整的键值！", "Please enter right format custom trait!": "请输入正确格式的自定义特性！", "Please enter right format key value!": "请输入正确格式的键值", "Please enter right format memory page value!": "请输入正确格式的内存页值", "Please enter right format trait!": "请输入正确格式的特性！", "Please enter the correct id": "请输入正确的ID", "Please enter the server id to be reduced, and separate different id with \",\"": "请输入指定缩减的server ID，并且不同ID之间用 ',' 分隔", "Please fill in the peer network segment and subnet mask of CIDR format, the written subnets should be under the same router, one per line.": "请填写CIDR格式的对端网段，且填写的网段需在同一个路由下，每行一个。", "Please input": "请输入", "Please input <username> or <username>@<domain name>!": "请输入<用户名> 或 <用户名>@<用户域名>！", "Please input ICMP code(0-255)": "请输入ICMP编码(0-255)", "Please input ICMP type(0-255)": "请输入ICMP类型(0-255)", "Please input IPv4 or IPv6 cidr": "请输入IPv4或IPv6网段地址", "Please input IPv4 or IPv6 cidr, (e.g. ***********/24, 2001:DB8::/48)": "请输入IPv4或IPv6网段地址，（如：***********/24, 2001:DB8::/48）", "Please input a number": "请输入数字", "Please input a parameter": "请输入参数", "Please input a valid ip!": "请输入正确的IP地址", "Please input a value": "请输入值", "Please input at least 2 characters.": "请输入至少2个字符", "Please input at least one record": "请输入至少一条记录", "Please input auth key": "请输入密钥", "Please input cipher": "请输入cipher", "Please input cluster name": "请输入集群名称", "Please input cluster template name": "请输入集群模板名称", "Please input complete data": "请输入完整的数据", "Please input container name": "请输入容器名称", "Please input file name": "请输入文件名称", "Please input image": "请输入镜像", "Please input ip address": "请输入 IP 地址", "Please input ipv4": "请输入IPV4", "Please input ipv6": "请输入IPV6", "Please input key": "请输入键", "Please input key and value": "请输入键和值", "Please input key size": "请输入密钥大小", "Please input metadata": "请输入元数据", "Please input name": "请输入名称", "Please input or load Template from a file": "请输入或者从文件加载模板", "Please input port and protocol": "请输入端口和协议", "Please input prefix": "请输入前缀", "Please input protocol number if it absent in select list.": "如果选择列表中没有，请输入协议号。", "Please input provider": "请输入提供者", "Please input snapshot name": "请输入快照名称", "Please input the correct format:  <username> or <username>@<domain name>.": "请输入正确格式：<用户名> 或 <用户名>@<用户域名>", "Please input transfer id": "请输入转让Id", "Please input user name": "请输入用户名称", "Please input value": "请输入值", "Please input your Password!": "请输入密码", "Please input your Username!": "请输入用户名", "Please input your current password!": "请输入当前密码！", "Please input your password!": "请输入密码！", "Please input {label}": "请输入{label}", "Please input {label}!": "请输入{label}！", "Please make sure this IP address be available to avoid creating VM failure.": "需检查此 IP 是否已被占用，否则可能创建失败。", "Please make sure this IP address be available.": "需确保此IP未被占用。", "Please note that when deleting a domain, all projects, users, and user groups under the domain will be deleted directly!": "请注意，删除域时，会直接把域下的项目、用户和用户组全部删除！", "Please reasonably plan the network and subnet to which the virtual network card belongs.": "请合理规划虚拟网卡所属的网络和子网。", "Please save your token properly and it will be valid for {left}.": "请妥善保管您的Token，该Token {left} 内有效。", "Please select": "请选择", "Please select a file": "请选择一个文件", "Please select a file with the suffix {types}": "请选择后缀为{types}的文件", "Please select a network!": "请选择网络！", "Please select a parameter": "请选择参数", "Please select a subnet!": "请选择子网！", "Please select a type!": "请选择类型！", "Please select availability zone": "请选择可用域", "Please select image driver": "请选择镜像来源", "Please select item!": "请选择一个条目！", "Please select login type!": "请选择登录方式！", "Please select policy": "请选择一个策略", "Please select source": "请选择源", "Please select type": "请选择类型", "Please select volume type": "请选择云硬盘类型", "Please select your Region!": "请选择Region！", "Please select {label}!": "请选择{label}！", "Please select {name} first": "请先选择{name}", "Please select: {name} or an image file that is the same as it": "请选择：{name}或与它相同的镜像文件。", "Please set CPU && Ram first.": "请先设置CPU、内存。", "Please set MUNA": "请设置NUMA节点", "Please set a size no less than {minSize} GiB!": "请设置不小于 {minSize} GiB 的容量！", "Please set at least one role!": "请至少选择一个角色！", "Please set the system disk size!": "请设置系统盘的容量！", "Please upload files smaller than { size }GiB on the page. It is recommended to upload files over { size }GiB using API.": "页面请上传小于{ size }GiB的文件，超过{ size }GiB的文件建议使用API上传。", "Pointer Record": "指针记录", "Poland": "波兰", "Policy": "策略", "Policy Detail": "策略详情", "Policy Edit": "编辑策略", "Policy Name": "策略名称", "Policy Rules": "策略规则", "Pool Algorithm": "资源池算法", "Pool Description": "资源池描述", "Pool Detail": "资源池详情", "Pool ID": "资源池 ID", "Pool Info": "资源池信息", "Pool Name": "资源池名称", "Pool Protocol": "资源池协议", "Pools": "资源池", "Port": "端口", "Port Count": "端口数量", "Port Detail": "端口详情", "Port Forwardings": "端口转发", "Port Group": "端口组", "Port Groups": "端口组", "Port ID": "端口ID", "Port Info": "端口信息", "Port Range": "端口范围", "Port Security": "端口安全", "Port Security Enabled": "启用端口安全", "Port Type": "端口方式", "Ports": "端口", "Ports are either single values or ranges": "端口要么都是单一数值，要么都是范围", "Ports provide extra communication channels to your containers. You can select ports instead of networks or a mix of both, If the terminal port and the network are selected at the same time, note that the terminal port is not a terminal port of the selected network, and the container under the same network will only be assigned one IP address (The port executes its own security group rules by default).": "端口为您的云容器提供了额外的通信渠道。您可以选择已创建的端口而非网络或者二者都选，如果同时选择了端口和网络，注意端口不是已选择网络的某端口，同一个网络下容器只会分配到一个IP地址（端口默认执行本身的安全组规则）。", "Ports provide extra communication channels to your instances. You can select ports instead of networks or a mix of both (The port executes its own security group rules by default).": "端口为您的云主机提供了额外的通信渠道。您可以选择已创建的端口而非网络或者二者都选（端口默认执行本身的安全组规则）。", "Portugal": "葡萄牙", "Power Off": "关机", "Power On": "开机", "Power State": "电源状态", "Powering Off": "关闭中", "Powering On": "启动中", "Pre Live Migration": "预热迁移", "Pre-Shared Key must be the same with Confirm Shared Key.": "确认共享密钥必须和预共享密钥一致。", "Pre-Shared Key(PSK) String": "预共享密钥(PSK)", "Prefer": "首选", "Prefer(Thread siblings are preferred)": "Prefer（最好有多线程, 若有的话则使用线程的兄弟节点）", "Preferred": "首选", "Prefix": "前缀", "Prep Resize": "预调整", "Prepare Template": "准备模板", "Previous": "上一步", "Primary": "主要", "Primary is controlled by Designate, Secondary zones are slaved from another DNS Server.": "主要区域由 Designate 控制，次要区域由另一个 DNS 服务器从属。", "Private": "私有", "Private Key": "私钥", "Profile": "概要", "Progress": "进度", "Project": "项目", "Project Console": "项目控制台", "Project Detail": "项目详情", "Project ID": "项目ID", "Project ID/Name": "项目ID/名称", "Project Name": "项目名称", "Project Num": "项目数量", "Project Quota": "项目配额", "Project Range": "项目范围", "Project Scope": "所属项目", "Project Scope (Project Name: Role Names)": "所属项目(项目名称：角色名称)", "Project User Groups": "项目用户组", "Project Users": "项目用户", "Projects": "项目", "Promote": "推动", "Properties": "属性", "Protected": "受保护的", "Protocol": "协议", "Protocol Type": "协议类型", "Provider": "提供者", "Provider Network Type": "提供商网络类型", "Provider Physical Network": "提供商物理网络", "Provision State": "配置状态", "Provisioning Status": "配置状态", "Public": "公有", "Public Access": "公开访问", "Public Address": "公共地址", "Public Images": "公有镜像", "Public Key": "公钥", "Published In": "发布入口", "Published Out": "发布出口", "Puerto Rico": "波多黎各", "QCOW2 - QEMU image format": "QCOW2-QEMU 镜像格式", "Qatar": "卡塔尔", "QoS Bandwidth Egress Limit": "QoS出方向带宽限制", "QoS Bandwidth Ingress Limit": "QoS入方向带宽限制", "QoS Bandwidth Limit": "QoS带宽限制", "QoS Detail": "QoS详情", "QoS Policies": "QoS策略", "QoS Policy": "QoS策略", "QoS Policy Detail": "QoS策略详情", "QoS Policy ID": "QoS策略ID", "QoS Policy ID/Name": "QoS策略ID/名称", "QoS Spec": "QoS规格", "QoS Spec ID": "QoS规格ID", "QoS Specs": "QoS规格", "QoS policies": "QoS策略", "Qos Policy": "QoS策略", "Queued": "已排队", "Queued To Apply": "排队申请", "Queued To Deny": "排队删除", "Quota": "配额", "Quota Overview": "配额概况", "Quota exceeded": "配额用尽", "Quota is not enough for extend share.": "配额不足以扩容共享。", "Quota is not enough for extend volume.": "配额不足以扩容硬盘。", "Quota of key pair means: the number of allowed key pairs for each user.": "密钥的配额表示：每个用户允许创建的密钥数量。", "Quota: Insufficient quota to create resources, please adjust resource quantity or quota(left { quota }, input { input }).": "配额：项目配额不足，无法创建资源，请进行资源数量或配额的调整（剩余{ quota }，输入{ input }）。", "Quota: Insufficient { name } quota to create resources, please adjust resource quantity or quota(left { left }, input { input }).": "配额：{ name } 配额不足，无法创建资源，请进行资源数量或配额的调整（剩余{ left }，输入{ input }）。", "Quota: Insufficient { name } quota to create resources.": "配额：{ name } 配额不足，无法创建资源。", "Quota: Project quotas sufficient resources can be created": "配额：项目配额充足，可创建资源", "RAM": "内存", "RAM (MiB)": "内存 (MiB)", "RAW - Raw disk image format": "RAW - 原始磁盘映像格式", "RBAC Policies": "RBAC策略", "RBAC Policy Detail": "RBAC策略详情", "REJECT": "拒绝", "RESTORE COMPLETE": "恢复完成", "RESUME COMPLETE": "恢复完成", "RESUME FAILED": "恢复失败", "ROLLBACK COMPLETE": "回滚完成", "ROLLBACK FAILED": "回滚失败", "ROLLBACK IN PROGRESS": "回滚中", "ROUND_ROBIN": "轮询算法", "RSVP": "", "Raid Interface": "Raid接口", "Ram Size (GiB)": "内存容量 (GiB)", "Ram value is { ram }, NUMA RAM value is { totalRam }, need to be equal. ": "内存是 { ram }MiB，NUMA节点的内存是{ totalRam }MiB，需要一致。", "Ramdisk ID": "内存盘ID", "Ramdisk Image": "Ramdisk镜像", "Rbac Policy": "RBAC策略", "Read and write": "可读可写", "Read only": "只读", "Real Name": "真实姓名", "Reason": "原因", "Reason: ": "原因：", "Reboot": "重启", "Reboot Container": "重启容器", "Reboot Database Instance": "重启数据库实例", "Reboot Instance": "重启云主机", "Rebooting": "重启中", "Rebuild": "重建", "Rebuild Block Device Mapping": "重建块设备映射中", "Rebuild Container": "重建容器", "Rebuild Instance": "重建云主机", "Rebuild Spawning": "重建孵化中", "Rebuilding": "重建中", "Rebuilt": "重建", "Recently a day": "最近1天", "Record Sets": "记录集", "Records": "记录", "Recordset Detail": "记录集详情", "Recordsets Detail": "记录集详情", "Recover": "恢复", "Recovering": "恢复中", "Recovery Method": "恢复类型", "Recycle Bin": "回收站", "Region": "域", "Registry Enabled": "启用注册表", "Related Policy": "关联策略", "Related Resources": "关联资源", "Release": "释放", "Release Fixed IP": "释放内网IP", "Remote Group Id": "远端安全组", "Remote IP Prefix": "远端IP前缀", "Remote Security Group": "远端安全组", "Remote Type": "远端方式", "Remove": "移除", "Remove Default Project": "移除默认项目", "Remove Network": "移除网络", "Remove Router": "移除路由器", "Remove Rule": "移除规则", "Remove default project for user": "移除用户默认项目", "Rename": "重命名", "Rename is to copy the current file to the new file address and delete the current file, which will affect the creation time of the file.": "重命名是把当前文件复制到新文件地址，并删除当前文件，会影响文件的创建时间。", "Replication Change": "复制更改中", "Report Count": "报告数量", "Republic of the Congo": "刚果共和国", "Request ID": "请求ID", "Require": "强制", "Require(Need multithreading)": "Require（必须有多线程）", "Required Data Disk": "所需数据盘", "Rescue": "救援", "Rescued": "已救援", "Rescuing": "救援中", "Reserved": "已预定", "Reset Status": "重置状态", "Reset To Initial Value": "重置为初始值", "Reset failed, please retry": "重置失败，请重试", "Resize": "修改配置", "Resize Cluster": "集群伸缩", "Resize Instance": "修改配置", "Resize Volume": "扩容硬盘", "Resized": "已修改配置", "Resizing or Migrating": "正在修改配置/迁移", "Resource": "资源", "Resource Class": "资源类", "Resource Class Properties": "资源类属性", "Resource Id": "资源ID", "Resource Not Found": "资源未找到", "Resource Pool": "资源池", "Resource Status": "资源状态", "Resource Status Reason": "资源状态信息", "Resource Type": "资源类型", "Resource Types": "资源类型", "Resources Synced": "资源同步", "Resource Monitor": "资源监控", "Restart": "重启", "Restart Container": "重启容器", "Restart Database Service": "重启数据库服务", "Restarting": "重启中", "Restore Backup": "恢复备份", "Restore From Snapshot": "从快照恢复", "Restore backup": "恢复备份", "Restore from snapshot": "从快照恢复", "Restoring": "恢复中", "Restoring Backup": "恢复备份中", "Restricted": "受限", "Restricted Situation": "受限情况", "Resume": "恢复", "Resume Complete": "恢复完成", "Resume Failed": "恢复失败", "Resume In Progress": "正在恢复中", "Resume Instance": "恢复云主机", "Resuming": "恢复中", "Retry times for restart on failure policy": "重新启动失败策略的重试次数", "Retyping": "修改类型中", "Reunion": "留尼汪岛", "Reverse DNS Detail": "反向DNS详情", "Reverse Detail": "反向详情", "Reverse Dns": "反向DNS", "Revert Resize or Migrate": "回滚修改配置/迁移", "Revert Resize/Migrate": "回滚修改配置/迁移", "Reverting": "恢复中", "Reverting Error": "恢复失败", "Reverting Resize or Migrate": "正在回滚修改配置/迁移", "Role": "角色", "Role Detail": "角色详情", "Role Name": "角色名称", "Roles": "角色", "Rollback Complete": "回滚完成", "Rollback Failed": "回滚失败", "Rollback In Progress": "回滚中", "Romania": "罗马尼亚", "Root Disk": "系统盘", "Root Disk (GiB)": "根磁盘 (GiB)", "Root directory": "根目录", "Router": "路由器", "Router Advertisements Mode": "路由广播模式", "Router Detail": "路由器详情", "Router External": "外部网关", "Router ID": "路由器ID", "Router Port": "路由器端口", "Routers": "路由器", "Rule": "规则", "Rule Action": "动作", "Rule Detail": "规则详情", "Rule Edit": "编辑规则", "Rule Numbers": "规则数量", "Rules": "规则", "Rules Number": "规则数量", "Running": "运行中", "Running Threads": "运行中的线程", "Running Time": "运行时间", "Runtime": "运行时", "Russia": "俄罗斯", "Rwanda": "卢旺达", "SCTP": "", "SNAPSHOT COMPLETE": "快照完成", "SNAT Enabled": "启用SNAT", "SNI Certificate": "SNI证书", "SNI Enabled": "SNI开启", "SOURCE_IP": "源IP算法", "SSH Public Key Fingerprint": "SSH 公钥指纹", "SSL Parsing Method": "SSL解析方式", "Saint Vincent and the Grenadines": "圣文森特和格林纳丁斯", "Same subnet with LB": "与LB子网相同", "Samoa": "美属萨摩亚", "San Marino": "圣马力诺共和国", "Sao Tome and Principe": "圣多美和普林西比", "Saudi Arabia": "沙特阿拉伯", "Saving": "保存中", "Scheduler Hints": "调度程序提示", "Scheduling": "调度中", "Search": "搜索", "Sec for DPD delay, > 0": "设置DPD检查的最大延时时间。", "Sec for DPD timeout, > 0 & > DPD Interval": "设置DPD检查的超时时间，超时时间必须大于最大延迟时间。", "Secondary": "次要", "Security Group": "安全组", "Security Group Detail": "安全组详情", "Security Group Info": "安全组信息", "Security Group Num:": "安全组数量:", "Security Group Rule": "安全组规则", "Security Group Rules": "安全组规则", "Security Groups": "安全组", "Security Groups Adding": "安全组绑定中", "Security Groups Removing": "安全组移除中", "Security Info": "安全信息", "Segment Detail": "分组详情", "Segment ID": "分组ID", "Segment Name": "分组名称", "Segmentation ID": "段ID", "Segmentation Id": "分段ID", "Segments": "分组", "Select File": "选择文件", "Select Project": "选择项目", "Select Project Role": "选择项目角色", "Select User Group": "选择用户组", "Select Volume Snapshot": "选择云硬盘快照", "Select a QoS Policy": "请选择Qos策略", "Select a login type": "请选择登录方式", "Select a network": "请选择网络", "Select a project": "请选择项目", "Select a region": "请选择Region", "Select an object type": "请选择对象类型", "Selected": "已选", "Selected Members": "已选择成员", "Selected list": "已选列表", "Sender Policy Framework": "", "Senegal": "塞内加尔", "Serbia": "塞尔维亚共和国", "Serial": "顺序", "Server Certificate": "服务器证书", "Server Certificates": "服务器证书", "Server Group": "云主机组", "Server Group Detail": "云主机组详情", "Server Group Member": "云主机组成员", "Server Groups": "云主机组", "Server Status": "服务状态", "Server Type": "服务类型", "Service": "服务", "Service List": "服务列表", "Service Locator": "服务定位器", "Service Port ID": "服务端口ID", "Service State": "服务状态", "Service Status": "管理状态", "Service Status Updated": "服务状态更新", "Service Type": "服务类型", "Service Unavailable (code: 503) ": "服务不可用（错误码：503 ）", "Services": "服务", "Set": "设置", "Set Admin Password": "设置管理员密码", "Set Boot Device": "设置引导设备", "Set Default Project": "设置默认项目", "Set Domain Name PTR": "设置域名 PTR", "Set IP": "设置IP", "Set default project for user": "设置用户默认项目", "Seychelles": "塞舌尔", "Share": "共享", "Share Capacity (GiB)": "共享容量 (GiB)", "Share Detail": "共享详情", "Share File Storage": "文件存储", "Share Group": "共享组", "Share Group Detail": "共享组详情", "Share Group Type": "共享组类型", "Share Group Type Detail": "共享组类型详情", "Share Group Types": "共享组类型", "Share Groups": "共享组", "Share Id": "共享ID", "Share Instance": "共享实例", "Share Instance Detail": "共享实例详情", "Share Instances": "共享实例", "Share Network": "共享网络", "Share Network Detail": "共享网络详情", "Share Network Subnet": "共享网络子网", "Share Network Subnets": "共享网络子网", "Share Networks": "共享网络", "Share Protocol": "共享协议", "Share Replica ID": "共享副本ID", "Share Server": "共享服务器", "Share Server Detail": "共享服务器详情", "Share Servers": "共享服务器", "Share Type": "共享类型", "Share Type Detail": "共享类型详情", "Share Type ID": "共享类型ID", "Share Type Name": "共享类型名称", "Share Types": "共享类型", "Shared": "共享", "Shared Images": "共享镜像", "Shared Network": "共享网络", "Shared Networks": "共享网络", "Shared QoS Policies": "共享QoS策略", "Shared QoS Policy": "共享QoS策略", "Shared policy only can insert shared rules.": "共享的策略只可以插入共享的规则。", "Shares": "共享", "Shelve": "归档", "Shelve Instance": "归档云主机", "Shelved": "已归档", "Shelved Offloaded": "已归档", "Shelving": "归档中", "Shelving Image Pending Upload": "归档镜像等待上传中", "Shelving Image Uploading": "归档镜像上传中", "Shelving Offloading": "归档卸载中", "Show All Domain": "展开所有域", "Show Instance": "展示云主机", "Show all Data": "展开所有数据", "Shrinking": "缩容中", "Shrinking Error": "缩容失败", "Shrinking Possible Data Loss Error": "由数据丢失导致的缩容失败", "Shut Down": "关闭", "Shut Off": "关闭", "Shutoff": "关闭", "Sierra Leone": "塞拉利昂", "Sign Out": "退出登录", "Sign up": "注册", "Signal to send to the container: integer or string like SIGINT. When not set, SIGKILL is set as default value and the container will exit. The supported signals varies between platform. Besides, you can omit \"SIG\" prefix.": "发送到容器的信号：整数或字符串，如 SIGINT。未设置时，SIGKILL 设置为默认值，容器将退出。支持的信号因平台而异。此外，您可以省略 \"SIG\" 前缀。", "Singapore": "新加坡", "Size": "容量", "Size (GiB)": "容量 (GiB)", "Slovakia (Slovak Republic)": "斯洛伐克", "Slovenia": "斯洛文尼亚", "Slow Query": "慢查询", "Small": "小", "Small(Not recommended)": "小（不推荐）", "Smart Scheduling": "智能调度", "Snapshot Complete": "生成快照完成", "Snapshot Failed": "生成快照失败", "Snapshot In Progress": "快照进行中", "Snapshot Instance": "创建云主机快照", "Snapshot Source": "快照源", "Snapshots can be converted into volume and used to create an instance from the volume.": "快照可以转换成云硬盘，用于从云硬盘启动云主机。", "Snapshotting": "创建快照中", "Soft Delete Instance": "软删除云主机", "Soft Deleted": "软删除", "Soft Deleting": "软删除中", "Soft Power Off": "软关机", "Soft Reboot": "软重启", "Soft Reboot Instance": "软重启云主机", "Soft Rebooting": "软重启中", "Soft-Affinity": "亲和组（非强制）", "Soft-Anti-Affinity": "反亲和组（非强制）", "Solomon Islands": "索罗门群岛", "Somalia": "索马里", "Sorry, the page you visited does not exist.": "抱歉，您访问的页面不存在。", "Source": "源", "Source IP": "源IP", "Source IP Address/Subnet": "源IP地址/子网", "Source Path: {path}": "原路径：{path}", "Source Port": "源端口", "Source Port/Port Range": "源端口/端口范围", "South Africa": "南非", "South Korea": "韩国", "Spain": "西班牙", "Spawning": "孵化中", "Spec": "规格", "Specification": "规格", "Specify Physical Node": "指定物理节点", "Specify mount point.": "指定挂载点", "Specify the client IP address": "指定客户端 IP 地址", "Specify the listener port": "指定监听器端口", "Specify whether future replicated instances will be created on the same hypervisor (affinity) or on different hypervisors (anti-affinity). This value is ignored if the instance to be launched is a replica.": "指定未来的复制实例是在相同的管理程序（亲和）上创建还是在不同的管理程序（反亲和）上创建。如果要启动的实例是副本，则忽略此值。", "Specs": "规格", "Sri Lanka": "斯里兰卡", "Stack": "堆栈", "Stack Detail": "Stack堆栈详情", "Stack Events": "部署日志", "Stack Faults": "堆栈故障", "Stack ID": "堆栈ID", "Stack Name": "堆栈名称", "Stack Resource": "堆栈资源", "Stack Resource Type": "堆栈资源类型", "Stack Resources": "堆栈资源", "Stack Status": "模板状态", "Stacks": "堆栈", "Stand Alone Ports Supported": "独立端口支持", "Standard Trait": "标准特性", "Start": "启动", "Start Container": "启动容器", "Start Instance": "启动云主机", "Start Of Authority": "授权开始", "Start Source": "启动源", "Start Source Name": "启动源名称", "Start Time": "开始时间", "Start auto refreshing data": "启动自动刷新数据", "Start refreshing data every {num} seconds": "启动每{num}秒自动刷新数据", "Started At": "开始于", "Startup Parameters": "启动参数", "State": "状态", "Static Routes": "静态路由", "Stats Information": "统计信息", "Status Code": "状态码", "Status Detail": "状态详情", "Status Reason": "状态原因", "Stop": "关闭", "Stop Container": "关闭容器", "Stop Database Service": "停止数据库服务", "Stop Instance": "关闭云主机", "Stop auto refreshing data": "关闭自动刷新数据", "Stop refreshing data every {num} seconds": "关闭每{num}秒自动刷新数据", "Stopped": "已关闭", "Storage": "存储", "Storage Backends": "存储后端", "Storage Capacity(GiB)": "存储容量(GiB)", "Storage Cluster Bandwidth": "存储集群带宽", "Storage Cluster IOPS": "存储集群IOPS", "Storage Cluster OSD Latency": "存储集群OSD延迟", "Storage Cluster Status": "存储集群状态", "Storage Cluster Usage": "存储集群使用率", "Storage Clusters": "存储集群", "Storage IOPS": "存储IOPS", "Storage Interface": "Storage接口", "Storage Policy": "存储权限", "Storage Pool Capacity Usage": "存储池容量使用情况", "Storage Types": "存储类型", "Sub Users": "组内用户列表", "Subnet": "子网", "Subnet Count": "子网数量", "Subnet Detail": "子网详情", "Subnet ID": "子网ID", "Subnet ID/Name": "子网ID/名称", "Subnet Name": "子网名称", "Subnets": "子网", "Subordinate Projects": "所属项目列表", "Subordinate User Groups": "所属用户组列表", "Succeeded": "成功", "Success": "成功", "Sudan": "苏丹", "Supports resumable transfer (recommended when uploading a large image)": "支持断点续传(推荐上传大镜像时使用)", "Suriname": "苏里南", "Suspend": "挂起", "Suspend Complete": "挂起完成", "Suspend Failed": "挂起失败", "Suspend In Progress": "正在挂起", "Suspend Instance": "挂起云主机", "Suspended": "挂起", "Suspending": "挂起中", "Swaziland": "斯威士兰", "Sweden": "瑞典", "Switch ID": "交换机ID", "Switch Info": "交换机信息", "Switch Language": "切换语言", "Switch Project": "切换项目", "Switzerland": "瑞士", "Syncing": "同步中", "Syrian Arab Republic": "叙利亚", "System": "系统", "System Config": "系统配置", "System Disk": "系统盘", "System Info": "系统信息", "System Load": "系统负载", "System Roles": "系统角色", "System Running Time": "系统运行时间", "System is error, please try again later.": "系统出错，请稍后再试。", "TCP": "TCP", "TCP Connections": "TCP连接数", "TLS Disabled": "TLS禁用", "TTL": "TTL", "TTL (Time to Live) for the zone.": "区域的 TTL（生存时间）。", "Tag is no longer than 60 characters": "标签名长度不超过60个字符", "Tags": "标签", "Tags Info": "标签信息", "Tags are not case sensitive": "标签不区分大小写", "Taiwan": "台湾", "Tajikistan": "塔吉克", "Take effect after restart": "重启后生效", "Tanzania": "坦桑尼亚", "Target Compute Host": "目标计算节点", "Target IP Address": "目标IP地址", "Target Port": "目标网卡", "Target Project": "目标项目", "Target Project ID": "目标项目ID", "Target Project ID/Name": "目标项目ID/名称", "Target Project Name": "目标项目名称", "Target Storage Backend": "目标存储后端", "Target Tenant": "目标项目", "Task State": "任务状态", "Template Content": "模板内容", "Template Name": "模板名称", "Text Record": "文字记录", "Thailand": "泰国", "That is, after how many consecutive failures of the health check, the health check status of the back-end cloud server is changed from normal to abnormal": "即健康检查连续失败多少次后，将后端云服务器的健康检查状态由正常改为不正常", "The DNS nameserver to use for this cluster template": "用于此集群模板的 DNS 名称服务器", "The Federation of Saint Kitts and Nevis": "圣基茨和尼维斯", "The Provider is the encryption provider format (e.g. \"luks\")": "提供者是提供者加密格式（例如\"luks\"）", "The Republic of Macedonia": "马其顿", "The Republic of South Sudan": "南苏丹共和国", "The SSH key is a way to remotely log in to the cluster instance. If it’s not set, the value of this in the template will be used.": "SSH 密钥是一种远程登录集群实例的方式，如果不设置，将使用模板的值。", "The SSH key is a way to remotely log in to the cluster instance. The cloud platform only helps to keep the public key. Please keep your private key properly.": "SSH 密钥是一种远程登录集群实例的方式，云平台只帮助保管公钥，请妥善保管自己的私钥。", "The SSH key is a way to remotely log in to the instance. The cloud platform only helps to keep the public key. Please keep your private key properly.": "SSH 密钥是一种远程登录云主机的方式，云平台只帮助保管公钥，请妥善保管自己的私钥。", "The amphora instance is required for load balancing service setup and is not recommended": "amphora 相关的云主机为负载均衡服务搭建所需，不建议选择", "The associated floating IP, virtual adapter, volume and other resources will be automatically disassociated.": "绑定的浮动IP、网卡、云硬盘等资源将自动解绑。", "The certificate contains information such as the public key and signature of the certificate. The extension of the certificate is \"pem\" or \"crt\", you can directly enter certificate content or upload certificate file.": "证书包含证书的公钥和签名等信息，证书扩展名为”pem”或”crt”，您可直接输入证书内容或上传证书文件。", "The changed node count can not be equal to the current value": "变更后的节点数量不可以等于当前节点数量", "The command to execute": "要执行的命令", "The container memory size in MiB": "以 MiB 为单位的容器内存大小", "The container runtime tool to create container with": "用于创建容器的容器运行时工具", "The creation instruction has been issued, please refresh to see the actual situation in the list.": "创建指令已下发，请刷新查看云主机列表中的实际情况。", "The creation instruction was issued successfully, instance: {name}. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.": "创建指令下发成功，实例名称：{name}。 \n 您可等待几秒关注列表数据的变更或是手动刷新数据，以获取最终展示结果。", "The current operation requires the instance to be shut down:": "当前操作需要云主机在关机状态下进行：", "The current platform has not yet enabled the {name} management module. Please contact the administrator to enable it": "当前平台暂未开启{name}管理模块，请联系管理员开启", "The description can be up to 255 characters long.": "描述最长为255字符", "The disk size in GiB for per container": "以 GiB 为单位的容器磁盘大小", "The domain name can only be composed of letters, numbers, dashes, in A dash cannot be at the beginning or end, and a single string cannot exceed more than 63 characters, separated by dots; At most can support 30 domain names, separated by commas;The length of a single domain name does not exceed 100 characters, and the total length degree does not exceed 1024 characters.": "域名只能由字母，数字，中划线组成，中划线不能在开头或末尾，单个字符串不超过63个字符，字符串间以点分隔；最多可支持30个域名，域名间以英文逗号分隔；单个域名长度不超过100个字符，且总长度不超过1024个字符。", "The entire inspection process takes 5 to 10 minutes, so you need to be patient. After the registration is completed, the node configuration status will return to the manageable status.": "检查的整个过程需要耗费 5 到 10 分钟时间，您需要耐心等待。在完成注册后，节点配置状态会重新回到可管理状态。", "The entrypoint which overwrites the default ENTRYPOINT of the image": "它将覆盖镜像默认的入口点", "The feasible configuration of cloud-init or cloudbase-init service in the image is not synced to image's properties, so the Login Name is unknown.": "镜像中的cloud-init或cloudbase-init服务的预制配置未同步至镜像属性, 登录名未知", "The file with the same name will be overwritten.": "对同名文件将会进行文件覆盖操作。", "The floating IP configured with port forwardings cannot be bound": "不允许绑定配置了端口转发的浮动IP", "The format of the certificate content is: by \"----BEGIN CERTIFICATE-----\" as the beginning,\"-----END CERTIFICATE----\" as the end, 64 characters per line, the last line does not exceed 64 characters, and there cannot be blank lines.": "证书内容格式为：以”-----BEGIN CERTIFICATE-----”作为开头，以“-----END CERTIFICATE----”作为结尾，每行64字符，最后一行不超过64字符，不能有空行。", "The host name of this container": "容器的主机名", "The http_proxy address to use for nodes in cluster": "用于集群中节点的HTTP代理地址", "The https_proxy address to use for nodes in cluster": "用于集群中节点的HTTPS代理地址", "The image is not existed": "镜像资源不存在", "The instance architecture diagram mainly shows the overall architecture composition of the instance. If you need to view the network topology of the instance, please go to: ": "云主机架构图主要展示云主机的总体架构组成。如果需要查看云主机的网络拓扑，请转到：", "The instance deleted immediately cannot be restored": "立即删除的云主机无法恢复", "The instance has been locked. If you want to do more, please unlock it first.": "该云主机已被锁定。如果要做更多操作，请先解锁。", "The instance is not shut down, unable to restore.": "云主机不处于关机状态，不支持恢复备份操作。", "The instance which is boot from volume will create snapshots for each mounted volumes.": "从卷启动的云主机将为每个挂载的卷创建快照。", "The instances in the affinity group are allocated to the same physical machine as much as possible, and when there are no more physical machines to allocate, the normal allocation strategy is returned.": "将亲和组内的云主机尽量分配到同一物理机上，当没有更多物理机可分配时，回归普通分配策略。", "The instances in the affinity group are strictly allocated to the same physical machine. When there are no more physical machines to allocate, the allocation fails.": "将亲和组内的云主机严格分配到同一物理机上，当没有更多物理机可分配时，则分配失败。", "The instances in the anti-affinity group are allocated to different physical machines as much as possible. When there are no more physical machines to allocate, the normal allocation strategy is returned.": "将反亲和组内的云主机尽量分配到不同物理机上，当没有更多物理机可分配时，回归普通分配策略。", "The instances in the anti-affinity group are strictly allocated to different physical machines. When there are no more physical machines to allocate, the allocation fails.": "将反亲和组内的云主机严格分配到不同物理机上，当没有更多物理机可分配时，则分配失败。", "The ip address {ip} is duplicated, please modify it.": "IP 地址 {ip} 已重复，请修改。", "The ip is not within the allocated pool!": "该ip不在分配的资源池范围内！", "The ip of external members can be any, including the public network ip.": "外部成员的IP可以是任何IP，包括公网IP。", "The key pair allows you to SSH into your newly created instance. You can select an existing key pair, import a key pair, or generate a new key pair.": "密钥对允许您SSH到您新创建的实例。 您可以选择一个已存在的密钥对、导入一个密钥对或生成一个新的密钥对。", "The kill signal to send": "要发送的终止信号", "The limit of cluster instance greater than or equal to 1.": "集群实例的配额必须大于或者等于1。", "The maximum batch size is {size}, that is, the size of the port range cannot exceed {size}.": "批量的上限为{size}个，即端口范围大小不可超过{size}。", "The maximum transmission unit (MTU) value to address fragmentation. Minimum value is 68 for IPv4, and 1280 for IPv6.": "地址片段的最大传输单位。IPv4最小68，IPv6最小1280。", "The min size is {size} GiB": "最小内存为 {size} GiB", "The name of the physical network to which a port is connected": "端口连接到的物理网络的名称", "The name should be end with \".\"": "", "The name should contain letter or number, the length is 1 to 16, characters can only contain \"0-9, a-z, A-Z, -, _.\"": "名称应包含字母或数字，长度为 1 到 16，且字符只能包含“0-9、a-z、A-Z、-、_”。", "The name should contain letter or number, the length is 2 to 64, characters can only contain \"0-9, a-z, A-Z, -, _.\"": "名称应包含字母或数字，长度为 2 到 64，且字符只能包含“0-9、a-z、A-Z、-、_”。", "The name should start with letter or number, and be a string of 2 to 255, characters can only contain \"0-9, a-z, A-Z, -, _, .\"": "名称应以字母或数字开头，长度为 2 到 255，且只包含“0-9, a-z, A-Z, -, _, .”。", "The name should start with upper letter or lower letter, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].:^\".": "名称应以大写字母或小写字母开头，最长为128字符，且只包含“0-9, a-z, A-Z, \"'-_()[].:^”。", "The name should start with upper letter or lower letter, characters can only contain \"0-9, a-z, A-Z, -, _, .\"": "名称应以大写字母或小写字母开头，且字符只能包含“0-9、a-z、A-Z、-、_、.”。", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].\".": "名称应以大写字母，小写字母或中文开头，最长为128字符，且只包含“0-9, a-z, A-Z, \"'-_()[].”。", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].:^\".": "名称应以大写字母，小写字母或中文开头，最长为128字符，且只包含“0-9, a-z, A-Z, \"'-_()[].:^”。", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_.\".": "名称应以大写字母，小写字母或中文开头，最长为128字符，且只包含“0-9, a-z, A-Z, \"-'_.”。", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 64, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].^\".": "名称应以大写字母，小写字母或中文开头，最长为64字符，且只包含“0-9, a-z, A-Z, \"'-_()[].^”。", "The name should start with upper letter, lower letter or chinese, and be a string of 3 to 63, characters can only contain \"0-9, a-z, A-Z, chinese, -, .\".": "名称应以大写字母，小写字母或中文开头，长度为3-63字符，且只包含“0-9, a-z, A-Z, 中文, -, .”。", "The name should start with upper letter, lower letter, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, -, _\".": "名称应以大写字母或小写字母开头，最长为128字符，且只包含“0-9, a-z, A-Z, -, _”。", "The name should start with upper letter, lower letter, and be a string of 2 to 255, characters can only contain \"0-9, a-z, A-Z, -, ., _\".": "名称应以大写字母或小写字母开头，长度为2-255字符，且只包含“0-9, a-z, A-Z, -, ., _”。", "The name should start with upper letter, lower letter, and be a string of 3 to 63, characters can only contain \"0-9, a-z, A-Z, -\".": "名称应以大写字母，小写字母开头，长度为3-63字符，且只包含“0-9, a-z, A-Z, -”。", "The new password cannot be identical to the current password.": "用户新密码不能与原密码相同。", "The no_proxy address to use for nodes in cluster": "集群中节点多用，无需代理", "The number of allowed key pairs for each user.": "每个用户允许创建的密钥数量", "The number of vCPU cores should not exceed the maximum number of CPU cores of the physical node. Otherwise it will cause fail to schedule to any physical node when creating instance.": "vCPU核数不应该超过物理节点的最大CPU核数，否则会导致云主机创建时无法调度到任何物理节点。", "The number of virtual cpu for this container": "容器的虚拟 CPU 数量", "The optional headers to insert into the request before it is sent to the backend member.": "在将请求发送到后端成员之前插入到请求中的可选标头。", "The password must not be the same as the previous": "新密码不能与以前的密码相同", "The password must not be the same as the previous two": "用户新密码不能与重置前的密码一致", "The password must not be the same as the previous {num}": "用户新密码不能与前{num}次密码相同", "The port created here will be automatically deleted when detach. If you need a reusable port, please go to the Virtual Adapter page to create and attach the port to instance.": "此处创建的网卡会在卸载的时候被自动删除，如果需要可复用的网卡，请前往虚拟网卡页面创建再从虚拟网卡页面绑定云主机。", "The private key content format is: with \"-----BEGIN RSA PRIVATE KEY-----\" as the beginning,\"-----END RSA PRIVATE KEY-----\" as the end, 64 characters per line, the last line does not exceed 64 characters, and there cannot be blank lines.": "私钥内容格式为：以“-----BEGIN RSA PRIVATE KEY-----”，以“-----END RSA PRIVATE KEY-----”作为结尾，每行64字符，最后一行不超过64字符，不能有空行。", "The private key of the certificate, the extension of the private key is \"key\", you can directly enter the content of the private key file or upload a private key that conforms to the format document.": "证书的私钥，私钥扩展名为”key”，您可直接输入私钥文件内容或上传符合格式的私钥文件。", "The resource class of the scheduled node needs to correspond to the resource class name of the flavor used by the ironic instance (for example, the resource class name of the scheduling node is baremetal.with-GPU, and the custom resource class name of the flavor is CUSTOM_BAREMETAL_WITH_GPU=1).": "被调度节点的资源类需要与裸机实例使用的云主机类型的资源类名称对应（比如：调度节点的资源类名称为 baremetal.with-GPU，云主机类型的资源类名称为CUSTOM_BAREMETAL_WITH_GPU=1 ）。", "The resource has been deleted": "该资源已被删除", "The root and os_admin are default users and cannot be created!": "root 和 os_admin 是默认用户，不能创建！", "The root disk of the instance has snapshots": "云主机的根盘有快照", "The security group is similar to the firewall function and is used to set up network access control. ": "安全组类似防火墙功能，用于设置网络访问控制。", "The security group is similar to the firewall function for setting up network access control, or you can go to the console and create a new security group. (Note: The security group you selected will work on all virtual LANs on the instances.)": "安全组类似防火墙功能，用于设置网络访问控制，您也可以前往控制台新建安全组。（注：您所选的安全组将作用于云主机的全部虚拟网卡。）", "The selected VPC/subnet does not have IPv6 enabled.": "所选的VPC/子网未开通IPv6", "The selected network has no subnet": "选择的网络没有子网", "The selected project is different from the project to which the network belongs. That is, the subnet to be created is not under the same project as the network. Please do not continue unless you are quite sure what you are doing.": "您选择的项目与网络所属项目不一致，即您将创建的子网与网络不在同一项目下。除非你非常确认自己在做什么，否则请不要继续。", "The session has expired, please log in again.": "会话已过期，请重新登录。", "The shelved offloaded instance only supports immediate deletion": "已归档的云主机仅支持立即删除", "The size of the external port range is required to be the same as the size of the internal port range": "源端口范围的大小要与目标端口范围的大小相同", "The start source is a template used to create an instance. You can choose an image or a bootable volume.": "启动源是用来创建云主机的模板， 您可以选择镜像或者可启动的卷。", "The starting number must be less than the ending number": "起始数字必须小于结束数字", "The timeout for cluster creation in minutes.": "集群创建超时时间，以分钟为单位。", "The timeout period of waiting for the return of the health check request, the check timeout will be judged as a check failure": "等待健康检查请求返回的超时时间，检查超时将会被判定为一次检查失败", "The total amount of data is { total }, and the interface can support downloading { totalMax } pieces of data. If you need to download all the data, please contact the administrator.": "数据总量为{ total }，界面可支持下载{ totalMax }条数据，如需下载全部数据，请联系管理员。", "The trait name of the flavor needs to correspond to the trait of the scheduling node; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all necessary traits (for example: the trait of the scheduling node has HW_CPU_X86_VMX trait, and the flavor adds HW_CPU_X86_VMX, it can be scheduled to this node for necessary traits).": "云主机类型的特性名称需要与调度节点的特性对应；通过给裸机实例注入必需特性，计算服务将只调度实例到具有所有必需特性的裸金属节点（比如：调度节点的有 HW_CPU_X86_VMX的特性，云主机类型添加HW_CPU_X86_VMX为必需特性，可以调度到此节点）。", "The trait of the scheduled node needs to correspond to the trait of the flavor used by the ironic instance; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all the necessary traits (for example, the ironic instance which use the flavor that has HW_CPU_X86_VMX as a necessary trait, can be scheduled to the node which has the trait of HW_CPU_X86_VMX).": "被调度节点的特性需要与裸机实例使用的云主机类型的特性对应；通过给裸机实例注入必需特性，计算服务将只调度实例到具有所有必需特性的裸金属节点（比如：调度节点的有 HW_CPU_X86_VMX的特性， 云主机类型添加HW_CPU_X86_VMX为必要特性，可以调度到此节点）。", "The unit suffix must be one of the following: Kb(it), Kib(it), Mb(it), Mib(it), Gb(it), Gib(it), Tb(it), Tib(it), KB, KiB, MB, MiB, GB, GiB, TB, TiB. If the unit suffix is not provided, it is assumed to be KB.": "单位后缀必须是以下之一：Kb(it)、<PERSON><PERSON>(it)、<PERSON>b(it)、<PERSON>b(it)、Gb(it)、Gib(it)、Tb(it)、Tib(it)、KB、 KiB、MB、MiB、GB、GiB、TB、TiB。如果未提供单位后缀，则假定为千字节。", "The user has been disabled, please contact the administrator": "用户已被禁用，请联系管理员", "The user needs to ensure that the input is a shell script that can run completely and normally.": "请确保输入的是能完整正常运行的 shell 脚本。", "The value of the upper limit of the range must be greater than the value of the lower limit of the range.": "范围上限的数值必须要大于范围下限的数值", "The volume associated with the backup is not available, unable to restore.": "云硬盘不处于可用状态，不支持恢复备份操作。", "The volume status can be reset to in-use only when the previous status is in-use.": "只有当之前的状态为使用中时，才将云硬盘状态重置为使用中。", "The volume type needs to be consistent with the volume type when the snapshot is created.": "创建云硬盘的云硬盘类型需要和创建快照时间点的云硬盘类型保持一致。", "The volume type needs to set \"multiattach\" in the metadata to support shared volume attributes.": "云硬盘类型需在元数据中设置\"multiattach\"，才可支持共享盘属性。", "The working directory for commands to run in": "用于运行命令的工作目录", "The zone name should end with \".\"": "区域名称应以“.”结尾。", "The {action} instruction has been issued, instance: {name}. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.": "{action}指令已下发，实例名称：{name}。 \n 您可等待几秒关注列表数据的变更或是手动刷新数据，以获取最终展示结果。", "The {action} instruction has been issued. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.": "{action}指令已下发。 \n 您可等待几秒关注列表数据的变更或是手动刷新数据，以获取最终展示结果。", "The {name} has already been used by other {resource}({content}), please change.": "{name} 已经被其他{resource}使用({content})，请修改。", "The {name} {ports} have already been used, please change.": "{name} {ports} 已经被使用，请修改。", "There are resources that cannot {action} in the selected resources, such as:": "您选中的资源中有无法{action}的资源，如：", "There are resources that cannot {action} in the selected resources.": "您选中的资源中有无法{action}的资源。", "There are resources under the project and cannot be deleted.": "项目下存在资源，无法执行删除操作。", "There is currently a image that needs to continue uploading. Please refresh page and upload the image. The new image cannot be used for the time being.": "当前存在需要继续上传的镜像，请刷新或返回完成上传该镜像，新镜像暂不能使用断点续传功能。", "There is currently no file to paste.": "当前没有需要粘贴的文件。", "This operation creates a security group with default security group rules for the IPv4 and IPv6 ether types.": "此操作会创建带有 IPv4 和 IPv6 的默认安全组规则的安全组。", "This service will automatically query the configuration (CPU, memory, etc.) and MAC address of the physical machine, and the ironic-inspector service will automatically register this information in the node information.": "此服务将对在对物理机的配置（CPU、内存等）和 mac 地址进行自动查询, 并且 ironic-inspector 服务会将这些信息自动注册入节点信息中。", "This will delete all child objects of the load balancer.": "这会删除所有LB下的资源", "Threads Activity Trends": "线程活动趋势", "Time Interval: ": "时间间隔：", "Time To Live": "生存时间", "Time To Live in seconds.": "以秒为单位的生存时间。", "Time between running the check in seconds": "运行检查之间的时间（以秒为单位）", "Timeout(Minute)": "创建超时（分钟）", "Timeout(s)": "检查超时时间(秒)", "Tips: without domain means \"Default\" domain.": "提示：不输入域名则默认为“Default”域名。", "To open": "去开通", "Today CPU usage > 80% alert": "今日CPU使用率大于80%的告警", "Today Memory usage > 80% alert": "今日内存使用率大于80%的告警", "Togo": "多哥", "Tokelau": "托克劳", "Tonga": "汤加", "Too many disks mounted on the instance will affect the read and write performance. It is recommended not to exceed 16 disks.": "云主机挂载太多磁盘会影响读写性能，建议不要超过 16 块。", "Topic": "主题", "Topology": "网络拓扑", "Total": "总计", "Total Capacity": "总容量(GB)", "Total Connections": "连接总数", "Total Consumers": "消费者总数", "Total Containers": "容器总数", "Total Exchanges": "交换机总数", "Total IPs": "所有IP", "Total Queues": "队列总数", "Total Ram": "总内存", "Total {total} items": "总计：{total}", "Trait Properties": "特性属性", "Traits": "特性", "Transfer ID": "转让ID", "Transfer Name": "转让名称", "Transferred": "转换", "Transform Protocol": "转换协议", "Trinidad and Tobago": "特立尼达和多巴哥", "True": "是", "Tunisia": "突尼斯", "Turkey": "土耳其", "Turkmenistan": "土库曼", "Turks and Caicos Islands": "土克斯及开科斯群岛", "Tuvalu": "图瓦卢", "Two-way authentication": "双向认证", "Type": "类型", "UDP": "UDP", "UDPLite": "", "UNHEALTHY": "不健康", "UNKNOWN": "未知", "UOS": "UOS", "UPDATE COMPLETE": "更新完成", "UPDATE FAILED": "更新失败", "UPDATE IN PROGRESS": "更新中", "USB Info": "USB 信息", "USB Parameters": "USB参数", "USB model, used when configuring instance flavor": "USB 型号，配置云主机类型时使用", "USER": "用户", "UStack": "UStack有栈后台管理系统", "UStack Reports": "云平台报表", "UStack Services": "UStack服务", "UUID": "", "Ubuntu": "", "Uccps": "容器云平台", "Uganda": "乌干达", "Ukraine": "乌克兰", "Unable to create instance: batch creation is not supported when specifying IP.": "无法创建云主机：指定IP时，不支持批量创建云主机。", "Unable to create instance: insufficient quota to create resources.": "无法创建云主机：超过配额。", "Unable to create volume: insufficient quota to create resources.": "无法创建云硬盘：超过配额。", "Unable to delete router \"{ name }\". External gateway is opened, please clear external gateway first.": "无法删除路由器\"{ name }\"。公网网关已打开，请先关闭公网网关。", "Unable to get {name} detail.": "无法获取{name}详情。", "Unable to get {name}.": "无法获取{name}。", "Unable to get {title}, please go back to ": "无法获取{title}，请返回", "Unable to get {title}, please go to ": "无法获取{title}，请访问", "Unable to paste into the same folder.": "无法粘贴到同一文件夹下。", "Unable to render form": "无法生成表单", "Unable to {action} {name}.": "无法{ action }{name}。", "Unable to {action}, because : {reason}, instance: {name}.": "无法{action}，原因：{reason}，实例名称：{name}。", "Unable to {action}, instance: {name}.": "无法{action}，实例名称：{name}。", "Unable to {action}.": "无法{action}。", "Unable to {title}, please go back to ": "无法{title}，请访问", "Unattached": "未挂载", "Unavailable": "不可用", "Unbootable": "不可启动", "Unbounded": "未绑定", "United Arab Emirates": "阿拉伯联合酋长国", "United Kingdom": "英国", "United States": "美国", "Unknown": "未知", "Unless Stopped": "除非停止", "Unless you know clearly which AZ to create the volume in, you don not need to fill in here.": "除非很明确知道应该在哪个 AZ 中创建 Volume，否则此处不用填", "Unlimit": "无限制", "Unlock": "解锁", "Unlock Instance": "解锁云主机", "Unmanage Error": "取消管理出错", "Unmanage Starting": "取消管理已开始", "Unmanaged": "已取消管理", "Unpause": "恢复", "Unpause Container": "恢复容器", "Unpause Instance": "恢复云主机", "Unrescuing": "", "Unrestricted": "不受限制", "Unset": "取消设置", "Unshelve": "取消归档", "Unshelve Instance": "取消归档云主机", "Unshelving": "", "Unused": "未用", "Up": "正常", "Update": "更新", "Update Access": "访问控制", "Update At": "更新于", "Update Cluster Template": "更新集群模板", "Update Complete": "更新完成", "Update Failed": "更新失败", "Update In Progress": "正在更新", "Update Record Set": "更新记录集", "Update Segment": "更新分组", "Update Status": "更新状态", "Update Template": "更新模板", "Update User Password": "修改用户密码", "Update user password": "修改用户密码", "Updated": "更新于", "Updated At": "更新于", "Updating": "更新中", "Updating Password": "更新密码中", "Upgrade Cluster": "升级集群", "Upload File": "上传文件", "Upload Type": "上传方式", "Upload progress": "上传进度", "Uploading": "上传中", "Uploads with mirrors are interrupted. The name of the mirror file is: {name}. Do you want to continue the image? Please select the mirror file in the button below and click the \"Continue\" button; Tips: If the wrong file is selected, it will not be interrupted. Clicking the \"Delete\" button will delete the task of uploading the image.": "存在镜像的上传被中断。该镜像文件的名字为：{name}。您想继续续传该镜像吗？请在下面按钮内选择该镜像文件并点击“继续”按钮；温馨提示：如果选错文件将无法断点续传。\n点击“删除”按钮，将会删除上传该镜像的任务。", "Uruguay": "乌拉圭", "Usage": "使用率", "Usage Type": "使用类型", "Usb Controller": "USB控制器", "Use Type": "使用类型", "Used": "已使用", "Used IPs": "已用IP", "Used by tunnel(s): {names}. ID(s): {ids}": "被隧道使用中：{names}。 ID：{ids}", "Used to restrict whether the application credential may be used for the creation or destruction of other application credentials or trusts.": "用于限制应用程序凭证是否可用于创建或销毁其他应用程序凭证或信任。", "User": "用户", "User Account": "用户账户", "User Center": "用户中心", "User Data": "用户数据", "User Detail": "用户详情", "User Edit": "编辑用户", "User Group": "用户组", "User Group Detail": "用户组详情", "User Group ID/Name": "用户组ID/名称", "User Group Name": "用户组名称", "User Group Num": "用户组数", "User Group Num: ": "用户组数: ", "User Groups": "用户组", "User ID": "用户ID", "User ID/Name": "用户ID/名称", "User Name": "用户名称", "User Num": "用户数", "User Num: ": "用户数： ", "User name can not be duplicated": "用户名称不可重复", "User need to change password": "用户需要修改密码后才能使用云平台", "Username": "用户名", "Captcha incorrect": "验证码不正确", "Captcha": "验证码", "Login failures exceeded": "登录失败次数过多，请稍后再试", "Too many failed login attempts. Please try again later after 1 minute.": "登录失败次数过多，请1分钟后再试。", "Username or password is incorrect": "用户名或密码不正确", "Users": "用户", "Using cascading deletion, when the volume has snapshots, the associated snapshot will be automatically deleted first, and then the volume will be deleted, thereby improving the success rate of deleting the volume.": "使用联级删除，当该云硬盘有快照时，会先自动删除关联快照后，再删除该云硬盘，从而提升删除云硬盘的成功率。", "Using server groups, you can create cloud hosts on the same/different physical nodes as much as possible to meet the affinity/non-affinity requirements of business applications.": "使用云主机组功能，您可以将云主机尽量创建在同一个/不同的物理节点上，满足业务应用的亲和/非亲和性需求。", "Uzbekistan": "乌兹别克斯坦", "VCPU (Core)": "VCPU (核)", "VCPUs": "虚拟CPU", "VDI - VirtualBox compatible image format": "VDI - VirtualBox 兼容的图像格式", "VGPU": "", "VGPU (Core)": "VGPU (核)", "VHD - VirtualPC compatible image format": "VHD - VirtualPC 兼容的图像格式", "VIF Details": "VIF详情", "VIF Type": "VIF类型", "VIR Domain Event": "VIR域事件", "VMDK - Hyper-V compatible image format": "VMDK - Hyper-V 兼容的图像格式", "VNC": "", "VNIC Type": "VNIC类型", "VPN": "VPN", "VPN EndPoint Groups": "VPN端点组", "VPN Gateways": "VPN网关", "VPN Service": "VPN服务", "VPN Service ID": "VPN服务ID", "VPNs": "VPN", "VRRP": "", "Valid": "有效", "Values": "值", "Vanuatu": "瓦努阿图", "Vatican City State (Holy See)": "梵蒂冈", "Vendor Interface": "", "Venezuela": "委内瑞拉", "Verifying": "验证", "Version": "版本", "Vietnam": "越南", "View": "查看", "View Detail": "查看详情", "View Full Log": "查看完整日志", "View Rules": "查看规则", "View virtual adapters": "仅查看虚拟网卡", "Virgin Islands (U.S.)": "维尔京群岛", "Virtual Adapter": "虚拟网卡", "Virtual Adapter ID": "虚拟网卡ID", "Virtual LAN": "虚拟网卡", "Virtual LANs": "块虚拟网卡", "Virtual Resource Overview": "虚拟资源总览", "Virtual Resources Used": "虚拟资源用量", "Virtual adapter mainly used for binding instance and other operations, occupying the quota of the port.": "虚拟网卡，主要用于绑定云主机等操作，占用端口的配额。", "Visibility": "可见性", "Visualization Compute Optimized Type with GPU": "GPU虚拟化型", "Volume": "云硬盘", "Volume Backup": "云硬盘备份", "Volume Backup Capacity (GiB)": "云硬盘备份容量 (GiB)", "Volume Backup Detail": "云硬盘备份详情", "Volume Backup Name": "云硬盘备份名称", "Volume Backups": "云硬盘备份", "Volume Capacity (GiB)": "云硬盘容量 (GiB)", "Volume Detail": "云硬盘详情", "Volume Driver": "云硬盘驱动", "Volume ID": "云硬盘ID", "Volume ID/Name": "云硬盘ID/名称", "Volume Info": "云硬盘信息", "Volume Name": "云硬盘名称", "Volume Size": "云硬盘容量", "Volume Snapshot": "云硬盘快照", "Volume Snapshot Detail": "云硬盘快照详情", "Volume Snapshot Name": "云硬盘快照名称", "Volume Snapshots": "云硬盘快照", "Volume Source": "云硬盘源", "Volume Transfer": "云硬盘传输", "Volume Type": "云硬盘类型", "Volume Type Detail": "云硬盘类型详情", "Volume Types": "云硬盘类型", "Volumes": "云硬盘", "Wallis And Futuna Islands": "沃利斯和富图纳群岛", "Warn": "警告", "Weight": "权重", "Weights": "权重", "Welcome": "欢迎", "Western Sahara": "西撒哈拉", "When auto-expand/close is enabled, if there is no operation in the pop-up window, the pop-up window will be closed automatically after { seconds } seconds, and it will be automatically expanded when the displayed content changes.": "开启自动展开/关闭时，如在该弹窗内无操作，将在 { seconds } 秒后自动关闭弹窗，当展示内容有变化时，将自动展开。", "When the computing service starts the recycling instance interval, the instance will be stored in the recycling bin after deletion, and will be retained according to the corresponding time interval. You can choose to restore it within this period. After successful recovery, the status of the instance is running and related resources remain unchanged.": "当计算服务开启回收实例间隔时，删除后云主机会存放在回收站，按对应的时间间隔保留，在此期限内可以选择恢复。恢复成功后的云主机状态为运行中，且相关资源保持不变。", "When the volume is \"bootable\" and the status is \"available\", it can be used as a startup source to create an instance.": "云硬盘为“可启用”并且状态为“可用”时，可以作为启动源来创建云主机。", "When you do online backup of the volume that has been bound, you need to pay attention to the following points:": "当您对已经绑定的硬盘做在线备份时，需要注意以下几点：", "When you restore a backup, you need to meet one of the following conditions:": "当您恢复备份时，需要满足以下条件之一：", "When your Yaml file is a fixed template, variable variables can be stored in an environment variable file to implement template deployment. The parameters in the environment variable file need to match the parameters defined in the template file.": "当您的Yaml文件为一个固定模版时，可变变量可以存放在环境变量文件中来实现模版部署，环境变量文件中的参数需要与模版文件中定义的参数保持匹配。", "Whether enable or not using the floating IP of cloud provider.": "是否启用或不使用云服务商的浮动IP。", "Whether the Login Name can be used is up to the feasible configuration of cloud-init or cloudbase-init service in the image.": "用户名是否可登录取决于镜像中的cloud-init或cloudbase-init服务的预制配置", "Whether the boot device should be set only for the next reboot, or persistently.": "是否永久使用该引导设置。", "Which Network Interface provider to use when plumbing the network connections for this Node": "当为这个节点连接网络时，使用哪个网络接口提供者", "Windows": "", "Workdir": "工作目录", "Working Directory": "工作目录", "X86 Architecture": "X86架构", "YAML File": "YAML文件", "Yemen": "也门", "Yes": "是", "Yes - Create a new system disk": "是 - 创建新的系统盘", "You are not allowed to delete policy \"{ name }\" used by firewalls: { firewalls }.": "无法删除防火墙：{ firewalls } 使用中的策略\"{ name }\"。", "You are not allowed to delete policy \"{ name }\".": "无法删除策略\"{ name }\"。", "You are not allowed to delete router \"{ name }\".": "无法删除路由器\"{ name }\"。", "You are not allowed to delete rule \"{ name }\" in use.": "无法删除使用中的规则\"{ name }\"。", "You are not allowed to delete rule \"{ name }\".": "无法删除规则\"{ name }\"。", "You are not allowed to delete snapshot \"{ name }\", which is used by creating volume \"{volumes}\".": "无法删除创建了云硬盘 \"{volumes}\" 的快照 \"{ name }\"。", "You are not allowed to delete snapshot \"{ name }\".": "无法删除快照\"{ name }\"。", "You are not allowed to jump to the console.": "无法跳转到控制台。", "You are not allowed to { action } \"{ name }\".": "无法{ action }\"{ name }\"。", "You are not allowed to { action } {name}.": "无法{ action }{ name }。", "You are not allowed to {action}, instance: {name}.": "无法{ action }， 实例名称：{ name }。", "You are not allowed to {action}.": "无法{ action }。", "You can manually specify a physical node to create an instance.": "您可以手动指定一台物理节点来创建云主机。", "You don't have access to get {name}.": "您没有权限访问{name}。", "You may update the editable properties of the RBAC policy here.": "您可以在此处更新 RBAC 策略的可编辑属性。", "Yugoslavia": "南斯拉夫", "Zambia": "赞比亚", "Zimbabwe": "津巴布韦", "Zone": "区域", "Zone ID": "区域ID", "Zone ID/Name": "区域ID/名称", "Zone Name": "区域名称", "Zones Detail": "区域详情", "abandon stack": "废弃堆栈", "add access rule": "添加访问规则", "add network": "添加网络", "add router": "添加路由器", "all": "所有", "an optional string field to be used to store any vendor-specific information": "选填选型，用于储存供应商的特定信息", "application credential": "应用凭证", "associate floating ip": "绑定浮动IP", "attach interface": "挂载网卡", "authorized by group ": "由组授权", "auto": "自动", "auto_priority": "自动优先级", "availability zones": "可用域", "available": "可用", "bare metal node": "裸机节点", "bare metal nodes": "裸机节点", "be copied": "复制", "be cut": "剪切", "be deleted": "删除", "be rebooted": "重启", "be recovered": "恢复", "be released": "释放", "be soft rebooted": "软重启", "be started": "启动", "be stopped": "关闭", "capsules": "集合", "certificate": "证书", "cidr": "CIDR", "cinder services": "存储服务", "clusters": "集群", "clustertemplates": "集群模板", "compute hosts": "计算节点", "compute services": "计算服务", "configurations": "配置", "confirm resize or migrate": "确认修改配置/迁移", "connect subnet": "连接子网", "container objects": "容器对象", "containers": "容器", "create DSCP marking rule": "创建DSCP标记规则", "create a new network/subnet": "新建网络/子网", "create a new security group": "新建安全组", "create allowed address pair": "创建可用地址对", "create bandwidth limit rule": "创建带宽限制规则", "create baremetal node": "创建裸机节点", "create default pool": "创建资源池", "create encryption": "创建加密", "create firewall policy": "创建防火墙策略", "create flavor": "创建云主机类型", "create instance snapshot": "创建云主机快照", "create ipsec site connection": "创建IPsec站点连接", "create network": "创建网络", "create router": "创建路由", "create share": "创建共享", "create share group": "创建共享组", "create share group type": "创建共享组类型", "create share network": "创建共享网络", "create share type": "创建共享类型", "create stack": "创建堆栈", "create volume": "创建云硬盘", "create volume snapshot": "创建云硬盘快照", "create volume type": "创建云硬盘类型", "create vpn": "创建VPN", "create vpn endpoint group": "创建VPN端点组", "create vpn ike policy": "创建VPN IKE策略", "create vpn ipsec policy": "创建VPN IPsec策略", "data": "数据", "database backups": "数据库备份", "database instances": "数据库实例", "delete": "删除", "delete allowed address pair": "删除可用地址对", "delete application credential": "删除应用凭证", "delete bandwidth egress rules": "删除出方向带宽限制规则", "delete bandwidth ingress rules": "删除入方向带宽限制规则", "delete certificate": "删除证书", "delete container": "删除容器", "delete default pool": "删除资源池", "delete domain": "删除域", "delete dscp marking rules": "删除DSCP标记规则", "delete firewall": "删除防火墙", "delete flavor": "删除云主机类型", "delete group": "删除组", "delete host": "删除主机", "delete image": "删除镜像", "delete instance": "删除云主机", "delete instance snapshot": "删除云主机快照", "delete ipsec site connection": "删除IPsec站点连接", "delete ironic instance": "删除裸机", "delete keypair": "删除密钥", "delete listener": "删除监听器", "delete load balancer": "删除负载均衡", "delete member": "删除成员", "delete network": "删除网络", "delete policy": "删除策略", "delete port forwarding": "删除端口转发", "delete project": "删除项目", "delete qos policy": "删除QoS策略", "delete role": "删除角色", "delete router": "删除路由器", "delete rule": "删除规则", "delete segments": "删除分组", "delete stack": "删除stack", "delete static route": "删除静态路由", "delete subnet": "删除子网", "delete user": "删除用户", "delete virtual adapter": "删除虚拟网卡", "delete volume": "删除云硬盘", "delete volume backup": "删除云硬盘备份", "delete volume snapshot": "删除云硬盘快照", "delete vpn": "删除vpn", "delete vpn IKE policy": "删除vpn IKE策略", "delete vpn IPsec policy": "删除vpn IPsec策略", "delete vpn endpoint groups": "删除vpn端点组", "description": "描述", "detach instance": "从云主机解绑", "detach security group": "解绑安全组", "disable cinder service": "禁用存储服务", "disable compute service": "禁用计算服务", "disable neutron agent": "禁用网络服务", "disassociate floating ip": "解绑浮动IP", "disconnect subnet": "断开子网", "dns zones": "DNS区域", "domain": "域", "domains": "域", "download image": "下载镜像", "e.g. 2001:Db8::/48": "", "edit baremetal node": "编辑裸机节点", "edit default pool": "编辑资源池", "edit health monitor": "编辑健康检查器", "edit image": "编辑镜像", "edit instance snapshot": "编辑云主机快照", "edit member": "编辑成员", "edit system permission": "编辑系统角色", "egress": "出方向", "enable cinder service": "启用存储服务", "enable compute service": "启用计算服务", "enable neutron agent": "启用网络服务", "external port": "源端口", "external ports": "源端口", "extra specs": "额外规格", "firewall": "防火墙", "firewall policies": "防火墙策略", "firewall rule": "防火墙规则", "firewall rules": "防火墙规则", "firewalls": "防火墙", "flavor": "云主机类型", "floating ip": "浮动IP", "floating ips": "浮动IP", "heat services": "编排服务", "host aggregates": "主机集合", "hosts": "主机", "hypervisor": "虚拟机管理器", "image": "镜像", "images": "镜像", "in": "进", "ingress": "入方向", "insert": "插入", "insert rule": "插入规则", "instance": "云主机", "instance snapshot": "云主机快照", "instance snapshots": "云主机快照", "instance: {name}.": "实例名称：{name}。", "instances": "云主机", "internal port": "目标端口", "internal ports": "目标端口", "ipsec site connection": "IPsec站点连接", "jump to the console": "跳转到控制台", "keypair": "密钥", "keypairs": "密钥", "labels": "标签", "list page": "列表页", "listener": "监听器", "listeners": "监听器", "live migrate": "热迁移", "load balancer": "负载均衡", "lock instance": "锁定云主机", "manage ports": "管理端口", "manage qos spec": "管理QoS规格", "manage resource types": "管理资源类型", "message": "", "message.reason": "", "metadata": "元数据", "migrate": "迁移", "modify instance tags": "修改云主机标签", "modify project tags": "修改项目标签", "network": "网络", "networks": "网络", "neutron agent": "网络服务", "neutron agents": "网络服务", "ns1.example.com admin.example.com 2013022001 86400 7200 604800 300 <ul><li>The primary name server for the domain, which is ns1.example.com or the first name server in the vanity name server list.</li><li>The responsible party for the domain: admin.example.com.</li><li>A timestamp that changes whenever you update your domain.</li><li>The number of seconds before the zone should be refreshed.</li><li>The number of seconds before a failed refresh should be retried.</li><li>The upper limit in seconds before a zone is considered no longer authoritative.</li><li>The negative result TTL (for example, how long a resolver should consider a negative result for a subdomain to be valid before retrying).</li></ul>": "ns1.example.com admin.example.com 2013022001 86400 7200 604800 300 <ul><li>域的主名称服务器，即 ns1.example.com 或名称服务器列表中的第一个名称服务器。</li><li>域的负责方：admin.example.com。</li><li>每当您更新域时都会更改的时间戳。</li><li>区域刷新前的秒数。</li><li>刷新失败之前应该重试的秒数。</li><li>区域被视为不再具有权威性之前的秒数上限。</li><li> 否定结果 TTL（例如，在重新尝试之前，解析器应该在多长时间内认为子域的否定结果有效）。</li></ul>", "open external gateway": "开启公网网关", "out": "出", "paste files to folder": "粘贴文件到文件夹下", "pause instance": "暂停云主机", "phone": "手机", "please select network": "请选择网络", "please select subnet": "请选择子网", "Please input the captcha!": "请输入验证码!", "policy": "策略", "port": "端口", "port forwarding": "端口转发", "port forwardings": "端口转发", "port groups": "端口组", "ports": "端口", "project": "项目", "projects": "项目", "qemu_guest_agent enabled": "启用qemu_guest_agent", "qoS policy": "QoS策略", "qos specs": "QoS规格", "quota set to -1 means there is no quota limit on the current resource": "配额为设为 -1 时表示当前资源无配额限制", "read": "读", "reboot instance": "重启云主机", "rebuild instance": "重建云主机", "receive": "接收", "recordsets": "记录集", "recover instance": "恢复云主机", "recycle bins": "回收站", "release fixed ip": "释放内网IP", "remove network": "移除网络", "remove router": "移除路由器", "remove rule": "移除规则", "reserved_host": "保留主机", "resize": "更改配置", "resume instance": "恢复云主机", "revert resize or migrate": "回滚修改配置/迁移", "rh_priority": "rh优先级", "role": "角色", "roles": "角色", "router": "路由器", "routers": "路由", "security group": "安全组", "security group rules": "安全组规则", "security groups": "安全组", "segments": "分组", "select an existing port": "选择已有网卡", "server group": "云主机组", "server groups": "云主机组", "services": "服务", "settings": "配置", "share": "共享", "share access rules": "共享访问规则", "share group": "共享组", "share group type": "共享组类型", "share groups": "共享组", "share instance": "共享实例", "share instances": "共享实例", "share metadata": "共享元数据", "share network": "共享网络", "share server": "共享服务器", "share servers": "共享服务器", "share type": "共享类型", "share types": "共享类型", "shelve instance": "归档云主机", "smtp.example.com": "smtp.example.com", "soft reboot instance": "软重启云主机", "stack": "stack堆栈", "stack events": "部署日志", "stack resources": "堆栈资源", "stacks": "stack堆栈", "start instance": "启动云主机", "static routers": "静态路由", "stop instance": "关闭云主机", "storage backend": "存储后端", "subnet": "子网", "subnets": "子网", "suspend instance": "挂起云主机", "the Republic of Abkhazia": "阿布哈兹", "the folder is not empty": "文件夹非空", "the policy is in use": "策略正在使用中", "the router has connected subnet": "路由器有连接的子网", "the vpn gateway is in use": "VPN网关正在使用中", "time / 24h": "次/24小时", "to delete": "即将删除", "transmit": "发送", "unlock instance": "解锁云主机", "unpause instance": "恢复云主机", "unshelve instance": "取消归档云主机", "update": "更新", "update status": "更新状态", "update template": "更新模板", "used": "已使用", "user": "用户", "user group": "用户组", "user groups": "用户组", "users": "用户", "vCPUs": "虚拟CPU", "vCPUs and ram are not used for bare metal scheduling": "vCPUs 和 ram 不用于裸机调度", "volume": "云硬盘", "volume backup": "云硬盘备份", "volume backups": "云硬盘备份", "volume capacity": "云硬盘容量", "volume snapshot": "云硬盘快照", "volume snapshots": "云硬盘快照", "volume type": "云硬盘类型", "volume type qos": "云硬盘类型Qos", "volume type {type}": "云硬盘类型 {type} ", "volume type {type} capacity": "云硬盘类型 {type} 容量", "volume types": "云硬盘类型", "volumes": "云硬盘", "vpn IKE policy": "VPN IKE策略", "vpn IPsec policy": "VPN IPsec策略", "vpn endpoint groups": "VPN端点组", "vpn services": "VPN网关", "write": "写", "{ name } Format Error (e.g. *********** or ***********/24)": "{ name }格式错误(例如：*********** 或 ***********/24)", "{ name } Format Error (e.g. FE80:0:0:0:0:0:0:1 or FE80:0:0:0:0:0:0:1/10)": "{ name }格式错误(例如：FE80:0:0:0:0:0:0:1 或 FE80:0:0:0:0:0:0:1/10)", "{ size } GiB": "{ size } GiB", "{ size } KiB": "{ size } KiB", "{ size } MiB": "{ size } MiB", "{ size } TiB": "{ size } TiB", "{ size } bytes": "{ size } 字节", "{action} successfully, instance: {name}.": "{action}成功，实例名称：{name}。", "{action} successfully.": "{action}成功。", "{action} {name} successfully.": "{action}{name}成功。", "{hours} hours {leftMinutes} minutes {leftSeconds} seconds": "{hours}小时{leftMinutes}分{leftSeconds}秒", "{interval, plural, =1 {one day} other {# days} } later delete": "{interval}天后删除", "{interval, plural, =1 {one hour} other {# hours} } later delete": "{interval}小时后删除", "{interval, plural, =1 {one minute} other {# minutes} } later delete": "{interval}分钟后删除", "{interval, plural, =1 {one week} other {# weeks} } later delete": "{interval}周后删除", "{minutes} minutes {leftSeconds} seconds": "{minutes}分{leftSeconds}秒", "{name} type": "{name} 类型", "{name} type capacity": "{name} 类型容量", "{name} type capacity (GiB)": "{name} 类型容量 (GiB)", "{name} type snapshots": "{name} 类型快照", "{name} {id} could not be found.": "您查看的资源{name} {id} 无法获取", "{number} {resource}": "{number}个{resource}", "{pageSize} items/page": "{pageSize} 条/页", "Network Rate": "网卡流量速率", "Network Packets Rate": "网卡包传输速率", "Block I/O Bytes Rate": "硬盘读写速率", "Block I/O Requests Rate": "硬盘读写次数速率", "Home Page Title": "主页标题", "Maximum 30 characters allowed": "不能超过30个字符", "Login Page Title": "登录页标题", "Browser Tab Title": "浏览器标签页标题", "Login Page Logo": "登录页logo", "Supported formats: PNG, JPG, JPEG. Max file size: 5MB": "支持的图片格式: PNG, JPG, JPEG. 最大文件大小: 5MB", "Favicon": "网站Favicon", "Supported formats: ICO. Max file size: 1MB": "支持的图片格式: ICO. 最大文件大小: 1MB", "Upload Image": "上传图片", "Image uploaded successfully": "图片上传成功", "Failed to upload image": "图片上传失败", "Image must be smaller than {num}MB!": "图片必须小于{num}MB!", "Preview": "预览", "Delete Login Logo": "删除登录页logo", "Are you sure you want to delete the image?": "确定要删除该图片吗？", "Image deleted successfully": "图片删除成功", "Failed to delete image": "图片删除失败", "You can only upload image files": "只能上传指定的图片格式", "You can only upload ICO files": "只能上传ICO图片格式", "Delete Favicon": "删除Favicon", "Refresh": "看不清楚", "{seconds} seconds": "{seconds}秒"}