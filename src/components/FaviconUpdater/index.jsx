// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { Component } from 'react';
import { inject, observer } from 'mobx-react';
import ustackClient from 'client/ustack';

@inject('rootStore')
@observer
export default class FaviconUpdater extends Component {
  componentDidMount() {
    this.fetchFavicon();
  }

  fetchFavicon = async () => {
    try {
      const response = await ustackClient.get_favicon();

      const { favicon } = response || {};

      if (favicon) {
        // 如果没有 data:image 前缀，则添加默认 ico 前缀
        const faviconUrl = favicon.startsWith('data:')
          ? favicon
          : `data:image/x-icon;base64,${favicon}`;

        this.updateFavicon(faviconUrl);
      } else {
        // console.log('返回数据中没有favicon字段，使用默认favicon');
      }
    } catch (error) {
      // console.log('获取Favicon失败，使用默认favicon', error);
    }
  };

  updateFavicon = (faviconUrl) => {
    // 查找现有的favicon链接元素
    let link = document.querySelector('link[rel="shortcut icon"]');

    // 如果不存在，则创建一个新的
    if (!link) {
      link = document.createElement('link');
      link.rel = 'shortcut icon';
      document.head.appendChild(link);
    }

    // 更新favicon的href
    link.href = faviconUrl;
  };

  render() {
    // 这是一个无UI组件，不需要渲染任何内容
    return null;
  }
}
