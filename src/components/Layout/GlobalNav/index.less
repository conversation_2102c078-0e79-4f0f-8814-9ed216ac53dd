@import '~styles/variables';

.global-nav-icon {
  position: relative;
  float: left;
  width: @sider-collapsed-width;
  height: @header-height;
  color: #fff;
  font-size: 16px;
  line-height: @header-height;
  text-align: center;
  background-color: @products-icon-background;
  cursor: pointer;

  &:hover {
    background-color: @products-icon-hover-background;
  }
}

.global-nav-icon-icon {
  width: 20px;
}

.main {
  padding: 32px 32px 0;
}

.drawer-left {
  :global {
    .ant-drawer-header {
      background-color: @products-drawer-background-1;

      .ant-drawer-title {
        color: @products-header-title-color;
      }

      border-bottom-color: @products-drawer-divider-color-1;
    }

    .ant-drawer-body {
      background-color: @products-drawer-background-2;
    }
  }
}

.drawer-right {
  :global {
    .ant-drawer-body {
      background-color: @products-drawer-background-1;
    }

    .ant-drawer-close {
      color: @products-drawer-close-icon;
    }
  }
}
