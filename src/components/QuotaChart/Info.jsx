// Copyright 2022 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import React from 'react';
import { Table } from 'antd';
import { typeColors } from './Ring';

export default function QuotaInfo(props) {
  const { reserved = 0, title = '', secondTitle = t('Quota') } = props;
  const fullTitle = `${title} ${secondTitle}: ${t('Unlimit')}`;
  const columns = [
    {
      dataIndex: 'used',
      title: t('Used'),
      align: 'center',
      render: (value) => (
        <span style={{ color: typeColors.used }}>{value || '-'}</span>
      ),
    },
    {
      dataIndex: 'add',
      title: t('New'),
      align: 'center',
      render: (value) => (
        <span style={{ color: typeColors.add }}>{value || '-'}</span>
      ),
    },
  ];
  if (reserved) {
    columns.splice(1, 0, {
      dataIndex: 'reserved',
      title: t('Reserved'),
      align: 'center',
    });
  }
  return (
    <div>
      <Table
        columns={columns}
        dataSource={[props]}
        pagination={false}
        title={() => fullTitle}
        bordered
        size="small"
      />
    </div>
  );
}
