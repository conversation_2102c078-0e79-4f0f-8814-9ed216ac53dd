@import '~styles/variables';

.wrapper {
  position: relative;
  height: 100%;
  overflow: hidden;
  border-top: 1px solid #f0f0f0;

  :global {
    .ant-spin-nested-loading {
      height: 100%;
    }

    .ant-spin-container {
      height: 100%;
    }
  }
}

.step {
  height: 80px;
  margin-bottom: 16px;
  padding-top: 24px;
  padding-right: 56px;
  padding-left: 56px;
  background: #fff;
}

.form {
  height: 100%;
  min-height: calc(100vh - 280px);
  padding-top: @body-padding;
  overflow-y: auto;
  background-color: #fff;

  :global {
    .sl-form {
      height: calc(100vh - 219px);
      padding-bottom: 48px;
    }
  }
}

.footer {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 48px;
  background-color: #fff;
  box-shadow: 0 2px 30px 0 rgba(0, 0, 0, 9%);
}

.btns {
  float: right;
  min-width: 200px;
  margin-right: 32px;

  :global {
    button {
      margin-left: 8px;
    }
  }
}

.footer-left {
  display: flex;
  align-items: center;
  justify-items: left;
  float: left;
  min-height: 48px;
  margin-left: 16px;
  color: rgb(72, 72, 72);
}

.right-top-extra-wrapper {
  position: absolute;
  top: 95px;
  right: 30px;
  z-index: 100;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 30px 0 rgba(0, 0, 0, 20%);

  :global {
    .ant-card-head {
      min-width: 32px;

      .ant-card-extra {
        padding: 8px 0;
      }
    }
  }
}
