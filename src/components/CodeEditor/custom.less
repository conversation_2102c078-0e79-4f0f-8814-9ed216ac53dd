/* stylelint-disable selector-class-pattern */
.ace_editor {
  font-family: Monaco, Menlo, Consolas, 'Courier New', monospace;
  line-height: 20px !important;
  -webkit-font-smoothing: auto;
}

.ace_editor.ace-chaos {
  color: #fff;
  background-color: #242e42;
}

.ace_editor.ace-chaos .ace_gutter {
  color: #537f7e;
  background-color: #242e42;
  border-right: 1px solid #4a5974;
}

.ace_editor.ace-chaos .ace_variable,
.ace_editor.ace-chaos .ace_identifier,
.ace_editor.ace-chaos .ace_meta.ace_tag {
  color: #75e0f2;
}

.ace_editor.ace-chaos .ace_keyword {
  color: #fff;
}

.ace_editor.ace-chaos .ace_string {
  color: #ebe087;
}

.ace_editor.ace-chaos .ace_constant.ace_numeric {
  color: #bd99ff;
}

.ace_editor.ace-chaos .ace_marker-layer .ace_active-line {
  background-color: #36435c;
}

.ace_editor.ace-chaos .ace_indent-guide {
  padding: 2px 0;
  border-right: 1px dotted #777;
}

.ace_editor.ace-chaos .ace_marker-layer .ace_selection {
  background-color: #4a5974;
}

.ace_editor.ace-chaos .ace_comment {
  color: #aaa;
}

.ace_editor.ace-chaos .ace_fold:hover {
  background-color: #fff;
}

.ace_editor.ace-chaos .ace_line .ace_fold {
  height: auto;
}
