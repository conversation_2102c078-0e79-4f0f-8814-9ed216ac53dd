// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { Typography, Image } from 'antd';
import cloudLogo from 'asset/image/cloud-logo.png';
import styles from './index.less';

@inject('rootStore')
@observer
export default class HomePageTitle extends Component {
  get rootStore() {
    return this.props.rootStore;
  }

  get info() {
    const { info = {} } = this.rootStore;
    return info || {};
  }

  get homePageTitle() {
    const { home_page_title } = this.info;
    return home_page_title;
  }

  render() {
    const { homePageTitle } = this;

    if (homePageTitle) {
      return (
        <div className={styles.titleContainer}>
          <Typography.Title
            level={4}
            className={`${styles.title} ${styles.title_black}`}
          >
            {homePageTitle}
          </Typography.Title>
        </div>
      );
    }

    return (
      <div className={styles.logoContainer}>
        <Image
          src={cloudLogo}
          alt="Cloud Logo"
          preview={false}
          className={styles.logo}
        />
      </div>
    );
  }
}
