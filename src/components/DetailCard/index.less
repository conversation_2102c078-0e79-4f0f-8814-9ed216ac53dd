@import '~styles/variables';

@min-space: 8px;
@mid-space: 16px;
@lg-space: 24px;
@box-shadow: 0 0 10px 0 rgba(0, 0, 0, 5%);
@topo-line: 1px solid #d2d2d2;
@resource-box-bg: #fbfdff;
@resource-box-border: 1px solid #cfe1ff;

.card {
  flex: 1;
  margin-bottom: @mid-space;
  background-color: #fff;
  border-radius: @border-radius;
  box-shadow: @box-shadow;

  .card-content {
    padding: @mid-space;

    .card-item {
      margin-bottom: @min-space;

      h3 {
        margin-bottom: 0;
      }

      .title-help {
        margin-left: @min-space;
        line-height: 26px;
      }

      div {
        word-break: break-all;
      }
    }

    :last-child {
      margin-bottom: 0;
    }

    :global {
      .ant-typography {
        word-break: break-all;
      }
    }
  }
}
