/* IframeContainer 样式 */
.container {
  width: 100%;
  height: calc(100vh - 64px); /* 减去header高度 */
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;

  iframe {
    position: absolute;
    top: -70px;
    left: 0;
    width: 100%;
    height: calc(100% + 70px);
    border: none;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

/* 加载状态样式 */
.loadingContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #666;
}

.loadingContainer p {
  margin-top: 16px;
  font-size: 14px;
}

/* 错误状态样式 */
.errorContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
  text-align: center;
  color: #666;
}

.errorContainer h3 {
  color: #ff4d4f;
  margin-bottom: 16px;
  font-size: 18px;
}

.errorContainer p {
  margin-bottom: 24px;
  font-size: 14px;
  line-height: 1.5;
  max-width: 400px;
}

.retryButton {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: background-color 0.3s ease;
}

.retryButton:hover {
  background-color: #40a9ff;
}

.retryButton:active {
  background-color: #096dd9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    height: calc(100vh - 48px); /* 移动端header高度较小 */
  }

  .errorContainer {
    padding: 16px;
  }

  .errorContainer h3 {
    font-size: 16px;
  }

  .errorContainer p {
    font-size: 12px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .container {
    border: 2px solid #000;
  }

  .errorContainer h3 {
    color: #d32f2f;
  }

  .retryButton {
    border: 2px solid #1890ff;
  }
}
