/**
 * 通用Iframe容器组件
 * 用于嵌入外部系统页面，支持完善的错误处理、性能监控和用户交互
 */
import React, { useEffect, useState, useCallback } from 'react';
import { notification, Spin } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import cookieStore from 'stores/ustack/cookies';
import { statusMap } from 'src/utils/code';
import {
  clearLocalStorage,
  getLocalStorageItem,
  setLocalStorageItem,
} from 'src/utils/local-storage';
import styles from './index.less';

/**
 * IframeContainer组件属性
 * @typedef {Object} IframeContainerProps
 * @property {string} pageType - 页面类型，用于生成唯一的iframe ID
 * @property {string} urlPath - URL路径，用于构建完整的URL
 * @property {string} [title] - 页面标题，用于显示和无障碍访问
 * @property {string} [containerClass] - 容器的额外CSS类名
 * @property {Function} [onLoad] - iframe加载完成的回调函数
 * @property {Function} [onError] - iframe加载错误的回调函数
 */

const IframeContainer = ({
  pageType,
  urlPath,
  title = '外部系统',
  containerClass = '',
  onLoad,
  onError,
}) => {
  // 状态管理
  const [ustackToken, setUstackToken] = useState(
    getLocalStorageItem('ustack_token')
  );
  const [ustackUrl, setUstackUrl] = useState(getLocalStorageItem('ustack_url'));
  const [isFirstLoad, setIsFirstLoad] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // 生成唯一的iframe ID和容器ID
  const iframeId = `${pageType}Frame`;
  const containerId = `${pageType}-container`;

  /**
   * 记录日志的统一方法
   * @param {string} level - 日志级别 (info, warn, error, debug)
   * @param {string} message - 日志消息
   * @param {Object} data - 附加数据
   */
  const log = useCallback(
    (level, message, data = {}) => {
      const logData = {
        component: 'IframeContainer',
        pageType,
        timestamp: new Date().toISOString(),
        ...data,
      };

      console[level](`[${title}] ${message}`, logData);
    },
    [pageType, title]
  );

  /**
   * 显示错误通知
   * @param {string} message - 错误消息
   * @param {Error} error - 错误对象
   */
  const showErrorNotification = useCallback(
    (message, errorObj = null) => {
      log('error', '显示错误通知', { message, error: errorObj?.message });

      let errorMessage = message;
      if (errorObj && errorObj.response) {
        errorMessage =
          errorObj.response.data?.detail ||
          statusMap[errorObj.response.status] ||
          message;
      }

      notification.error({
        message: title,
        description: errorMessage,
        duration: 5,
      });

      setError(errorMessage);
      onError && onError(errorObj || new Error(errorMessage));
    },
    [title, onError, log]
  );

  /**
   * 创建并配置iframe元素
   * @param {string} src - iframe的src地址
   * @returns {HTMLIFrameElement} 配置好的iframe元素
   */
  const createIframe = useCallback(
    (src) => {
      log('debug', '创建iframe元素', { src });

      const iframe = document.createElement('iframe');
      iframe.id = iframeId;
      iframe.src = src;
      iframe.style.width = '100%';
      iframe.style.height = '100%';
      iframe.style.border = 'none';
      iframe.style.overflow = 'hidden';

      // 设置iframe的安全属性
      iframe.setAttribute(
        'sandbox',
        'allow-scripts allow-same-origin allow-forms allow-top-navigation'
      );
      iframe.setAttribute('title', title);

      return iframe;
    },
    [iframeId, title, log]
  );

  /**
   * 处理iframe加载完成事件
   * @param {string} token - 认证token
   * @param {string} url - 目标URL
   */
  const handleIframeLoad = useCallback(
    (token, url) => {
      log('debug', 'iframe加载完成', { hasToken: !!token, url });

      try {
        // 向iframe发送认证信息
        const targetFrame = document.getElementById(iframeId);
        if (targetFrame && targetFrame.contentWindow) {
          const messageData = {
            page: pageType,
            token,
            timestamp: Date.now(),
          };

          targetFrame.contentWindow.postMessage(messageData, url);
          log('info', '向iframe发送认证信息成功', messageData);

          onLoad && onLoad(targetFrame);
        } else {
          log('warn', '未找到iframe元素或contentWindow不可用');
        }
      } catch (err) {
        log('error', '向iframe发送消息失败', { error: err.message });
      }
    },
    [iframeId, pageType, onLoad, log]
  );

  /**
   * 使用现有token加载iframe
   * @param {HTMLIFrameElement} iframe - iframe元素
   * @param {string} token - 认证token
   * @param {string} url - 目标URL
   */
  const loadWithExistingToken = useCallback(
    (iframe, token, url) => {
      const startTime = Date.now();
      log('info', '使用现有token加载iframe', { hasToken: !!token, url });

      try {
        const fullUrl = `${url}${urlPath}?token=${token}&page=${urlPath}`;
        iframe.src = fullUrl;

        // 监听iframe加载完成
        iframe.onload = () => {
          const loadTime = Date.now() - startTime;
          log('info', 'iframe加载完成', { url: fullUrl, loadTime });
          handleIframeLoad(token, url);
        };

        // 监听iframe加载错误
        iframe.onerror = (err) => {
          const loadTime = Date.now() - startTime;
          log('error', 'iframe加载失败', {
            url: fullUrl,
            loadTime,
            error: err.message,
          });
          showErrorNotification(`${title}页面加载失败`, err);
        };

        const container = document.getElementById(containerId);
        if (container) {
          container.appendChild(iframe);
          log('debug', 'iframe已添加到容器中');
        } else {
          log('error', '未找到页面容器元素');
          showErrorNotification('页面容器初始化失败');
        }
      } catch (err) {
        log('error', '加载iframe时发生异常', { error: err.message });
        showErrorNotification(`加载${title}页面失败`, err);
      }
    },
    [urlPath, containerId, title, handleIframeLoad, showErrorNotification, log]
  );

  /**
   * 获取新的token并加载iframe
   * @param {HTMLIFrameElement} iframe - iframe元素
   */
  const loadWithNewToken = useCallback(
    async (iframe) => {
      if (isLoading) {
        log('warn', '正在加载中，跳过重复请求');
        return;
      }

      const startTime = Date.now();
      setIsLoading(true);
      setError(null);

      log('info', '开始获取新的认证token');

      try {
        const tokenResponse = await cookieStore.getUStackToken();
        const { token, url, expire } = tokenResponse;

        const tokenTime = Date.now() - startTime;
        log('info', '获取token完成', {
          hasToken: !!token,
          url,
          expire,
          tokenTime,
        });

        // 更新状态和本地存储
        setUstackToken(token);
        setUstackUrl(url);
        setLocalStorageItem('ustack_token', token, null, expire);
        setLocalStorageItem('ustack_url', url, null, expire);

        // 构建完整URL
        const fullUrl = `${url}${urlPath}?token=${token}&page=${urlPath}`;
        iframe.src = fullUrl;

        // 监听iframe加载完成
        iframe.onload = () => {
          const totalTime = Date.now() - startTime;
          log('info', '新token加载iframe完成', { url: fullUrl, totalTime });
          handleIframeLoad(token, url);
        };

        // 监听iframe加载错误
        iframe.onerror = (err) => {
          const totalTime = Date.now() - startTime;
          log('error', '新token加载iframe失败', {
            url: fullUrl,
            totalTime,
            error: err.message,
          });
          showErrorNotification(`${title}页面加载失败`, err);
        };

        // 首次加载时添加到容器
        if (isFirstLoad) {
          const container = document.getElementById(containerId);
          if (container) {
            container.appendChild(iframe);
            log('debug', '首次加载：iframe已添加到容器中');
          } else {
            log('error', '首次加载：未找到页面容器元素');
            showErrorNotification('页面容器初始化失败');
          }
          setIsFirstLoad(false);
        }
      } catch (err) {
        const errorTime = Date.now() - startTime;
        log('error', '获取认证令牌失败', { error: err.message, errorTime });
        showErrorNotification('获取认证令牌失败，请重新登录', err);
      } finally {
        setIsLoading(false);
      }
    },
    [
      isLoading,
      isFirstLoad,
      urlPath,
      containerId,
      title,
      handleIframeLoad,
      showErrorNotification,
      log,
    ]
  );

  /**
   * 加载页面
   * @param {boolean} forceRefresh - 是否强制刷新token
   */
  const loadPage = useCallback(
    async (forceRefresh = false) => {
      const startTime = Date.now();
      log('info', '开始加载页面', { forceRefresh });

      try {
        const iframe = createIframe('about:blank');

        // 如果有有效的token且不强制刷新，则直接使用
        if (ustackToken && ustackUrl && !forceRefresh) {
          log('info', '使用现有token加载页面');
          loadWithExistingToken(iframe, ustackToken, ustackUrl);
        } else {
          // 获取新的token
          log('info', '获取新token并加载页面');
          await loadWithNewToken(iframe);
        }

        const totalTime = Date.now() - startTime;
        log('info', '页面加载流程完成', { totalTime, forceRefresh });
      } catch (err) {
        const errorTime = Date.now() - startTime;
        log('error', '初始化页面失败', {
          error: err.message,
          forceRefresh,
          errorTime,
        });
        showErrorNotification(`初始化${title}页面失败`, err);
      }
    },
    [
      ustackToken,
      ustackUrl,
      createIframe,
      loadWithExistingToken,
      loadWithNewToken,
      title,
      showErrorNotification,
      log,
    ]
  );

  /**
   * 处理来自iframe的消息
   */
  const handleMessage = useCallback(
    (event) => {
      log('debug', '收到iframe消息', {
        origin: event.origin,
        data: event.data,
      });

      try {
        // 验证消息来源
        if (!ustackUrl || !event.origin.includes(new URL(ustackUrl).hostname)) {
          log('warn', '忽略来自未知来源的消息', {
            origin: event.origin,
            expectedOrigin: ustackUrl,
          });
          return;
        }

        // 处理不同类型的消息
        if (event.data === 'clear') {
          log('info', '清除本地存储并重新加载');
          clearLocalStorage(['keystone_token', 'loglevel:webpack-dev-server']);
          setUstackToken('');
          setUstackUrl('');
          loadPage(true);
        } else if (event.data === 'refresh') {
          log('info', '刷新页面');
          loadPage(true);
        } else if (event.data && event.data.type === 'error') {
          log('error', 'iframe内部错误', { eventData: event.data });
          showErrorNotification(event.data.message || `${title}系统发生错误`);
        } else if (event.data && event.data.type === 'performance') {
          log('info', `iframe性能数据: ${event.data.action}`, {
            duration: event.data.duration,
            data: event.data.data,
          });
        } else {
          log('debug', '收到未处理的消息类型', { eventData: event.data });
        }
      } catch (err) {
        log('error', '处理iframe消息失败', {
          error: err.message,
          eventData: event.data,
        });
      }
    },
    [ustackUrl, loadPage, title, showErrorNotification, log]
  );

  // 组件初始化
  useEffect(() => {
    log('info', '组件初始化开始');

    // 加载页面
    loadPage();

    // 监听iframe消息
    window.addEventListener('message', handleMessage);

    // 清理函数
    return () => {
      log('info', '组件卸载，清理事件监听器');
      window.removeEventListener('message', handleMessage);
    };
  }, [loadPage, handleMessage, log]);

  // 重试处理
  const handleRetry = useCallback(() => {
    log('info', '用户点击重试按钮');
    setError(null);
    loadPage(true);
  }, [loadPage, log]);

  // 错误状态显示
  if (error) {
    return (
      <div id={containerId} className={`${styles.container} ${containerClass}`}>
        <div className={styles.errorContainer}>
          <h3>{title}暂时无法访问</h3>
          <p>{error}</p>
          <button
            type="button"
            onClick={handleRetry}
            className={styles.retryButton}
          >
            <ReloadOutlined /> 重试
          </button>
        </div>
      </div>
    );
  }

  // 加载状态显示
  if (isLoading) {
    return (
      <div id={containerId} className={`${styles.container} ${containerClass}`}>
        <div className={styles.loadingContainer}>
          <Spin size="large" />
          <p>正在加载{title}...</p>
        </div>
      </div>
    );
  }

  return (
    <div
      id={containerId}
      className={`${styles.container} ${containerClass}`}
      role="main"
      aria-label={title}
    />
  );
};

export default IframeContainer;
