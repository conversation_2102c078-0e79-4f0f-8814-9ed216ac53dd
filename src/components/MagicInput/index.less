@import '~styles/variables';

.magic-input-outer-wrapper {
  display: flex;
  align-items: center;
}

.magic-input-wrapper {
  position: relative;
  align-items: center;
  width: 100%;
  min-width: 200px;
  padding: 3px 0 3px 8px;
  background-color: @table-header-search-input-background;
  border: 1px solid @table-header-search-input-border-color;
  border-radius: @border-radius;

  :global {
    .ant-tag {
      height: 24px;
      margin-right: 4px;
      margin-bottom: 1px;
      padding: 0 4px;
      color: #fff;
      font-size: @table-header-search-input-font-size;
      line-height: 24px;
      background-color: @primary-color;
      border: none;

      .anticon-close {
        margin-left: 16px;
        color: #fff;
      }
    }

    .ant-btn {
      margin-top: -3px;
      margin-right: 0;
    }

    .ant-btn-icon-only {
      background-color: #fff;
    }
  }
}

.magic-input-wrapper-active {
  border-color: @primary-color;
  box-shadow: 0 0 0 2px rgba(0, 104, 255, 20%);
}

.input-wrapper {
  position: relative;
  flex-grow: 1;
  height: 24px;
  line-height: 24px;

  :global {
    .ant-input {
      padding: 0;
      background: none;
      border: none;
      box-shadow: none;
    }

    .ant-input:focus {
      border: none;
      box-shadow: none;
    }

    input::placeholder {
      font-size: @table-header-search-input-font-size;
    }

    .ant-menu-vertical > .ant-menu-item {
      height: 26px;
      line-height: 26px;
    }

    .ant-menu-vertical .ant-menu-item {
      margin-top: 0;
      margin-bottom: 0;
      font-size: @table-header-search-input-font-size;
    }
  }
}

.menu {
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 9%) !important;
}

.menu,
.option-menu {
  position: absolute;
  top: 34px;
  z-index: 100;
  max-height: 310px;
  overflow-x: hidden;
  overflow-y: auto;
  border-radius: @border-radius;
}

.key {
  font-size: @table-header-search-input-font-size;
  line-height: 24px;

  :global {
    .ant-divider,
    .ant-divider-vertical {
      margin-right: 4px;
      margin-left: 4px;
    }
  }
}

:global {
  .ant-form-item-has-error .magic-input-wrapper .ant-input,
  .ant-form-item-has-error .magic-input-wrapper .ant-input:focus,
  .ant-form-item-has-error
    .magic-input-wrapper
    .ant-input:not([disabled]):hover {
    background: none;
    border: none;
    box-shadow: none;
  }

  .magic-input-wrapper .ant-tag {
    display: inline-block;
    height: 24px;
    margin: 0 5px 0 0;
    margin-bottom: 1px;
    line-height: 24px;
  }
}

.search-icon {
  position: absolute;
  top: 0;
  right: 8px;
  color: @color-text-body;
  font-size: 12px;
  line-height: 32px;
}

.search-icon-hidden {
  display: none;
}

.close-btn-col {
  position: absolute;
  top: 0;
  right: 0;
  height: 30px;
  line-height: 30px;

  .close-btn {
    height: 28px !important;
    padding: 0;
    background-color: @table-header-default-button-background;
    border: none;
  }
}

.close-option-btn {
  top: 3px;
  height: 24px !important;
}

.magic-input-checks {
  margin-left: 8px;
  line-height: 32px;

  :global {
    .ant-checkbox + span {
      text-wrap: nowrap;
    }
  }
}
