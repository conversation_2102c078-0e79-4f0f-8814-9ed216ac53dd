@import '~styles/variables';

.network-select {
  position: relative;
  display: block;
  height: 61.6px;
  margin-bottom: 0 !important;

  :global {
    .ant-form-item-control-input-content {
      height: 61.6px;
    }

    .ant-form-item-explain {
      position: absolute;
      bottom: 0;
    }
  }
}

.select {
  margin-right: 40px;
}

.size-label {
  margin-right: 40px;
  margin-left: 10px;
}

.tips {
  margin-top: 0;
}

.label {
  margin-right: 10px;
  color: @color-text-caption;
  line-height: 30px;
}

.content {
  color: @color-text-body;
}

.subnet-options-cidr {
  margin-left: 5px;
  padding-left: 5px;
  border-left: 1px solid;
  opacity: 0.6;
}
