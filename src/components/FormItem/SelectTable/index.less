@import '~styles/variables';

.search-wrapper {
  margin-bottom: 16px;

  :global {
    .ant-menu-root.ant-menu-vertical {
      box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 20%) !important;
    }
  }
}

.image-tabs {
  display: flex;
  height: 52px;
  margin-bottom: 8px;

  img {
    display: block;
    width: 20px;
    height: 20px;
    margin: 0 auto;
  }

  :global {
    .ant-radio-button-wrapper {
      width: 70px;
      height: 50px;
      overflow: hidden;
      color: @color-text-body;
      text-align: center;
      border: none;
    }

    .ant-radio-button-wrapper:not(:first-child)::before {
      width: 0;
    }

    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      color: @primary-color;
      font-weight: bold;
    }

    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
}

.image-tab {
  flex: 1 auto;
}

.image-tab-label {
  font-size: 10px;
}

.normal-tabs {
  margin-bottom: 8px;
}

.pagination-footer {
  :global {
    .ant-select {
      width: unset;
      min-width: 80px;
    }
  }
}

.sl-select-table-backend {
  :global {
    .ant-table-footer {
      padding: 8px 16px;
      background: #fff;
    }
  }
}

.search-line {
  display: flex;
  gap: 8px;

  :global {
    .ant-btn-default {
      color: @table-header-default-button-color;
      background-color: @table-header-default-button-background-gray;
      border-color: @table-header-default-button-border;
    }

    .ant-btn-default:hover {
      color: @primary-color;
      border-color: @primary-color;
    }
  }
}
