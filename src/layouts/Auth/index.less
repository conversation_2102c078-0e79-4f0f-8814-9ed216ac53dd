.viewport {
  height: 100vh;
  padding-top: 40px;
  overflow-y: auto;
  background-color: #f8f9fc;

  .bg {
    width: 1200px;
    margin: auto;

    .top {
      position: relative;
      text-align: center;

      .header {
        height: 56px;
        line-height: 56px;
        text-align: left;

        a {
          text-decoration: none;
        }

        .logo {
          //width: 87px;
          height: 50px;
          margin-right: 16px;
          vertical-align: top;
        }
      }
    }

    .container {
      display: flex;
      flex-flow: row;
      height: 680px;
      margin-top: 40px;
      // height: 100vh;
      overflow: hidden;
      background-color: #fff;
      box-shadow: 0 0 20px 10px #0918430d;

      .left {
        position: relative;
        width: 576px;
        overflow-y: auto;

        /* TODO wait for RGB
              // background: rgb(253, 249, 252) no-repeat fixed left bottom;
              // background-size: 556px; */

        .lang {
          position: absolute;
          top: 20px;
          right: 24px;
          width: 20px;
          height: 20px;
          text-align: right;
        }

        .main {
          margin-top: 20vh;
          padding: 0 130px;
        }
      }

      .right {
        position: relative;
        flex: 1 1;
        text-align: center;
        background-color: #fff;
      }
    }
  }
}

.login-image {
  margin-top: calc(100vh - 668px);
}

.login-full-image {
  width: 100%;
  height: 100%;
}

.login-right-logo {
  position: relative;
  top: -100px;
}
