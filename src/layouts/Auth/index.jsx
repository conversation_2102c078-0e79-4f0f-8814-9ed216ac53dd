// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import renderRoutes from 'utils/RouterConfig';
import SelectLang from 'components/SelectLang';
import ustackClient from 'client/ustack';

import logo from 'asset/image/logo.png';
import loginFullImage from 'asset/image/login-full.svg';
import styles from './index.less';

export class AuthLayout extends Component {
  constructor(props) {
    super(props);
    this.state = {
      customLogo: null,
    };
    this.routes = props.route.routes;
  }

  componentDidMount() {
    this.fetchLoginPageLogo();
  }

  fetchLoginPageLogo = async () => {
    try {
      const response = await ustackClient.get_login_page_logo();

      const { logo: logoBase64 } = response || {};

      if (logoBase64) {
        // 如果没有 data:image 前缀，则添加默认 png 前缀
        const logoUrl = logoBase64.startsWith('data:')
          ? logoBase64
          : `data:image/png;base64,${logoBase64}`;
        this.setState({ customLogo: logoUrl });
      } else {
        // console.log('返回数据中没有logo字段或为空，使用默认logo');
      }
    } catch (error) {
      // console.log('获取Logo失败，使用默认logo', error);
    }
  };

  renderRight() {
    return (
      <div className={styles.right}>
        <img
          alt=""
          className={styles['login-full-image']}
          src={loginFullImage}
        />
      </div>
    );
  }

  render() {
    const { customLogo } = this.state;
    return (
      <div className={styles.viewport}>
        <div className={styles.bg}>
          <div className={styles.top}>
            <div className={styles.header}>
              <img
                alt="logo"
                className={styles.logo}
                src={customLogo || logo}
              />
            </div>
          </div>
          <div className={styles.container}>
            {this.renderRight()}
            <div className={styles.left}>
              <div className={styles.lang}>
                <SelectLang />
              </div>
              <div className={styles.main}>{renderRoutes(this.routes)}</div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default inject('rootStore')(observer(AuthLayout));
