const { isObject } = require('lodash');
const { getServerConfig } = require('./utils');

const { server, port, host } = getServerConfig();

const getProxyByMap = (apiMap) => {
  const result = {};
  Object.keys(apiMap).forEach((key) => {
    const value = apiMap[key];
    const base = isObject(value) ? value : { target: value };
    result[key] = {
      ...base,
      changeOrigin: true,
      pathRewrite: {'/api/openstack/skyline/api': '/api'},
      secure: false,
      headers: {
        Connection: 'keep-alive',
      },
    };
  });
  return result;
};

const apiMap = {
  '/api/openstack/regionone': 'http://***********:10087/',
  '/api/': server,
};

// eslint-disable-next-line no-console
console.log('apiMap', apiMap);
const proxy = getProxyByMap(apiMap);

module.exports = { proxy, host, port };
